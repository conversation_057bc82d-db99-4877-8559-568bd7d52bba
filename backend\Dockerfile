FROM node:20-alpine

# Install dependencies for Nuclei
RUN apk add --no-cache curl unzip

# Install Nuclei using go install (more reliable)
RUN apk add --no-cache go git && \
    go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest && \
    mv /root/go/bin/nuclei /usr/local/bin/ && \
    chmod +x /usr/local/bin/nuclei && \
    apk del go git

WORKDIR /app

# Copy shared tsconfig and lib from project root
COPY tsconfig.base.json /tsconfig.base.json
COPY lib /app/lib

# Copy package files first for better layer caching
# These paths are now relative to the build context (project root)
COPY backend/package.json backend/package-lock.json* ./

# Install dependencies
# Ensure npm ci is used if package-lock.json is present, otherwise npm install
RUN if [ -f package-lock.json ]; then npm ci; else npm install; fi

# Copy the rest of the backend application code
# This path is now relative to the build context (project root)
COPY backend/ /app/

# Build the TypeScript code
RUN npm run build && echo '--- Listing /app/dist contents after successful build ---' && ls -R /app/dist

# Download Nuclei templates
RUN mkdir -p /app/nuclei-templates && \
    nuclei -update-templates -templates-directory /app/nuclei-templates || true

# Expose the application port
EXPOSE 3001

# Run the application
CMD ["npm", "run", "start"]