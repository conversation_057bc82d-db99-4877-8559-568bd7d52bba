/**
 * Test file for HIPAA Security Scanner - Part 2 Implementation
 * This file demonstrates the usage of the security scanner services
 * and can be used for testing the implementation.
 */

import { HipaaSecurityScanner, ScannerConfigService, SSLAnalyzer, ContentAnalyzer } from '../index';
import { NucleiClient } from './nuclei-client';

export class SecurityScannerTester {
  private configService: ScannerConfigService;

  constructor() {
    this.configService = ScannerConfigService.getInstance();
  }

  /**
   * Test the configuration service
   */
  async testConfiguration(): Promise<void> {
    console.log('🔧 Testing Configuration Service...');

    // Get current configuration
    const config = this.configService.getScannerConfig();
    console.log('Scanner Configuration:', {
      nucleiPath: config.nucleiPath,
      nucleiTemplatesPath: config.nucleiTemplatesPath,
      maxConcurrentRequests: config.maxConcurrentRequests,
      requestTimeout: config.requestTimeout,
    });

    // Validate configuration
    const validation = this.configService.validateConfiguration();
    console.log('Configuration Validation:', validation);

    // Get environment info
    const envInfo = this.configService.getEnvironmentInfo();
    console.log('Environment Variables:', envInfo);

    // Test connectivity (if ZAP is running)
    try {
      const connectivity = await this.configService.testConnectivity();
      console.log('Connectivity Test:', connectivity);
    } catch (error) {
      console.log(
        'Connectivity Test Failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Test individual services
   */
  async testIndividualServices(): Promise<void> {
    console.log('🔍 Testing Individual Services...');

    // Test SSL Analyzer
    await this.testSSLAnalyzer();

    // Test Content Analyzer
    await this.testContentAnalyzer();

    // Test Nuclei Client (if available)
    await this.testNucleiClient();
  }

  private async testSSLAnalyzer(): Promise<void> {
    console.log('🔒 Testing SSL Analyzer...');

    const sslAnalyzer = new SSLAnalyzer();

    try {
      // Test with a known good SSL site
      const result = await sslAnalyzer.analyzeDomain('google.com', 443);
      console.log('SSL Analysis Result:', {
        isValid: result.isValid,
        daysRemaining: result.daysRemaining,
        tlsVersion: result.tlsVersion,
        hipaaCompliant: result.hipaaCompliant,
        grade: result.grade,
        vulnerabilityCount: result.vulnerabilities.length,
      });
    } catch (error) {
      console.log('SSL Analysis Failed:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private async testContentAnalyzer(): Promise<void> {
    console.log('📄 Testing Content Analyzer...');

    const contentAnalyzer = new ContentAnalyzer();

    // Test with sample HTML content
    const sampleHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test Page</title>
      </head>
      <body>
        <h1>Medical Records</h1>
        <p>Patient ID: 12345</p>
        <p>SSN: ***********</p>
        <form action="http://example.com/submit" method="POST">
          <input type="text" name="patient_name" />
          <input type="password" name="password" />
          <input type="submit" value="Submit" />
        </form>
        <script src="http://example.com/script.js"></script>
        <script>
          document.write('Unsafe script');
        </script>
      </body>
      </html>
    `;

    const sampleHeaders = {
      'content-type': 'text/html',
      'x-frame-options': 'DENY',
      'set-cookie': 'sessionid=abc123; Path=/; HttpOnly',
    };

    const result = contentAnalyzer.analyzeContent(sampleHtml, 'http://example.com', sampleHeaders);

    console.log('Content Analysis Result:', {
      hasEPHI: result.hasEPHI,
      ephiMatchCount: result.ephiMatches.length,
      securityHeaderCount: result.securityHeaders.length,
      formCount: result.formAnalysis.length,
      scriptIssues: result.scriptAnalysis.securityIssues.length,
      cookieCount: result.cookieAnalysis.length,
    });

    // Show ePHI matches
    if (result.ephiMatches.length > 0) {
      console.log('ePHI Matches Found:');
      result.ephiMatches.forEach((match) => {
        console.log(`  - ${match.match} (Risk: ${match.riskLevel}) at ${match.location}`);
      });
    }

    // Show security issues
    const allIssues = [
      ...result.formAnalysis.flatMap((f) => f.securityIssues),
      ...result.scriptAnalysis.securityIssues,
      ...result.cookieAnalysis.flatMap((c) => c.securityIssues),
    ];

    if (allIssues.length > 0) {
      console.log('Security Issues Found:');
      allIssues.forEach((issue) => {
        console.log(`  - ${issue}`);
      });
    }
  }

  private async testNucleiClient(): Promise<void> {
    console.log('🔍 Testing Nuclei Client...');

    const nucleiClient = new NucleiClient();

    try {
      // Test if Nuclei is available
      const isAvailable = await nucleiClient.isAvailable();
      console.log('Nuclei Availability:', isAvailable);

      if (isAvailable) {
        // Test template update
        console.log('Updating Nuclei templates...');
        const templatesUpdated = await nucleiClient.updateTemplates();
        console.log('Templates Updated:', templatesUpdated);

        // Test a quick scan
        console.log('Running test scan...');
        const vulnerabilities = await nucleiClient.scanForHipaaVulnerabilities({
          targetUrl: 'https://example.com',
          timeout: 15000,
          tags: ['ssl', 'headers'],
          severity: ['critical', 'high'],
        });

        console.log('Nuclei Test Scan Result:', {
          vulnerabilityCount: vulnerabilities.length,
          sampleFindings: vulnerabilities.slice(0, 2).map((v) => ({
            type: v.type,
            severity: v.severity,
            location: v.location,
          })),
        });
      }
    } catch (error) {
      console.log(
        'Nuclei Client Test Failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      console.log('Note: This is expected if Nuclei is not installed');
    }
  }

  // ZAP client removed - using Nuclei for security scanning

  /**
   * Test the complete security scanner
   */
  async testCompleteScan(targetUrl: string = 'https://example.com'): Promise<void> {
    console.log(`🚀 Testing Complete Security Scan for: ${targetUrl}`);

    const config = this.configService.getScannerConfig();
    const scanner = new HipaaSecurityScanner(config);

    // Get recommended scan configuration
    const { scanConfig, recommendations } = this.configService.getRecommendedSettings(targetUrl);

    console.log('Scan Recommendations:');
    recommendations.forEach((rec) => console.log(`  - ${rec}`));

    // Nuclei scanning is now the default vulnerability scanner

    try {
      const result = await scanner.performSecurityScan(scanConfig);

      console.log('Scan Results Summary:', {
        scanId: result.scanId,
        overallScore: result.overallScore,
        riskLevel: result.riskLevel,
        scanDuration: `${result.scanDuration}ms`,
        passedTests: result.passedTests.length,
        failedTests: result.failedTests.length,
        vulnerabilities: result.vulnerabilities.length,
        pagesScanned: result.pagesScanned.length,
        toolsUsed: result.toolsUsed,
        scanStatus: result.scanStatus,
      });

      // Show category breakdown
      console.log('Category Breakdown:');
      console.log(
        `  Technical Safeguards: ${result.technicalSafeguards.score}% (${result.technicalSafeguards.passedTests}/${result.technicalSafeguards.totalTests})`,
      );
      console.log(
        `  Administrative Safeguards: ${result.administrativeSafeguards.score}% (${result.administrativeSafeguards.passedTests}/${result.administrativeSafeguards.totalTests})`,
      );
      console.log(
        `  Organizational Safeguards: ${result.organizationalSafeguards.score}% (${result.organizationalSafeguards.passedTests}/${result.organizationalSafeguards.totalTests})`,
      );
      console.log(
        `  Physical Safeguards: ${result.physicalSafeguards.score}% (${result.physicalSafeguards.passedTests}/${result.physicalSafeguards.totalTests})`,
      );

      // Show failed tests
      if (result.failedTests.length > 0) {
        console.log('Failed Tests:');
        result.failedTests.forEach((test) => {
          console.log(`  - ${test.testName} (${test.riskLevel}): ${test.failureReason}`);
        });
      }

      // Show vulnerabilities
      if (result.vulnerabilities.length > 0) {
        console.log('Vulnerabilities:');
        result.vulnerabilities.forEach((vuln) => {
          console.log(`  - ${vuln.type} (${vuln.severity}): ${vuln.description}`);
        });
      }

      await scanner.cleanup();
    } catch (error) {
      console.log(
        'Complete Scan Failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting HIPAA Security Scanner Tests...\n');

    try {
      await this.testConfiguration();
      console.log('\n');

      await this.testIndividualServices();
      console.log('\n');

      await this.testCompleteScan();
      console.log('\n');

      console.log('✅ All tests completed successfully!');
    } catch (error) {
      console.error('❌ Test execution failed:', error);
    }
  }
}

// Export for use in other files
export default SecurityScannerTester;

// If running this file directly
if (require.main === module) {
  const tester = new SecurityScannerTester();
  tester.runAllTests().catch(console.error);
}
