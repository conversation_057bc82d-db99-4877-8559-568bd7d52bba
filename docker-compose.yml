services:
  postgres:
    image: postgres:16-alpine
    container_name: comply_checker_postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-complyuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-complypassword}
      POSTGRES_DB: ${POSTGRES_DB:-complychecker_dev}
    command: postgres -c password_encryption=md5
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d # Added this line
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-complyuser} -d ${POSTGRES_DB:-complychecker_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5

  keycloak:
    image: quay.io/keycloak/keycloak:24.0.4 # Using a recent version
    container_name: comply_checker_keycloak
    restart: unless-stopped
    ports:
      - "${KEYCLOAK_PORT:-8080}:8080"
    environment:
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER:-admin}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin}
      KC_DB: postgres
      # Use a different database name for Keycloak, e.g., 'keycloak_db'
      # The 'postgres' service (your app's DB container) will host this too.
      KC_DB_URL_HOST: postgres # This refers to the service name 'postgres'
      KC_DB_URL_DATABASE: ${POSTGRES_DB:-complychecker_dev} # Use same database as main app
      KC_DB_USERNAME: ${POSTGRES_USER:-complyuser}     # Use same user as main app
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD:-complypassword} # Use same password as main app
      KC_DB_SCHEMA: public # Keycloak will use the public schema within its own DB
      KC_HTTP_RELATIVE_PATH: /auth
      KC_PROXY: edge
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_LOG_LEVEL: debug # Added for detailed logging
    command:
      - "start-dev"
      # For production, use 'start' and configure TLS/hostname properly
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/auth/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s # Give Keycloak time to start up

  mailhog:
    image: mailhog/mailhog:latest
    container_name: comply_checker_mailhog
    restart: unless-stopped
    ports:
      - "${MAILHOG_SMTP_PORT:-1025}:1025" # SMTP port
      - "${MAILHOG_HTTP_PORT:-8025}:8025" # Web UI port



  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: comply_checker_backend
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      POSTGRES_HOST: postgres
      POSTGRES_USER: ${POSTGRES_USER:-complyuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-complypassword}
      POSTGRES_DB: ${POSTGRES_DB:-complychecker_dev}
      POSTGRES_PORT: ${POSTGRES_PORT:-5432}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      SESSION_SECRET: ${SESSION_SECRET:-local-dev-secret}
      KEYCLOAK_ADMIN_USER: ${KEYCLOAK_ADMIN_USER:-admin}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin}
      KEYCLOAK_PORT: ${KEYCLOAK_PORT:-8080}
      KEYCLOAK_REALM: ${KEYCLOAK_REALM:-comply-checker}
      KEYCLOAK_CLIENT_ID_FRONTEND: ${KEYCLOAK_CLIENT_ID_FRONTEND:-comply-checker-frontend}
      KEYCLOAK_CLIENT_ID_BACKEND: ${KEYCLOAK_CLIENT_ID_BACKEND:-comply-checker-backend}
      KEYCLOAK_CLIENT_SECRET_BACKEND: ${KEYCLOAK_CLIENT_SECRET_BACKEND:-your-backend-client-secret}
      KEYCLOAK_URL: http://keycloak:8080
      SMTP_HOST: mailhog
      SMTP_PORT: ${MAILHOG_SMTP_PORT:-1025}
      # HIPAA Security Configuration
      HIPAA_SECURITY_ENABLED: true
      HIPAA_SCAN_TIMEOUT: 1800000
      HIPAA_MAX_PAGES: 15
      HIPAA_SCAN_DEPTH: 2
      HIPAA_ENABLE_VULN_SCAN: true
      HIPAA_ENABLE_SSL_ANALYSIS: true
      HIPAA_ENABLE_CONTENT_ANALYSIS: true
      # Nuclei Configuration
      NUCLEI_PATH: /usr/local/bin/nuclei
      NUCLEI_TEMPLATES_PATH: /app/nuclei-templates
      NUCLEI_ENABLED: true
    volumes:
      # - ./backend:/app # Commented out to use the /app directory from the image, including /app/dist
      - /app/node_modules
      - nuclei_templates:/app/nuclei-templates
    depends_on:
      postgres:
        condition: service_healthy
      keycloak:
        condition: service_started

volumes:
  postgres_data:
    driver: local
  nuclei_templates:
    driver: local

networks:
  default:
    name: comply_checker_network
