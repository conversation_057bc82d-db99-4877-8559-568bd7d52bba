'use client';

import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { getScanDetails } from '@/lib/api';
import { Scan } from '@/backend-types/index';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { ArrowLeft, Loader2, AlertTriangle, CheckCircle, Info } from 'lucide-react';

interface Finding {
  id: string;
  type: string;
  severity: string;
  title: string;
  description: string;
  recommendation?: string;
  category?: string;
}

interface CheckDetail {
  findings?: Array<{
    id?: string;
    type?: string;
    severity?: string;
    message?: string;
    content?: string;
    suggestion?: string;
  }>;
}

interface Check {
  details?: CheckDetail;
  passed: boolean;
  name?: string;
  checkId?: string;
  category?: string;
}

interface HipaaRecommendation {
  id?: string;
  title?: string;
  description?: string;
  priority: string;
  category: string;
}

interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: string;
  category: string;
}

const ScanFindingsPage = () => {
  const params = useParams();
  const router = useRouter();
  const { authenticated, loading: authLoading } = useAuth();
  const scanId = params?.scanId as string | undefined;
  const [scan, setScan] = useState<Scan | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'findings' | 'recommendations'>('findings');

  useEffect(() => {
    if (authLoading) return;
    if (!authenticated) {
      router.push('/auth/login');
      return;
    }
    if (scanId) {
      setIsLoading(true);
      getScanDetails(scanId)
        .then((data: Scan) => {
          setScan(data);
        })
        .catch((err) => {
          setError(err.response?.data?.error || err.message || 'Failed to load scan details.');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [scanId, authenticated, authLoading, router]);

  if (isLoading || authLoading) {
    return (
      <div className="flex justify-center items-center h-screen" role="status">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2">Loading findings...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p role="alert">{error}</p>
            <Button onClick={() => router.back()} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!scan || !scan.enhancedHipaaResults) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p role="status">No enhanced findings available for this scan.</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    );
  }

  // Extract findings and recommendations from enhanced results
  const findings: Finding[] = [];
  const recommendations: Recommendation[] = [];

  // Process enhanced HIPAA results
  if (scan.enhancedHipaaResults) {
    // Extract findings from checksBreakdown
    if (scan.enhancedHipaaResults.checksBreakdown) {
      scan.enhancedHipaaResults.checksBreakdown.forEach((check: Check, checkIndex: number) => {
        if (check.details?.findings) {
          check.details.findings.forEach((finding, findingIndex: number) => {
            findings.push({
              id: finding.id || `finding-${checkIndex}-${findingIndex}`,
              type: finding.type || 'compliance_issue',
              severity: finding.severity || 'MEDIUM',
              title: finding.message || finding.content || `Finding ${findingIndex + 1}`,
              description: finding.content || finding.message || 'No description available',
              recommendation: finding.suggestion,
              category: check.category || 'general',
            });
          });
        }

        // Also add failed checks as findings
        if (!check.passed) {
          findings.push({
            id: `check-${checkIndex}`,
            type: 'non_compliant',
            severity: 'MEDIUM',
            title: check.name || `Check ${checkIndex + 1}`,
            description: `Check failed: ${check.name || check.checkId}`,
            recommendation: 'Review and address this compliance requirement',
            category: check.category || 'general',
          });
        }
      });
    }

    // Extract recommendations
    if (scan.enhancedHipaaResults.recommendations) {
      scan.enhancedHipaaResults.recommendations.forEach(
        (rec: HipaaRecommendation, index: number) => {
          recommendations.push({
            id: rec.id || `rec-${index}`,
            title: rec.title || `Recommendation ${index + 1}`,
            description: rec.description || 'No description available',
            priority: rec.priority || 'MEDIUM',
            category: rec.category || 'general',
          });
        },
      );
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <Info className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: string | null | undefined) => {
    if (!priority) return 'bg-gray-500';

    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" onClick={() => router.back()} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Scan Details
          </Button>
          <h1 className="text-3xl font-bold">Detailed Findings & Recommendations</h1>
          <p className="text-gray-600">Scan ID: {scan.id}</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('findings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'findings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Findings ({findings.length})
          </button>
          <button
            onClick={() => setActiveTab('recommendations')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'recommendations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Recommendations ({recommendations.length})
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'findings' && (
        <div className="space-y-4">
          {findings.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900">No Issues Found</h3>
                <p className="text-gray-600">
                  Great! No compliance issues were detected in this scan.
                </p>
              </CardContent>
            </Card>
          ) : (
            findings.map((finding) => (
              <Card key={finding.id} className="border-l-4 border-l-orange-500">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {getSeverityIcon(finding.severity)}
                      <div>
                        <CardTitle className="text-lg">{finding.title}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={`${getSeverityColor(finding.severity)} text-white`}>
                            {finding.severity.toUpperCase()}
                          </Badge>
                          <Badge variant="outline">{finding.category}</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{finding.description}</p>
                  {finding.recommendation && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-900 mb-2">Recommended Action:</h4>
                      <p className="text-blue-800 text-sm">{finding.recommendation}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {activeTab === 'recommendations' && (
        <div className="space-y-4">
          {recommendations.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <Info className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900">No Recommendations</h3>
                <p className="text-gray-600">
                  No specific recommendations are available for this scan.
                </p>
              </CardContent>
            </Card>
          ) : (
            recommendations.map((recommendation) => (
              <Card key={recommendation.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{recommendation.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge
                          className={`${getPriorityColor(recommendation.priority)} text-white`}
                        >
                          {recommendation.priority
                            ? recommendation.priority.toUpperCase()
                            : 'UNKNOWN'}{' '}
                          PRIORITY
                        </Badge>
                        <Badge variant="outline">{recommendation.category}</Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{recommendation.description}</p>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-4 pt-6 border-t">
        <Link href="/guidance">
          <Button variant="outline">View Compliance Guidance</Button>
        </Link>
        <Link href="/dashboard/scan/new">
          <Button>Run New Scan</Button>
        </Link>
      </div>
    </div>
  );
};

export default ScanFindingsPage;
