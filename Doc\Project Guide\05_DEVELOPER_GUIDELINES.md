# Developer Guidelines

## 🎯 Overview

This document provides comprehensive guidelines for developing and maintaining the Comply Checker project. Following these guidelines ensures code consistency, maintainability, and adherence to project standards.

## 🚀 Quick Start

### Prerequisites
- **Node.js**: 18.x or higher
- **npm**: 9.x or higher
- **Docker**: 24.x or higher
- **PostgreSQL**: 16.x (via Docker)
- **Git**: Latest version

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd comply-checker

# Install dependencies
npm install

# Install frontend dependencies
cd frontend && npm install && cd ..

# Install backend dependencies
cd backend && npm install && cd ..

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development environment
docker-compose up -d postgres keycloak
npm run dev:backend &
npm run dev:frontend
```

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Keycloak Admin**: http://localhost:8080/auth/admin
- **Database**: localhost:5432

## 📁 Project Structure

### Monorepo Organization
```
comply-checker/
├── frontend/           # Next.js 14 application
├── backend/            # Express.js API server
├── docs/              # Project documentation
├── scripts/           # Utility scripts
├── docker/            # Docker configurations
├── .env               # Environment variables
├── docker-compose.yml # Development services
└── package.json       # Root package configuration
```

### Workspace Commands
```bash
# Root level commands
npm run dev:frontend    # Start frontend development server
npm run dev:backend     # Start backend development server
npm run build          # Build both frontend and backend
npm run test           # Run all tests
npm run lint           # Lint all code
npm run format         # Format all code

# Frontend specific
cd frontend
npm run dev            # Development server
npm run build          # Production build
npm run start          # Start production server
npm run lint           # ESLint check
npm run type-check     # TypeScript check

# Backend specific
cd backend
npm run dev            # Development server with nodemon
npm run build          # TypeScript compilation
npm run start          # Start production server
npm run test           # Jest tests
npm run migrate        # Run database migrations
```

## 🎨 Coding Standards

### TypeScript Configuration

#### Strict Type Safety

### 🚫 **TypeScript Strict Requirements**

#### **PROHIBITED PRACTICES**
```typescript
// ❌ NEVER use 'any' type - Always define precise interfaces
function processScan(data: any): any {  // FORBIDDEN
  return data.result;
}

// ❌ NEVER use 'any[]' arrays - Always specify array element types
const results: any[] = [];  // FORBIDDEN
const findings: any[] = scanResults;  // FORBIDDEN

// ❌ NEVER use '@ts-ignore' - Use '@ts-expect-error' with explanation
// @ts-ignore  // FORBIDDEN
const value = someComplexOperation();

// ❌ NEVER use 'object' type - Define specific interfaces
function updateScan(data: object): void { }  // FORBIDDEN
```

#### **REQUIRED PRACTICES**
```typescript
// ✅ ALWAYS define precise interfaces for all data structures
interface HipaaScanRequest {
  targetUrl: string;
  options?: {
    enableLevel1?: boolean;
    enableLevel2?: boolean;
    enableLevel3?: boolean;
    timeout?: number;
  };
}

// ✅ ALWAYS specify array element types
const results: HipaaScanResult[] = [];
const findings: ComplianceFinding[] = scanResults.findings;

// ✅ USE union types for known value sets
type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// ✅ HANDLE errors with 'unknown' type and proper type guards
try {
  const result = await scanService.performScan(request);
  return result;
} catch (error: unknown) {
  if (error instanceof Error) {
    console.error('Scan failed:', error.message);
  }
  throw new Error('Scan processing failed');
}

// ✅ USE '@ts-expect-error' with explanation when absolutely necessary
// @ts-expect-error - Legacy API doesn't have proper types yet
const legacyResult = oldApiCall();
```

#### **TypeScript Configuration Requirements**
```json
// tsconfig.json - REQUIRED settings
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

#### Naming Conventions
```typescript
// ✅ Interfaces and Types: PascalCase
interface UserProfile { }
type ScanStatus = 'pending' | 'completed' | 'failed';

// ✅ Variables and Functions: camelCase
const scanResults = await getScanResults();
function calculateComplianceScore() { }

// ✅ Constants: SCREAMING_SNAKE_CASE
const MAX_SCAN_TIMEOUT = 300000;
const DEFAULT_PAGE_SIZE = 50;

// ✅ Components: PascalCase
function ComplianceDashboard() { }
const ScanResultsTable = () => { };

// ✅ Files: kebab-case
// hipaa-dashboard-api.ts
// scan-results-table.tsx
// compliance-overview.component.tsx
```

### React/Next.js Best Practices

#### Component Structure
```typescript
// ✅ Good: Proper component structure
interface ScanResultsProps {
  scanId: string;
  onRefresh?: () => void;
}

export function ScanResults({ scanId, onRefresh }: ScanResultsProps) {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<ScanResult | null>(null);

  useEffect(() => {
    loadScanResults();
  }, [scanId]);

  const loadScanResults = async () => {
    try {
      setLoading(true);
      const data = await scanService.getScanResults(scanId);
      setResults(data);
    } catch (error) {
      console.error('Failed to load scan results:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <LoadingSpinner />;
  if (!results) return <div>No results found</div>;

  return (
    <div className="scan-results">
      {/* Component JSX */}
    </div>
  );
}
```

#### Hooks Usage
```typescript
// ✅ Good: Custom hooks for reusable logic
export function useScanResults(scanId: string) {
  const [results, setResults] = useState<ScanResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refresh = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await scanService.getScanResults(scanId);
      setResults(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [scanId]);

  useEffect(() => {
    refresh();
  }, [refresh]);

  return { results, loading, error, refresh };
}
```

### Backend/Express.js Best Practices

#### Route Structure
```typescript
// ✅ Good: Proper route organization
import { Router } from 'express';
import { validateRequest } from '../middleware/validation';
import { requireAuth } from '../middleware/auth';
import { scanSchema } from '../schemas/scan';

const router = Router();

router.post('/scan', 
  requireAuth,
  validateRequest(scanSchema),
  async (req, res, next) => {
    try {
      const { targetUrl, options } = req.body;
      const result = await scanService.performScan(targetUrl, options);
      
      res.json({
        success: true,
        data: result,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.id,
          processingTime: Date.now() - req.startTime
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
```

#### Error Handling
```typescript
// ✅ Good: Centralized error handling
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_ERROR'
  ) {
    super(message);
    this.name = 'AppError';
  }
}

// Error middleware
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: req.id
      }
    });
  }

  // Log unexpected errors
  console.error('Unexpected error:', error);
  
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    }
  });
}
```

## 🗄️ Database Guidelines

### Migration Best Practices
```typescript
// ✅ Good: Descriptive migration names
// 20250626_001_add_error_message_to_scans.ts
// 20250626_002_create_hipaa_security_scans_table.ts

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('hipaa_security_scans', (table) => {
    table.uuid('scan_id').primary();
    table.text('target_url').notNullable();
    table.integer('overall_score');
    table.string('risk_level', 20);
    table.integer('scan_duration');
    table.specificType('tools_used', 'text[]');
    table.timestamps(true, true);
    
    // Indexes
    table.index('target_url');
    table.index('risk_level');
    table.index('created_at');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('hipaa_security_scans');
}
```

### Query Best Practices
```typescript
// ✅ Good: Parameterized queries with proper types
async function getScansByUser(userId: string, limit: number = 50): Promise<Scan[]> {
  return knex('scans')
    .select('*')
    .where('user_id', userId)
    .orderBy('created_at', 'desc')
    .limit(limit);
}

// ✅ Good: Transaction usage for related operations
async function createScanWithFindings(scanData: ScanData, findings: Finding[]): Promise<string> {
  return knex.transaction(async (trx) => {
    const [scanId] = await trx('scans').insert(scanData).returning('id');
    
    if (findings.length > 0) {
      const findingsWithScanId = findings.map(finding => ({
        ...finding,
        scan_id: scanId
      }));
      await trx('compliance_findings').insert(findingsWithScanId);
    }
    
    return scanId;
  });
}
```

## 🎨 UI/UX Guidelines

### Design System Compliance
```typescript
// ✅ Good: Using design system colors
const riskColors = {
  critical: 'text-red-600 bg-red-50 border-red-200',
  high: 'text-orange-600 bg-orange-50 border-orange-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  low: 'text-green-600 bg-green-50 border-green-200'
};

// ✅ Good: WCAG AA compliant components
function StatusBadge({ status, children }: StatusBadgeProps) {
  return (
    <span 
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        riskColors[status]
      )}
      role="status"
      aria-label={`Status: ${status}`}
    >
      {children}
    </span>
  );
}
```

### Accessibility Requirements
```typescript
// ✅ Good: Proper ARIA labels and semantic HTML
function ComplianceTable({ data }: ComplianceTableProps) {
  return (
    <div role="region" aria-labelledby="compliance-table-title">
      <h2 id="compliance-table-title">Compliance Scan Results</h2>
      <table role="table" aria-describedby="compliance-table-desc">
        <caption id="compliance-table-desc">
          Table showing compliance scan results with scores and risk levels
        </caption>
        <thead>
          <tr>
            <th scope="col">Scan Date</th>
            <th scope="col">Target URL</th>
            <th scope="col">Score</th>
            <th scope="col">Risk Level</th>
          </tr>
        </thead>
        <tbody>
          {data.map((scan) => (
            <tr key={scan.id}>
              <td>{formatDate(scan.createdAt)}</td>
              <td>{scan.targetUrl}</td>
              <td>{scan.score}%</td>
              <td>
                <StatusBadge status={scan.riskLevel}>
                  {scan.riskLevel}
                </StatusBadge>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

## 🧪 Testing Guidelines

### Unit Testing
```typescript
// ✅ Good: Comprehensive unit tests
describe('HipaaScanService', () => {
  let scanService: HipaaScanService;
  let mockDatabase: jest.Mocked<Database>;

  beforeEach(() => {
    mockDatabase = createMockDatabase();
    scanService = new HipaaScanService(mockDatabase);
  });

  describe('performPrivacyScan', () => {
    it('should return scan results for valid URL', async () => {
      // Arrange
      const targetUrl = 'https://example-healthcare.com';
      const expectedResult = createMockScanResult();
      mockDatabase.saveScan.mockResolvedValue(expectedResult);

      // Act
      const result = await scanService.performPrivacyScan(targetUrl);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(result.overallScore).toBeGreaterThan(0);
      expect(result.riskLevel).toMatch(/^(critical|high|medium|low)$/);
    });

    it('should handle invalid URLs gracefully', async () => {
      // Arrange
      const invalidUrl = 'not-a-url';

      // Act & Assert
      await expect(scanService.performPrivacyScan(invalidUrl))
        .rejects
        .toThrow('Invalid URL format');
    });
  });
});
```

### Integration Testing
```typescript
// ✅ Good: API integration tests
describe('HIPAA Privacy API', () => {
  let app: Express;
  let authToken: string;

  beforeAll(async () => {
    app = createTestApp();
    authToken = await getTestAuthToken();
  });

  it('should start privacy policy scan', async () => {
    const response = await request(app)
      .post('/api/v1/compliance/hipaa/privacy/scan')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        targetUrl: 'https://example-healthcare.com'
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.scanId).toBeDefined();
    expect(response.body.data.overallScore).toBeGreaterThanOrEqual(0);
  });
});
```

## 🔄 Git Workflow

### Branch Naming
```bash
# Feature branches
feature/hipaa-privacy-analysis
feature/gdpr-cookie-consent
feature/dashboard-improvements

# Bug fix branches
bugfix/nuclei-scanner-integration
bugfix/typescript-errors
bugfix/accessibility-issues

# Hotfix branches
hotfix/security-vulnerability
hotfix/production-error
```

### Commit Messages
```bash
# ✅ Good: Descriptive commit messages
feat(hipaa): implement 3-level privacy policy analysis
fix(security): resolve Nuclei scanner command execution issue
docs(api): add comprehensive endpoint documentation
refactor(frontend): improve component structure and accessibility
test(backend): add unit tests for scan service

# ❌ Bad: Vague commit messages
fix bug
update code
changes
```

### Pull Request Process
1. **Create Feature Branch**: `git checkout -b feature/your-feature`
2. **Make Changes**: Follow coding standards and write tests
3. **Run Quality Checks**: `npm run lint && npm run test && npm run type-check`
4. **Commit Changes**: Use conventional commit format
5. **Push Branch**: `git push origin feature/your-feature`
6. **Create PR**: Include description, testing notes, and screenshots
7. **Code Review**: Address feedback and update as needed
8. **Merge**: Squash and merge after approval

## 🚀 Deployment Guidelines

### Environment Configuration
```bash
# Development
NODE_ENV=development
DATABASE_URL=postgresql://user:pass@localhost:5432/comply_checker_dev
KEYCLOAK_URL=http://localhost:8080/auth

# Staging
NODE_ENV=staging
DATABASE_URL=**************************************/comply_checker_staging
KEYCLOAK_URL=https://staging-auth.example.com/auth

# Production
NODE_ENV=production
DATABASE_URL=***********************************/comply_checker
KEYCLOAK_URL=https://auth.example.com/auth
```

### Docker Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to staging
docker-compose -f docker-compose.staging.yml up -d

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d

# Health check
curl -f http://localhost:5000/api/v1/health || exit 1
```

## 📚 Documentation Standards

### Code Documentation
```typescript
/**
 * Performs comprehensive HIPAA privacy policy analysis using 3-level approach
 * 
 * @param targetUrl - The URL to analyze for HIPAA compliance
 * @param options - Configuration options for the scan
 * @returns Promise resolving to detailed scan results
 * 
 * @example
 * ```typescript
 * const results = await performPrivacyScan('https://healthcare.com', {
 *   enableLevel3: true,
 *   timeout: 300000
 * });
 * ```
 */
async function performPrivacyScan(
  targetUrl: string, 
  options: ScanOptions = {}
): Promise<HipaaPrivacyScanResult> {
  // Implementation
}
```

### API Documentation
- Use OpenAPI/Swagger specifications
- Include request/response examples
- Document error codes and responses
- Provide authentication details
- Include rate limiting information

## 🔄 **Git Workflow Standards**

> **⚠️ IMPORTANT HUSKY PRE-COMMIT HOOK WARNING**: This project uses Husky pre-commit hooks that automatically run linting checks. If there are ANY lint errors or warnings when you attempt to commit, Husky will prevent the commit and revert your changes to the previous version. Always run `npm run lint` and fix all issues before committing to avoid losing your work.

### **Branch Naming Conventions**
```bash
# Feature branches
feature/hipaa-privacy-analysis
feature/gdpr-cookie-consent
feature/dashboard-improvements
feature/legalbert-integration

# Bug fix branches
bugfix/nuclei-scanner-integration
bugfix/typescript-errors
bugfix/accessibility-issues
bugfix/scoring-system-alignment

# Hotfix branches (for production issues)
hotfix/security-vulnerability
hotfix/production-error
hotfix/critical-compliance-bug

# Release branches
release/v2.1.0
release/v2.2.0
```

### **Commit Message Standards**
Follow **Conventional Commits** format for consistency and automated changelog generation:

```bash
# Format: <type>(<scope>): <description>
# Types: feat, fix, docs, style, refactor, test, chore

# ✅ Good commit messages
feat(hipaa): implement 3-level privacy policy analysis
fix(security): resolve Nuclei scanner command execution issue
docs(api): add comprehensive endpoint documentation
refactor(frontend): improve component structure and accessibility
test(backend): add unit tests for scan service
chore(deps): update dependencies to latest versions
style(frontend): fix ESLint formatting issues

# ❌ Bad commit messages
fix bug
update code
changes
working on stuff
```

### **Pull Request Process**
1. **Create Feature Branch**: `git checkout -b feature/your-feature`
2. **Make Changes**: Follow coding standards and write tests
3. **Pre-commit Checks**:
   ```bash
   npm run lint        # Fix ALL linting errors
   npm run test        # Ensure all tests pass
   npm run type-check  # Verify TypeScript compilation
   ```
4. **Commit Changes**: Use conventional commit format
5. **Push Branch**: `git push origin feature/your-feature`
6. **Create PR**: Include description, testing notes, and screenshots
7. **Code Review**: Address feedback and update as needed
8. **Merge**: Squash and merge after approval

### **Pre-commit Checklist**
Before every commit, ensure:
- [ ] `npm run lint` passes with zero errors/warnings
- [ ] `npm run type-check` passes with zero TypeScript errors
- [ ] `npm run test` passes all unit tests
- [ ] No `any` types used anywhere in new code
- [ ] All new functions have proper TypeScript interfaces
- [ ] WCAG AA accessibility standards maintained
- [ ] Security best practices followed

## 🔧 Troubleshooting

### Common Issues

#### TypeScript Errors
```bash
# Clear TypeScript cache
rm -rf node_modules/.cache
npm run type-check

# Rebuild dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Database Issues
```bash
# Reset database
npm run db:reset
npm run migrate
npm run seed

# Check connection
npm run db:status
```

#### Docker Issues
```bash
# Clean Docker environment
docker-compose down -v
docker system prune -f
docker-compose up -d
```

#### Husky Pre-commit Hook Issues
```bash
# If Husky prevents commit due to lint errors:
npm run lint          # See all linting errors
npm run lint --fix    # Auto-fix what can be fixed
# Manually fix remaining errors, then commit again

# If Husky is not working:
npx husky install     # Reinstall hooks
chmod +x .husky/*     # Ensure hooks are executable
```

---

*These guidelines ensure consistent, maintainable, and high-quality code across the Comply Checker project. Regular updates to this document reflect evolving best practices and project requirements.*
