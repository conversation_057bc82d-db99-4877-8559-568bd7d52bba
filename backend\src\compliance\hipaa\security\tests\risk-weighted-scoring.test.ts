/**
 * Test the new risk-weighted scoring system
 * This demonstrates how the new scoring aligns with risk levels
 */

// Simple test interfaces
interface TestResult {
  testId: string;
  passed: boolean;
  riskLevel?: 'critical' | 'high' | 'medium' | 'low';
}

// Mock implementation of the risk-weighted scoring for testing
function calculateRiskWeightedScore(allTests: TestResult[]): number {
  if (allTests.length === 0) return 100;

  const riskWeights = {
    critical: 40,
    high: 30,
    medium: 20,
    low: 10,
  };

  let totalPossiblePoints = 0;
  let earnedPoints = 0;

  allTests.forEach((test) => {
    let testRiskLevel: 'critical' | 'high' | 'medium' | 'low' = 'low';

    if (!test.passed) {
      testRiskLevel = test.riskLevel || 'low';
    } else {
      if (test.testId.includes('HTTPS') || test.testId.includes('SSL')) {
        testRiskLevel = 'critical';
      } else if (test.testId.includes('HEADERS') || test.testId.includes('AUTH')) {
        testRiskLevel = 'high';
      } else if (test.testId.includes('PRIVACY') || test.testId.includes('CONTENT')) {
        testRiskLevel = 'medium';
      } else {
        testRiskLevel = 'low';
      }
    }

    const pointValue = riskWeights[testRiskLevel];
    totalPossiblePoints += pointValue;

    if (test.passed) {
      earnedPoints += pointValue;
    }
  });

  const score =
    totalPossiblePoints > 0 ? Math.round((earnedPoints / totalPossiblePoints) * 100) : 100;
  return Math.max(0, Math.min(100, score));
}

function determineRiskLevelFromScore(score: number, criticalIssues: number): string {
  if (criticalIssues > 0) return 'critical';
  if (score <= 30) return 'critical';
  if (score <= 60) return 'high';
  if (score <= 80) return 'medium';
  return 'low';
}

// Test scenarios
console.log('🧪 Testing Risk-Weighted Scoring System\n');

// Scenario 1: All tests pass (should be 100% and low risk)
const allPassedTests: TestResult[] = [
  { testId: 'HTTPS-001', passed: true },
  { testId: 'SSL-001', passed: true },
  { testId: 'HEADERS-001', passed: true },
  { testId: 'PRIVACY-001', passed: true },
  { testId: 'OTHER-001', passed: true },
];

const score1 = calculateRiskWeightedScore(allPassedTests);
const risk1 = determineRiskLevelFromScore(score1, 0);
console.log(`✅ All Tests Pass: ${score1}% - ${risk1.toUpperCase()} risk`);

// Scenario 2: Critical SSL failure (should be low score and critical risk)
const criticalFailureTests: TestResult[] = [
  { testId: 'HTTPS-001', passed: false, riskLevel: 'critical' },
  { testId: 'SSL-001', passed: false, riskLevel: 'critical' },
  { testId: 'HEADERS-001', passed: true },
  { testId: 'PRIVACY-001', passed: true },
  { testId: 'OTHER-001', passed: true },
];

const score2 = calculateRiskWeightedScore(criticalFailureTests);
const risk2 = determineRiskLevelFromScore(score2, 2);
console.log(`❌ Critical SSL Failures: ${score2}% - ${risk2.toUpperCase()} risk`);

// Scenario 3: High-level failures only (should be medium score and high risk)
const highFailureTests: TestResult[] = [
  { testId: 'HTTPS-001', passed: true },
  { testId: 'SSL-001', passed: true },
  { testId: 'HEADERS-001', passed: false, riskLevel: 'high' },
  { testId: 'AUTH-001', passed: false, riskLevel: 'high' },
  { testId: 'OTHER-001', passed: true },
];

const score3 = calculateRiskWeightedScore(highFailureTests);
const risk3 = determineRiskLevelFromScore(score3, 0);
console.log(`⚠️  High-Level Failures: ${score3}% - ${risk3.toUpperCase()} risk`);

// Scenario 4: Only low-level failures (should be high score and low risk)
const lowFailureTests: TestResult[] = [
  { testId: 'HTTPS-001', passed: true },
  { testId: 'SSL-001', passed: true },
  { testId: 'HEADERS-001', passed: true },
  { testId: 'PRIVACY-001', passed: true },
  { testId: 'OTHER-001', passed: false, riskLevel: 'low' },
  { testId: 'OTHER-002', passed: false, riskLevel: 'low' },
];

const score4 = calculateRiskWeightedScore(lowFailureTests);
const risk4 = determineRiskLevelFromScore(score4, 0);
console.log(`ℹ️  Low-Level Failures: ${score4}% - ${risk4.toUpperCase()} risk`);

console.log('\n📊 Risk Level Ranges:');
console.log('🟢 81-100%: Low Risk');
console.log('🟡 61-80%: Medium Risk');
console.log('🟠 31-60%: High Risk');
console.log('🔴 0-30%: Critical Risk');

console.log(
  '\n✅ Risk-weighted scoring system provides logical alignment between scores and risk levels!',
);
