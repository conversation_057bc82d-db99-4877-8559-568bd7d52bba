#!/usr/bin/env node

/**
 * HIPAA Dashboard Integration Verification Script
 * Verifies that all dashboard components and integrations are working correctly
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Test results
let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName, status, message = '') {
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  
  log(`${statusIcon} ${testName}: ${status}`, statusColor);
  if (message) {
    log(`   ${message}`, 'reset');
  }
  
  testResults.tests.push({ name: testName, status, message });
  
  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') testResults.failed++;
  else testResults.warnings++;
}

function checkFileExists(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    logTest(`File Check: ${description}`, 'PASS', filePath);
    return true;
  } else {
    logTest(`File Check: ${description}`, 'FAIL', `Missing: ${filePath}`);
    return false;
  }
}

function checkDirectoryStructure() {
  log('\n📁 Checking Directory Structure...', 'blue');
  
  const requiredFiles = [
    // Frontend Pages
    { path: 'frontend/app/dashboard/hipaa/page.tsx', desc: 'Main HIPAA Dashboard' },
    { path: 'frontend/app/dashboard/hipaa/privacy/page.tsx', desc: 'Privacy Scans Listing' },
    { path: 'frontend/app/dashboard/hipaa/privacy/[scanId]/page.tsx', desc: 'Privacy Scan Details' },
    { path: 'frontend/app/dashboard/hipaa/security/page.tsx', desc: 'Security Scans Listing' },
    { path: 'frontend/app/dashboard/hipaa/security/[scanId]/page.tsx', desc: 'Security Scan Details' },
    { path: 'frontend/app/dashboard/hipaa/test/page.tsx', desc: 'Integration Test Page' },
    
    // Frontend Components
    { path: 'frontend/components/dashboard/hipaa/HipaaDashboard.tsx', desc: 'Main Dashboard Component' },
    { path: 'frontend/components/dashboard/hipaa/HipaaOverviewCard.tsx', desc: 'Overview Card Component' },
    { path: 'frontend/components/dashboard/hipaa/HipaaModuleCard.tsx', desc: 'Module Card Component' },
    { path: 'frontend/components/dashboard/hipaa/index.ts', desc: 'HIPAA Components Index' },
    
    // Shared Components
    { path: 'frontend/components/dashboard/shared/ComplianceMetrics.tsx', desc: 'Compliance Metrics Component' },
    { path: 'frontend/components/dashboard/shared/RiskLevelIndicator.tsx', desc: 'Risk Level Indicator' },
    { path: 'frontend/components/dashboard/shared/ScanStatusBadge.tsx', desc: 'Scan Status Badge' },
    { path: 'frontend/components/dashboard/shared/index.ts', desc: 'Shared Components Index' },
    
    // Navigation Components
    { path: 'frontend/components/navigation/ComplianceBreadcrumb.tsx', desc: 'Breadcrumb Navigation' },
    { path: 'frontend/components/navigation/index.ts', desc: 'Navigation Components Index' },
    
    // Services
    { path: 'frontend/services/hipaa-dashboard-api.ts', desc: 'Frontend API Service' },
    
    // Backend Files
    { path: 'backend/src/routes/compliance/hipaa.ts', desc: 'Backend HIPAA Routes' },
    { path: 'backend/src/services/hipaa-dashboard-service.ts', desc: 'Backend Dashboard Service' },
    { path: 'backend/src/services/hipaa-privacy-service.ts', desc: 'Backend Privacy Service' },
    { path: 'backend/src/services/hipaa-security-service.ts', desc: 'Backend Security Service' }
  ];
  
  requiredFiles.forEach(file => {
    checkFileExists(file.path, file.desc);
  });
}

function checkComponentImports() {
  log('\n🔗 Checking Component Imports...', 'blue');
  
  const importChecks = [
    {
      file: 'frontend/components/dashboard/hipaa/index.ts',
      imports: ['HipaaDashboard', 'HipaaOverviewCard', 'HipaaModuleCard'],
      desc: 'HIPAA Components Exports'
    },
    {
      file: 'frontend/components/dashboard/shared/index.ts',
      imports: ['ComplianceMetrics', 'RiskLevelIndicator', 'ScanStatusBadge'],
      desc: 'Shared Components Exports'
    },
    {
      file: 'frontend/components/navigation/index.ts',
      imports: ['ComplianceBreadcrumb', 'createComplianceBreadcrumbs'],
      desc: 'Navigation Components Exports'
    }
  ];
  
  importChecks.forEach(check => {
    const filePath = path.join(__dirname, '..', check.file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const missingImports = check.imports.filter(imp => !content.includes(imp));
      
      if (missingImports.length === 0) {
        logTest(`Import Check: ${check.desc}`, 'PASS');
      } else {
        logTest(`Import Check: ${check.desc}`, 'FAIL', `Missing exports: ${missingImports.join(', ')}`);
      }
    } else {
      logTest(`Import Check: ${check.desc}`, 'FAIL', `File not found: ${check.file}`);
    }
  });
}

function checkRouteStructure() {
  log('\n🛣️ Checking Route Structure...', 'blue');
  
  // Check that old compliance folder is removed
  const oldCompliancePath = path.join(__dirname, '..', 'frontend/app/dashboard/compliance');
  if (!fs.existsSync(oldCompliancePath)) {
    logTest('Route Structure: Old compliance folder removed', 'PASS');
  } else {
    logTest('Route Structure: Old compliance folder removed', 'FAIL', 'Old compliance folder still exists');
  }
  
  // Check new HIPAA structure
  const newHipaaPath = path.join(__dirname, '..', 'frontend/app/dashboard/hipaa');
  if (fs.existsSync(newHipaaPath)) {
    logTest('Route Structure: New HIPAA folder exists', 'PASS');
  } else {
    logTest('Route Structure: New HIPAA folder exists', 'FAIL', 'New HIPAA folder not found');
  }
}

function checkBackendIntegration() {
  log('\n🔧 Checking Backend Integration...', 'blue');
  
  // Check backend route file
  const routeFile = path.join(__dirname, '..', 'backend/src/routes/compliance/hipaa.ts');
  if (fs.existsSync(routeFile)) {
    const content = fs.readFileSync(routeFile, 'utf8');
    
    // Check for dashboard endpoints
    if (content.includes('/dashboard') && content.includes('/metrics')) {
      logTest('Backend Integration: Dashboard endpoints', 'PASS');
    } else {
      logTest('Backend Integration: Dashboard endpoints', 'FAIL', 'Dashboard endpoints not found in route file');
    }
    
    // Check for service import
    if (content.includes('HipaaDashboardService')) {
      logTest('Backend Integration: Service import', 'PASS');
    } else {
      logTest('Backend Integration: Service import', 'FAIL', 'HipaaDashboardService import not found');
    }
  } else {
    logTest('Backend Integration: Route file exists', 'FAIL', 'Backend route file not found');
  }
}

function checkDocumentation() {
  log('\n📚 Checking Documentation...', 'blue');
  
  const docFiles = [
    { path: 'HIPAA_DASHBOARD_DEVELOPMENT_PLAN.md', desc: 'Development Plan' },
    { path: 'HIPAA_DASHBOARD_IMPLEMENTATION_SUMMARY.md', desc: 'Implementation Summary' },
    { path: 'HIPAA_DASHBOARD_USER_GUIDE.md', desc: 'User Guide' }
  ];
  
  docFiles.forEach(doc => {
    checkFileExists(doc.path, doc.desc);
  });
}

function checkTypeScriptTypes() {
  log('\n🔷 Checking TypeScript Types...', 'blue');
  
  const apiServiceFile = path.join(__dirname, '..', 'frontend/services/hipaa-dashboard-api.ts');
  if (fs.existsSync(apiServiceFile)) {
    const content = fs.readFileSync(apiServiceFile, 'utf8');
    
    const requiredTypes = [
      'HipaaDashboardData',
      'ScanActivity',
      'DashboardMetrics',
      'HipaaDashboardService'
    ];
    
    const missingTypes = requiredTypes.filter(type => !content.includes(type));
    
    if (missingTypes.length === 0) {
      logTest('TypeScript Types: API Service types', 'PASS');
    } else {
      logTest('TypeScript Types: API Service types', 'FAIL', `Missing types: ${missingTypes.join(', ')}`);
    }
  } else {
    logTest('TypeScript Types: API Service file', 'FAIL', 'API service file not found');
  }
}

function printSummary() {
  log('\n' + '='.repeat(60), 'bold');
  log('🎯 HIPAA Dashboard Integration Verification Summary', 'bold');
  log('='.repeat(60), 'bold');
  
  log(`\n📊 Test Results:`, 'blue');
  log(`   ✅ Passed: ${testResults.passed}`, 'green');
  log(`   ❌ Failed: ${testResults.failed}`, 'red');
  log(`   ⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  log(`   📝 Total Tests: ${testResults.tests.length}`, 'blue');
  
  const successRate = Math.round((testResults.passed / testResults.tests.length) * 100);
  log(`\n🎯 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  if (testResults.failed > 0) {
    log('\n❌ Failed Tests:', 'red');
    testResults.tests
      .filter(test => test.status === 'FAIL')
      .forEach(test => {
        log(`   • ${test.name}: ${test.message}`, 'red');
      });
  }
  
  if (testResults.warnings > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    testResults.tests
      .filter(test => test.status === 'WARN')
      .forEach(test => {
        log(`   • ${test.name}: ${test.message}`, 'yellow');
      });
  }
  
  log('\n🚀 Next Steps:', 'blue');
  if (testResults.failed === 0) {
    log('   ✅ All critical tests passed! Dashboard is ready for use.', 'green');
    log('   🎯 Access the dashboard at: /dashboard/hipaa', 'blue');
    log('   🧪 Run integration tests at: /dashboard/hipaa/test', 'blue');
  } else {
    log('   🔧 Fix the failed tests above before proceeding.', 'yellow');
    log('   📖 Refer to the documentation for troubleshooting.', 'blue');
  }
  
  log('\n📚 Documentation:', 'blue');
  log('   • Development Plan: HIPAA_DASHBOARD_DEVELOPMENT_PLAN.md', 'reset');
  log('   • Implementation Summary: HIPAA_DASHBOARD_IMPLEMENTATION_SUMMARY.md', 'reset');
  log('   • User Guide: HIPAA_DASHBOARD_USER_GUIDE.md', 'reset');
  
  log('\n' + '='.repeat(60), 'bold');
}

// Main execution
function main() {
  log('🔍 HIPAA Dashboard Integration Verification', 'bold');
  log('Verifying all components and integrations...', 'blue');
  
  checkDirectoryStructure();
  checkComponentImports();
  checkRouteStructure();
  checkBackendIntegration();
  checkDocumentation();
  checkTypeScriptTypes();
  
  printSummary();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run the verification
main();
