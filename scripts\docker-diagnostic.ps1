#!/usr/bin/env pwsh
# Simple Docker diagnostic script

Write-Host "🔍 Docker Diagnostic Report" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan

# Check if Docker command exists
Write-Host "`n1. Checking if Docker is installed..." -ForegroundColor Yellow
try {
    $dockerPath = Get-Command docker -ErrorAction Stop
    Write-Host "✅ Docker found at: $($dockerPath.Source)" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker command not found. Please install Docker Desktop." -ForegroundColor Red
    Write-Host "Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Check Docker version
Write-Host "`n2. Checking Docker version..." -ForegroundColor Yellow
try {
    $version = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $version" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to get Docker version" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error checking Docker version" -ForegroundColor Red
}

# Check if Docker daemon is running
Write-Host "`n3. Checking if Docker daemon is running..." -ForegroundColor Yellow
try {
    $info = docker info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is running" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker daemon is not running" -ForegroundColor Red
        Write-Host "Please start Docker Desktop manually" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking Docker daemon" -ForegroundColor Red
}

# Check Docker Compose
Write-Host "`n4. Checking Docker Compose..." -ForegroundColor Yellow
try {
    $composeVersion = docker-compose --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $composeVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker Compose not available" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error checking Docker Compose" -ForegroundColor Red
}

# Check current directory
Write-Host "`n5. Checking current directory..." -ForegroundColor Yellow
$currentDir = Get-Location
Write-Host "📁 Current directory: $currentDir" -ForegroundColor White

# Check if docker-compose.yml exists
if (Test-Path "docker-compose.yml") {
    Write-Host "✅ docker-compose.yml found" -ForegroundColor Green
} else {
    Write-Host "❌ docker-compose.yml not found" -ForegroundColor Red
}

# Check if .env file exists
if (Test-Path ".env") {
    Write-Host "✅ .env file found" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env file not found (will use defaults)" -ForegroundColor Yellow
}

Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "If Docker daemon is not running, please:" -ForegroundColor White
Write-Host "1. Open Docker Desktop application" -ForegroundColor White
Write-Host "2. Wait for it to start completely" -ForegroundColor White
Write-Host "3. Run this diagnostic again" -ForegroundColor White
Write-Host "`nIf Docker Desktop is not installed:" -ForegroundColor White
Write-Host "1. Download from https://www.docker.com/products/docker-desktop" -ForegroundColor White
Write-Host "2. Install and restart your computer" -ForegroundColor White
Write-Host "3. Start Docker Desktop" -ForegroundColor White
