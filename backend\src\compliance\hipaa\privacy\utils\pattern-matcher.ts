// backend/src/compliance/hipaa/privacy/utils/pattern-matcher.ts

import {
  PatternMatch,
  Level1Finding,
  Level1Result,
  HipaaSeverity,
  ContentCitation,
  EnhancedFinding,
  HipaaSpecificCheckConfig,
  EnhancedHipaaSectionConfig,
  HipaaCheckCategory,
} from '../types';
import {
  HIPAA_REQUIRED_SECTIONS,
  HIPAA_REQUIRED_CONTENT,
  ENHANCED_HIPAA_SECTIONS,
  HIPAA_SPECIFIC_CHECKS,
  GENERAL_PRIVACY_PATTERNS,
} from '../constants';
import { ContentCitationExtractor } from './content-citation';

/**
 * Pattern matching utilities for Level 1 HIPAA analysis
 * Implements basic phrase matching for exact HIPAA requirements
 */
export class PatternMatcher {
  private static patternCache = new Map<string, RegExp>();

  /**
   * Finds all patterns in content and returns matches with context
   * This is the core Level 1 analysis implementation
   */
  static findPatterns(content: string, patterns: readonly RegExp[]): PatternMatch[] {
    const matches: PatternMatch[] = [];

    for (const pattern of patterns) {
      const patternMatches = this.findPatternMatches(content, pattern);
      matches.push(...patternMatches);
    }

    return matches;
  }

  /**
   * Calculates compliance score based on found patterns
   * Returns percentage of required patterns found
   */
  static scoreMatches(matches: PatternMatch[]): number {
    const totalRequiredPatterns = this.getTotalRequiredPatterns();
    const uniqueRequirements = new Set(matches.map((match) => match.requirement));

    if (totalRequiredPatterns === 0) return 0;

    return Math.round((uniqueRequirements.size / totalRequiredPatterns) * 100);
  }

  /**
   * Extracts context around a pattern match for better understanding
   */
  static extractContext(content: string, match: PatternMatch, contextLength = 150): string {
    const start = Math.max(0, match.position - contextLength);
    const end = Math.min(content.length, match.position + match.text.length + contextLength);

    let context = content.substring(start, end);

    // Add ellipsis if we truncated
    if (start > 0) context = '...' + context;
    if (end < content.length) context = context + '...';

    return context.trim();
  }

  /**
   * Performs comprehensive Level 1 analysis on content
   * Uses hybrid approach: HIPAA patterns first, then general privacy patterns
   */
  static performLevel1Analysis(content: string): Level1Result {
    console.log('🔍 [Level 1 Analysis] Starting pattern matching analysis');
    console.log('📊 [Level 1 Analysis] Content length:', content.length);
    console.log('📝 [Level 1 Analysis] Content preview:', content.substring(0, 200) + '...');

    const startTime = Date.now();
    const allMatches: PatternMatch[] = [];
    const findings: Level1Finding[] = [];

    // First try HIPAA-specific patterns
    console.log('📋 [Level 1 Analysis] Analyzing HIPAA-specific patterns...');
    const hipaaResults = this.analyzeHIPAAPatterns(content);
    allMatches.push(...hipaaResults.matches);
    findings.push(...hipaaResults.findings);

    const hipaaScore = this.scoreMatches(hipaaResults.matches);
    console.log('✅ [Level 1 Analysis] HIPAA analysis complete:', {
      hipaaMatches: hipaaResults.matches.length,
      hipaaScore: hipaaScore,
    });

    // If HIPAA score is low, also check general privacy patterns
    let finalScore = hipaaScore;
    let analysisType = 'HIPAA-specific';

    if (hipaaScore < 30) {
      console.log('📋 [Level 1 Analysis] Low HIPAA score, analyzing general privacy patterns...');
      const generalResults = this.analyzeGeneralPrivacyPatterns(content);

      // Use the higher score between HIPAA and general privacy
      const generalScore = this.scoreGeneralPrivacyMatches(generalResults.matches);
      console.log('✅ [Level 1 Analysis] General privacy analysis complete:', {
        generalMatches: generalResults.matches.length,
        generalScore: generalScore,
      });

      if (generalScore > hipaaScore) {
        // Replace findings with general privacy findings for better relevance
        findings.length = 0; // Clear HIPAA findings
        findings.push(...generalResults.findings);
        allMatches.push(...generalResults.matches);
        finalScore = generalScore;
        analysisType = 'General Privacy';
      }
    }

    const foundPatterns = allMatches.length;
    const totalPatterns = this.getTotalRequiredPatterns();
    const confidence = Math.min(95, 70 + (foundPatterns / totalPatterns) * 25);
    const processingTime = Date.now() - startTime;

    console.log('🎯 [Level 1 Analysis] Pattern matching analysis complete:', {
      analysisType: analysisType,
      score: finalScore,
      foundPatterns: foundPatterns,
      totalPatterns: totalPatterns,
      confidence: confidence,
      processingTime: processingTime + 'ms',
      matchDetails: allMatches.slice(0, 5).map((match) => ({
        requirement: match.requirement,
        text: match.text.substring(0, 50) + '...',
        position: match.position,
      })),
    });

    // Determine the method string based on analysis type
    let method:
      | 'Basic Phrase Matching'
      | 'HIPAA-specific Pattern Matching'
      | 'General Privacy Pattern Matching';
    if (analysisType === 'HIPAA-specific') {
      method = 'HIPAA-specific Pattern Matching';
    } else if (analysisType === 'General Privacy') {
      method = 'General Privacy Pattern Matching';
    } else {
      method = 'Basic Phrase Matching';
    }

    return {
      level: 1 as const,
      method,
      score: finalScore,
      foundPatterns,
      totalPatterns,
      confidence,
      findings,
      processingTime,
    };
  }

  /**
   * Analyzes HIPAA-specific patterns (sections + content)
   */
  private static analyzeHIPAAPatterns(content: string): {
    matches: PatternMatch[];
    findings: Level1Finding[];
  } {
    const sectionResults = this.analyzeSections(content);
    const contentResults = this.analyzeContent(content);

    return {
      matches: [...sectionResults.matches, ...contentResults.matches],
      findings: [...sectionResults.findings, ...contentResults.findings],
    };
  }

  /**
   * Analyzes general privacy patterns for non-healthcare websites
   */
  private static analyzeGeneralPrivacyPatterns(content: string): {
    matches: PatternMatch[];
    findings: Level1Finding[];
  } {
    const matches: PatternMatch[] = [];
    const findings: Level1Finding[] = [];

    for (const [, patternConfig] of Object.entries(GENERAL_PRIVACY_PATTERNS)) {
      const patternMatches = this.findPatterns(content, patternConfig.patterns);

      if (patternMatches.length > 0) {
        // Found privacy content
        matches.push(...patternMatches);
        findings.push({
          type: 'exact_match',
          content: patternMatches[0].text,
          location: patternMatches[0].position,
          requirement: patternConfig.description,
          severity: HipaaSeverity.INFO,
          pattern: patternMatches[0].pattern.source,
        });
      } else {
        // Missing privacy content (less severe than missing HIPAA)
        findings.push({
          type: 'missing_pattern',
          content: '',
          location: -1,
          requirement: patternConfig.description,
          severity: HipaaSeverity.LOW, // General privacy missing is less critical
          pattern: patternConfig.patterns[0].source,
        });
      }
    }

    return { matches, findings };
  }

  /**
   * Calculates score for general privacy patterns
   */
  private static scoreGeneralPrivacyMatches(matches: PatternMatch[]): number {
    const totalGeneralPatterns = Object.keys(GENERAL_PRIVACY_PATTERNS).length;
    const uniqueRequirements = new Set(matches.map((match) => match.requirement));

    if (totalGeneralPatterns === 0) return 0;

    return Math.round((uniqueRequirements.size / totalGeneralPatterns) * 100);
  }

  /**
   * Analyzes required HIPAA sections
   */
  private static analyzeSections(content: string): {
    matches: PatternMatch[];
    findings: Level1Finding[];
  } {
    const matches: PatternMatch[] = [];
    const findings: Level1Finding[] = [];

    for (const [sectionName, sectionConfig] of Object.entries(HIPAA_REQUIRED_SECTIONS)) {
      const sectionMatches = this.findPatterns(content, sectionConfig.patterns);

      if (sectionMatches.length > 0) {
        // Found the section
        matches.push(...sectionMatches);
        findings.push({
          type: 'exact_match',
          content: sectionMatches[0].text,
          location: sectionMatches[0].position,
          requirement: sectionConfig.description,
          severity: HipaaSeverity.INFO,
          pattern: sectionMatches[0].pattern.source,
        });
      } else if (sectionConfig.required) {
        // Missing required section
        findings.push({
          type: 'missing_pattern',
          content: '',
          location: -1,
          requirement: sectionConfig.description,
          severity: this.getSeverityForMissingSection(sectionName),
          pattern: sectionConfig.patterns[0].source,
        });
      }
    }

    return { matches, findings };
  }

  /**
   * Analyzes required HIPAA content
   */
  private static analyzeContent(content: string): {
    matches: PatternMatch[];
    findings: Level1Finding[];
  } {
    const matches: PatternMatch[] = [];
    const findings: Level1Finding[] = [];

    for (const [contentName, contentConfig] of Object.entries(HIPAA_REQUIRED_CONTENT)) {
      const contentMatches = this.findPatterns(content, contentConfig.patterns);

      if (contentMatches.length > 0) {
        // Found the content
        matches.push(...contentMatches);
        findings.push({
          type: 'exact_match',
          content: contentMatches[0].text,
          location: contentMatches[0].position,
          requirement: contentConfig.description,
          severity: HipaaSeverity.INFO,
          pattern: contentMatches[0].pattern.source,
        });
      } else {
        // Missing content
        findings.push({
          type: 'missing_pattern',
          content: '',
          location: -1,
          requirement: contentConfig.description,
          severity: this.getSeverityForMissingContent(contentName),
          pattern: contentConfig.patterns[0].source,
        });
      }
    }

    return { matches, findings };
  }

  /**
   * Finds all matches for a specific pattern in content
   */
  private static findPatternMatches(content: string, pattern: RegExp): PatternMatch[] {
    const matches: PatternMatch[] = [];
    const globalPattern = new RegExp(
      pattern.source,
      pattern.flags.includes('g') ? pattern.flags : pattern.flags + 'g',
    );

    let match;
    while ((match = globalPattern.exec(content)) !== null) {
      matches.push({
        pattern: pattern,
        text: match[0],
        position: match.index,
        requirement: this.getRequirementForPattern(pattern),
        weight: this.getWeightForPattern(pattern),
      });

      // Prevent infinite loop for zero-length matches
      if (match.index === globalPattern.lastIndex) {
        globalPattern.lastIndex++;
      }
    }

    return matches;
  }

  /**
   * Gets the requirement description for a pattern
   */
  private static getRequirementForPattern(pattern: RegExp): string {
    // Check HIPAA sections
    for (const [, sectionConfig] of Object.entries(HIPAA_REQUIRED_SECTIONS)) {
      if (sectionConfig.patterns.some((p) => p.source === pattern.source)) {
        return sectionConfig.description;
      }
    }

    // Check HIPAA content
    for (const [, contentConfig] of Object.entries(HIPAA_REQUIRED_CONTENT)) {
      if (contentConfig.patterns.some((p) => p.source === pattern.source)) {
        return contentConfig.description;
      }
    }

    // Check general privacy patterns
    for (const [, privacyConfig] of Object.entries(GENERAL_PRIVACY_PATTERNS)) {
      if (privacyConfig.patterns.some((p) => p.source === pattern.source)) {
        return privacyConfig.description;
      }
    }

    return 'Privacy policy requirement';
  }

  /**
   * Gets the weight for a pattern
   */
  private static getWeightForPattern(pattern: RegExp): number {
    // Check HIPAA sections
    for (const [, sectionConfig] of Object.entries(HIPAA_REQUIRED_SECTIONS)) {
      if (sectionConfig.patterns.some((p) => p.source === pattern.source)) {
        return sectionConfig.weight;
      }
    }

    // Check HIPAA content
    for (const [, contentConfig] of Object.entries(HIPAA_REQUIRED_CONTENT)) {
      if (contentConfig.patterns.some((p) => p.source === pattern.source)) {
        return contentConfig.weight;
      }
    }

    // Check general privacy patterns
    for (const [, privacyConfig] of Object.entries(GENERAL_PRIVACY_PATTERNS)) {
      if (privacyConfig.patterns.some((p) => p.source === pattern.source)) {
        return privacyConfig.weight;
      }
    }

    return 1;
  }

  /**
   * Gets total number of required patterns for scoring
   */
  private static getTotalRequiredPatterns(): number {
    const sectionCount = Object.keys(HIPAA_REQUIRED_SECTIONS).length;
    const contentCount = Object.keys(HIPAA_REQUIRED_CONTENT).length;
    return sectionCount + contentCount;
  }

  /**
   * Determines severity for missing sections
   */
  private static getSeverityForMissingSection(sectionName: string): HipaaSeverity {
    const criticalSections = ['NOTICE_HEADER', 'INDIVIDUAL_RIGHTS', 'USES_DISCLOSURES'];
    const highSections = ['CONTACT_INFO'];

    if (criticalSections.includes(sectionName)) {
      return HipaaSeverity.CRITICAL;
    } else if (highSections.includes(sectionName)) {
      return HipaaSeverity.HIGH;
    } else {
      return HipaaSeverity.MEDIUM;
    }
  }

  /**
   * Determines severity for missing content
   */
  private static getSeverityForMissingContent(contentName: string): HipaaSeverity {
    const criticalContent = ['PHI_DEFINITION', 'USES_DISCLOSURES'];
    const highContent = ['INDIVIDUAL_RIGHTS_SPECIFIC'];

    if (criticalContent.includes(contentName)) {
      return HipaaSeverity.CRITICAL;
    } else if (highContent.includes(contentName)) {
      return HipaaSeverity.HIGH;
    } else {
      return HipaaSeverity.MEDIUM;
    }
  }

  /**
   * Compiles and caches regex patterns for performance
   */
  static getCompiledPattern(patternString: string, flags = 'gi'): RegExp {
    const cacheKey = `${patternString}:${flags}`;

    if (!this.patternCache.has(cacheKey)) {
      this.patternCache.set(cacheKey, new RegExp(patternString, flags));
    }

    return this.patternCache.get(cacheKey)!;
  }

  /**
   * Clears the pattern cache (useful for testing)
   */
  static clearCache(): void {
    this.patternCache.clear();
  }

  // ============================================================================
  // ENHANCED PATTERN ANALYSIS WITH CONTENT CITATION
  // ============================================================================

  /**
   * Performs enhanced pattern analysis with content citations
   */
  static performEnhancedAnalysis(content: string): {
    findings: EnhancedFinding[];
    citations: ContentCitation[];
    overallScore: number;
  } {
    console.log('🔍 [Enhanced Analysis] Starting enhanced pattern analysis with citations');

    const findings: EnhancedFinding[] = [];
    const allCitations: ContentCitation[] = [];

    // Analyze each specific HIPAA check
    for (const [checkId, checkConfig] of Object.entries(HIPAA_SPECIFIC_CHECKS)) {
      const checkResult = this.analyzeSpecificCheck(content, checkId, checkConfig);
      findings.push(...checkResult.findings);
      allCitations.push(...checkResult.citations);
    }

    // Analyze enhanced HIPAA sections
    for (const [sectionName, sectionConfig] of Object.entries(ENHANCED_HIPAA_SECTIONS)) {
      const sectionResult = this.analyzeEnhancedSection(content, sectionName, sectionConfig);
      findings.push(...sectionResult.findings);
      allCitations.push(...sectionResult.citations);
    }

    // Calculate overall score based on findings
    const overallScore = this.calculateEnhancedScore(findings);

    console.log('✅ [Enhanced Analysis] Enhanced analysis complete:', {
      totalFindings: findings.length,
      totalCitations: allCitations.length,
      overallScore,
    });

    return {
      findings,
      citations: ContentCitationExtractor.mergeCitations(allCitations),
      overallScore,
    };
  }

  /**
   * Analyze a specific HIPAA check with detailed findings
   */
  private static analyzeSpecificCheck(
    content: string,
    checkId: string,
    checkConfig: HipaaSpecificCheckConfig,
  ): { findings: EnhancedFinding[]; citations: ContentCitation[] } {
    const findings: EnhancedFinding[] = [];
    const citations: ContentCitation[] = [];

    // Extract citations for this check's patterns
    const checkCitations = ContentCitationExtractor.extractCitations(content, checkConfig.name);

    if (checkCitations.length > 0) {
      // Found evidence - create positive finding
      findings.push({
        id: `${checkId}-POSITIVE`,
        type: 'positive',
        title: `${checkConfig.name} Found`,
        description: checkConfig.successMessage,
        hipaaRequirement: checkConfig.name,
        hipaaReference: checkConfig.hipaaReference,
        severity: 'info',
        citations: checkCitations,
        confidence: Math.max(...checkCitations.map((c: { confidence: number }) => c.confidence)),
        category: HipaaCheckCategory.HIPAA_SPECIFIC,
        checkId,
      });
      citations.push(...checkCitations);
    } else {
      // Missing required element - create missing finding
      findings.push({
        id: `${checkId}-MISSING`,
        type: 'missing',
        title: `${checkConfig.name} Missing`,
        description: checkConfig.failureMessage,
        hipaaRequirement: checkConfig.name,
        hipaaReference: checkConfig.hipaaReference,
        severity: checkConfig.severity,
        citations: [],
        recommendation: {
          action: checkConfig.recommendation.action,
          implementation: checkConfig.recommendation.implementation,
          exampleLanguage: checkConfig.recommendation.exampleLanguage,
          effort: checkConfig.recommendation.effort,
          impact: checkConfig.recommendation.impact,
          timeline: checkConfig.recommendation.timeline,
          priority:
            checkConfig.severity === 'critical' ? 10 : checkConfig.severity === 'high' ? 8 : 6,
          businessImpact: {
            riskReduction: `Eliminates ${checkConfig.severity} compliance risk`,
            complianceImprovement:
              checkConfig.severity === 'critical' ? 20 : checkConfig.severity === 'high' ? 15 : 10,
            auditPreparedness: 'Improves readiness for regulatory audits',
          },
        },
        confidence: 95, // High confidence in missing elements
        category: HipaaCheckCategory.HIPAA_SPECIFIC,
        checkId,
      });
    }

    return { findings, citations };
  }

  /**
   * Analyze enhanced HIPAA sections with detailed findings
   */
  private static analyzeEnhancedSection(
    content: string,
    sectionName: string,
    sectionConfig: EnhancedHipaaSectionConfig,
  ): { findings: EnhancedFinding[]; citations: ContentCitation[] } {
    const findings: EnhancedFinding[] = [];
    const citations: ContentCitation[] = [];

    // Extract citations for this section's patterns
    const sectionCitations = ContentCitationExtractor.extractCitations(
      content,
      sectionConfig.description,
    );

    if (sectionCitations.length > 0) {
      // Found evidence - create positive finding
      findings.push({
        id: `ENHANCED-${sectionName}-POSITIVE`,
        type: 'positive',
        title: `${sectionConfig.description} Present`,
        description: `Found evidence of ${sectionConfig.description.toLowerCase()} in your privacy policy.`,
        hipaaRequirement: sectionConfig.description,
        hipaaReference: sectionConfig.hipaaReference,
        severity: 'info',
        citations: sectionCitations,
        confidence: Math.max(...sectionCitations.map((c: { confidence: number }) => c.confidence)),
        category: HipaaCheckCategory.HIPAA_SPECIFIC,
        checkId: `ENHANCED-${sectionName}`,
      });
      citations.push(...sectionCitations);
    } else if (sectionConfig.required) {
      // Missing required section - create missing finding
      findings.push({
        id: `ENHANCED-${sectionName}-MISSING`,
        type: 'missing',
        title: `${sectionConfig.description} Missing`,
        description: `Your privacy policy lacks ${sectionConfig.description.toLowerCase()}, which is required by HIPAA.`,
        hipaaRequirement: sectionConfig.description,
        hipaaReference: sectionConfig.hipaaReference,
        severity: 'high',
        citations: [],
        recommendation: {
          action: `Add ${sectionConfig.description.toLowerCase()} to your privacy policy`,
          implementation: `Include a section that explains ${sectionConfig.description.toLowerCase()}`,
          exampleLanguage: sectionConfig.exampleLanguage,
          effort: 'moderate',
          impact: 'high',
          timeline: '30 minutes',
          priority: 8,
          businessImpact: {
            riskReduction: 'Reduces regulatory compliance risk',
            complianceImprovement: 15,
            auditPreparedness: 'Demonstrates comprehensive HIPAA understanding',
          },
        },
        confidence: 90,
        category: HipaaCheckCategory.HIPAA_SPECIFIC,
        checkId: `ENHANCED-${sectionName}`,
      });
    }

    return { findings, citations };
  }

  /**
   * Calculate enhanced score based on findings
   */
  private static calculateEnhancedScore(findings: EnhancedFinding[]): number {
    const totalChecks =
      Object.keys(HIPAA_SPECIFIC_CHECKS).length +
      Object.values(ENHANCED_HIPAA_SECTIONS).filter((s) => s.required).length;

    const passedChecks = findings.filter((f) => f.type === 'positive').length;

    if (totalChecks === 0) return 0;

    return Math.round((passedChecks / totalChecks) * 100);
  }
}
