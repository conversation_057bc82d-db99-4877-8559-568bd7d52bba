import { HIPAA_SECURITY_CONSTANTS } from '../constants';
import { HipaaSecurityScanConfig, ScannerConfig } from '../types';

export class ScannerConfigService {
  private static instance: ScannerConfigService;
  private scannerConfig: ScannerConfig;

  private constructor() {
    this.scannerConfig = this.loadConfiguration();
  }

  public static getInstance(): ScannerConfigService {
    if (!ScannerConfigService.instance) {
      ScannerConfigService.instance = new ScannerConfigService();
    }
    return ScannerConfigService.instance;
  }

  private loadConfiguration(): ScannerConfig {
    return {
      sslLabsApiUrl: process.env.SSL_LABS_API_URL,
      maxConcurrentRequests: parseInt(process.env.HIPAA_MAX_CONCURRENT_REQUESTS || '3'),
      requestTimeout: parseInt(process.env.HIPAA_REQUEST_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.HIPAA_RETRY_ATTEMPTS || '3'),
      userAgent:
        process.env.HIPAA_USER_AGENT || HIPAA_SECURITY_CONSTANTS.DEFAULT_SCAN_CONFIG.userAgent,
      nucleiPath: process.env.NUCLEI_PATH || 'nuclei',
      nucleiTemplatesPath: process.env.NUCLEI_TEMPLATES_PATH || './nuclei-templates',
    };
  }

  public getScannerConfig(): ScannerConfig {
    return { ...this.scannerConfig };
  }

  public getDefaultScanConfig(targetUrl: string): HipaaSecurityScanConfig {
    return {
      targetUrl,
      maxPages: parseInt(process.env.HIPAA_MAX_PAGES || '15'),
      scanDepth: parseInt(process.env.HIPAA_SCAN_DEPTH || '2'),
      timeout: parseInt(process.env.HIPAA_SCAN_TIMEOUT || '1800000'), // 30 minutes
      enableVulnerabilityScanning: process.env.HIPAA_ENABLE_VULN_SCAN !== 'false',
      enableSSLAnalysis: process.env.HIPAA_ENABLE_SSL_ANALYSIS !== 'false',
      enableContentAnalysis: process.env.HIPAA_ENABLE_CONTENT_ANALYSIS !== 'false',
      userAgent: this.scannerConfig.userAgent,
      customHeaders: this.parseCustomHeaders(process.env.HIPAA_CUSTOM_HEADERS),
    };
  }

  private parseCustomHeaders(headersString?: string): Record<string, string> | undefined {
    if (!headersString) return undefined;

    try {
      return JSON.parse(headersString);
    } catch (error) {
      console.warn('Failed to parse custom headers from environment variable:', error);
      return undefined;
    }
  }

  public updateScannerConfig(updates: Partial<ScannerConfig>): void {
    this.scannerConfig = { ...this.scannerConfig, ...updates };
  }

  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate SSL Labs API URL if provided
    if (this.scannerConfig.sslLabsApiUrl) {
      try {
        new URL(this.scannerConfig.sslLabsApiUrl);
      } catch (error) {
        errors.push('Invalid SSL Labs API URL');
      }
    }

    // Validate Nuclei configuration
    if (!this.scannerConfig.nucleiPath) {
      errors.push('Nuclei path is required');
    }

    if (!this.scannerConfig.nucleiTemplatesPath) {
      errors.push('Nuclei templates path is required');
    }

    // Validate numeric values
    if (
      this.scannerConfig.maxConcurrentRequests < 1 ||
      this.scannerConfig.maxConcurrentRequests > 10
    ) {
      errors.push('Max concurrent requests must be between 1 and 10');
    }

    if (this.scannerConfig.requestTimeout < 5000 || this.scannerConfig.requestTimeout > 300000) {
      errors.push('Request timeout must be between 5 seconds and 5 minutes');
    }

    if (this.scannerConfig.retryAttempts < 0 || this.scannerConfig.retryAttempts > 5) {
      errors.push('Retry attempts must be between 0 and 5');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  public getEnvironmentInfo(): Record<string, string | undefined> {
    return {
      SSL_LABS_API_URL: process.env.SSL_LABS_API_URL,
      NUCLEI_PATH: process.env.NUCLEI_PATH,
      NUCLEI_TEMPLATES_PATH: process.env.NUCLEI_TEMPLATES_PATH,
      NUCLEI_ENABLED: process.env.NUCLEI_ENABLED,
      HIPAA_MAX_PAGES: process.env.HIPAA_MAX_PAGES,
      HIPAA_SCAN_DEPTH: process.env.HIPAA_SCAN_DEPTH,
      HIPAA_SCAN_TIMEOUT: process.env.HIPAA_SCAN_TIMEOUT,
      HIPAA_ENABLE_VULN_SCAN: process.env.HIPAA_ENABLE_VULN_SCAN,
      HIPAA_ENABLE_SSL_ANALYSIS: process.env.HIPAA_ENABLE_SSL_ANALYSIS,
      HIPAA_ENABLE_CONTENT_ANALYSIS: process.env.HIPAA_ENABLE_CONTENT_ANALYSIS,
      HIPAA_USER_AGENT: process.env.HIPAA_USER_AGENT,
      HIPAA_CUSTOM_HEADERS: process.env.HIPAA_CUSTOM_HEADERS ? '[REDACTED]' : undefined,
    };
  }

  public async testConnectivity(): Promise<{
    nucleiAvailable: boolean;
    sslLabsConnected: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    let nucleiAvailable = false;
    let sslLabsConnected = false;

    // Test Nuclei availability
    try {
      const { NucleiClient } = await import('./nuclei-client');
      const nucleiClient = new NucleiClient();
      nucleiAvailable = await nucleiClient.isAvailable();

      if (!nucleiAvailable) {
        errors.push('Nuclei is not available or not properly installed');
      }
    } catch (error) {
      errors.push(
        `Failed to test Nuclei availability: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }

    // Test SSL Labs connectivity if configured
    if (this.scannerConfig.sslLabsApiUrl) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(`${this.scannerConfig.sslLabsApiUrl}/info`, {
          method: 'GET',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        sslLabsConnected = response.ok;
        if (!sslLabsConnected) {
          errors.push(`SSL Labs API returned status: ${response.status}`);
        }
      } catch (error) {
        errors.push(
          `Failed to connect to SSL Labs API: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }
    } else {
      sslLabsConnected = true; // Not configured, so consider it "connected"
    }

    return {
      nucleiAvailable,
      sslLabsConnected,
      errors,
    };
  }

  public getRecommendedSettings(targetUrl: string): {
    scanConfig: HipaaSecurityScanConfig;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    const scanConfig = this.getDefaultScanConfig(targetUrl);

    try {
      const url = new URL(targetUrl);

      // Adjust settings based on URL characteristics
      if (url.protocol === 'http:') {
        recommendations.push('Target URL uses HTTP - consider testing HTTPS redirect behavior');
        scanConfig.enableSSLAnalysis = false;
      }

      // Adjust scan depth and pages based on domain type
      if (url.hostname.includes('localhost') || url.hostname.includes('127.0.0.1')) {
        recommendations.push(
          'Local development environment detected - using conservative scan settings',
        );
        scanConfig.maxPages = Math.min(scanConfig.maxPages, 10);
        scanConfig.scanDepth = Math.min(scanConfig.scanDepth, 1);
        scanConfig.timeout = Math.min(scanConfig.timeout, 600000); // 10 minutes
      }

      // Production environment recommendations
      if (
        !url.hostname.includes('localhost') &&
        !url.hostname.includes('127.0.0.1') &&
        !url.hostname.includes('dev')
      ) {
        recommendations.push(
          'Production environment detected - ensure proper authorization before scanning',
        );
        recommendations.push('Consider running scans during maintenance windows');
      }

      // HIPAA-specific recommendations
      recommendations.push(
        'Ensure target application handles PHI data before running security scan',
      );
      recommendations.push('Review scan results for any exposed sensitive data patterns');
      recommendations.push('Document scan results for HIPAA compliance audit trail');
    } catch (error) {
      recommendations.push('Invalid target URL provided');
    }

    return {
      scanConfig,
      recommendations,
    };
  }
}
