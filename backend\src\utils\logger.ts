/**
 * @file Custom logger utility for the backend application.
 * Provides leveled logging (DEBUG, INFO, WARN, ERROR) with timestamps and optional metadata.
 * Logs are output to the console.
 * DEBUG and INFO logs are suppressed when NODE_ENV is 'test'.
 */

/**
 * Defines the available log levels.
 */
enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

/**
 * Gets the current timestamp in ISO string format.
 * @returns {string} The current timestamp as an ISO string.
 */
const getCurrentTimestamp = (): string => new Date().toISOString();

/**
 * Formats a log message with timestamp, level, and optional metadata.
 *
 * @param {LogLevel} level - The log level.
 * @param {string} message - The main log message.
 * @param {Record<string, unknown>} [metadata] - Optional structured data to include in the log.
 * @returns {string} The formatted log entry string.
 */
const formatMessage = (
  level: LogLevel,
  message: string,
  metadata?: Record<string, unknown>,
): string => {
  let logEntry = `${getCurrentTimestamp()} [${level}] - ${message}`;
  if (metadata && Object.keys(metadata).length > 0) {
    try {
      logEntry += ` - ${JSON.stringify(metadata)}`;
    } catch (e) {
      // Fallback if metadata cannot be stringified
      logEntry += ` - { SerializationError: "Failed to stringify metadata" }`;
    }
  }
  return logEntry;
};

/**
 * Logger object with methods for different log levels.
 */
const logger = {
  /**
   * Logs a debug message. Suppressed if NODE_ENV is 'test'.
   * @param {string} message - The message to log.
   * @param {Record<string, unknown>} [metadata] - Optional metadata.
   */
  debug: (message: string, metadata?: Record<string, unknown>): void => {
    if (process.env.NODE_ENV !== 'test') {
      // Avoid noisy logs during tests
      console.debug(formatMessage(LogLevel.DEBUG, message, metadata));
    }
  },
  /**
   * Logs an informational message. Suppressed if NODE_ENV is 'test'.
   * @param {string} message - The message to log.
   * @param {Record<string, unknown>} [metadata] - Optional metadata.
   */
  info: (message: string, metadata?: Record<string, unknown>): void => {
    if (process.env.NODE_ENV !== 'test') {
      console.info(formatMessage(LogLevel.INFO, message, metadata));
    }
  },
  /**
   * Logs a warning message.
   * @param {string} message - The message to log.
   * @param {Record<string, unknown>} [metadata] - Optional metadata.
   */
  warn: (message: string, metadata?: Record<string, unknown>): void => {
    console.warn(formatMessage(LogLevel.WARN, message, metadata));
  },
  /**
   * Logs an error message.
   * @param {string} message - The message to log.
   * @param {Record<string, unknown>} [metadata] - Optional metadata.
   */
  error: (message: string, metadata?: Record<string, unknown>): void => {
    console.error(formatMessage(LogLevel.ERROR, message, metadata));
  },
};

export default logger;
