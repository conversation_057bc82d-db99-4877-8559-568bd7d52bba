// Core HIPAA Security Types
export interface HipaaSecurityScanConfig {
  targetUrl: string;
  maxPages: number;
  scanDepth: number;
  timeout: number;
  enableVulnerabilityScanning: boolean;
  enableSSLAnalysis: boolean;
  enableContentAnalysis: boolean;
  userAgent?: string;
  customHeaders?: Record<string, string>;
}

export interface HipaaSecurityScanResult {
  scanId: string;
  targetUrl: string;
  scanTimestamp: Date;
  scanDuration: number;
  overallScore: number;
  riskLevel: RiskLevel;

  // Test results
  passedTests: HipaaTestDetail[];
  failedTests: HipaaTestFailure[];

  // Category summaries
  technicalSafeguards: CategoryResult;
  administrativeSafeguards: CategoryResult;
  organizationalSafeguards: CategoryResult;
  physicalSafeguards: CategoryResult;

  // Vulnerabilities
  vulnerabilities: VulnerabilityResult[];

  // Metadata
  pagesScanned: string[];
  toolsUsed: string[];
  scanStatus: ScanStatus;
  errorMessage?: string;
}

export interface HipaaTestDetail {
  testId: string;
  testName: string;
  hipaaSection: string;
  description: string;
  category: HipaaCategory;
  passed: true;
  evidence: string;
  pagesTested: string[];
  timestamp: Date;
}

export interface HipaaTestFailure {
  testId: string;
  testName: string;
  hipaaSection: string;
  description: string;
  category: HipaaCategory;
  passed: false;
  failureReason: string;
  riskLevel: RiskLevel;
  failureEvidence: FailureEvidence[];
  recommendedAction: string;
  remediationPriority: number;
  timestamp: Date;
}

export interface FailureEvidence {
  location: string;
  elementType: ElementType;
  actualCode: string;
  expectedBehavior: string;
  lineNumber?: number;
  context: string;
}

export interface VulnerabilityResult {
  id: string;
  type: string;
  severity: Severity;
  location: string;
  description: string;
  evidence: Record<string, unknown>;
  cweId?: number;
  owaspCategory?: string;
  remediationGuidance: string;
}

export interface CategoryResult {
  category: HipaaCategory;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  score: number;
  riskLevel: RiskLevel;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
}

// Enums and Union Types
export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type HipaaCategory = 'technical' | 'administrative' | 'organizational' | 'physical';
export type ElementType =
  | 'header'
  | 'html'
  | 'javascript'
  | 'response'
  | 'cookie'
  | 'form'
  | 'connection';
export type Severity = 'critical' | 'high' | 'medium' | 'low' | 'info';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// Test Configuration Types
export interface TestConfig {
  testId: string;
  testName: string;
  hipaaSection: string;
  category: HipaaCategory;
  description: string;
  enabled: boolean;
  timeout: number;
  retryCount: number;
  dependencies: string[];
}

// Scanner Configuration
export interface ScannerConfig {
  sslLabsApiUrl?: string;
  maxConcurrentRequests: number;
  requestTimeout: number;
  retryAttempts: number;
  userAgent: string;
  nucleiPath?: string;
  nucleiTemplatesPath?: string;
}

// Nuclei Client Types
export interface NucleiScanOptions {
  targetUrl: string;
  timeout: number;
  maxTemplates?: number;
  tags?: string[];
  excludeTags?: string[];
  severity?: string[];
}

export interface NucleiScanResult {
  success: boolean;
  output: string;
  error: string;
}

export interface NucleiVulnerability {
  id: string;
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  location: string;
  description: string;
  evidence: {
    nucleiTemplate: string;
    templateInfo: Record<string, unknown>;
    matchedAt: string;
    extractedResults: Record<string, unknown>[];
  };
  remediationGuidance: string;
  tags: string[];
  reference: string[];
}

// SSL Analysis Types
export interface SSLAnalysisResult {
  isValid: boolean;
  daysRemaining: number;
  issuer: string;
  subject: string;
  tlsVersion: string;
  cipherSuite: string;
  keyExchange: string;
  serverSignature: string;
  hipaaCompliant: boolean;
  vulnerabilities: SSLVulnerability[];
  grade: string;
}

export interface SSLVulnerability {
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  remediation: string;
}

// Content Analysis Types
export interface ContentAnalysisResult {
  hasEPHI: boolean;
  ephiMatches: EPHIMatch[];
  securityHeaders: SecurityHeaderResult[];
  formAnalysis: FormAnalysisResult[];
  scriptAnalysis: ScriptAnalysisResult;
  cookieAnalysis: CookieAnalysisResult[];
}

export interface EPHIMatch {
  pattern: string;
  match: string;
  location: string;
  context: string;
  lineNumber: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
}

export interface SecurityHeaderResult {
  header: string;
  present: boolean;
  value?: string;
  secure: boolean;
  recommendation?: string;
}

export interface FormAnalysisResult {
  formId: string;
  action: string;
  method: string;
  hasCSRFProtection: boolean;
  hasSSLAction: boolean;
  sensitiveFields: string[];
  securityIssues: string[];
}

export interface ScriptAnalysisResult {
  externalScripts: string[];
  inlineScripts: number;
  potentialXSSVulns: string[];
  securityIssues: string[];
}

export interface CookieAnalysisResult {
  name: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: string | null;
  domain: string;
  path: string;
  securityIssues: string[];
}

// Note: Duplicate interface definitions removed - using the ones defined above at lines 14-101
