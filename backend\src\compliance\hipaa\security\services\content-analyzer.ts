import { parse, HTMLElement } from 'node-html-parser';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';
import {
  ContentAnalysisResult,
  EPHIMatch,
  SecurityHeaderResult,
  FormAnalysisResult,
  ScriptAnalysisResult,
  CookieAnalysisResult,
} from '../types';

export class ContentAnalyzer {
  analyzeContent(
    html: string,
    url: string,
    responseHeaders: Record<string, string>,
  ): ContentAnalysisResult {
    const document = parse(html);

    return {
      hasEPHI: this.detectEPHI(html).length > 0,
      ephiMatches: this.detectEPHI(html),
      securityHeaders: this.analyzeSecurityHeaders(responseHeaders),
      formAnalysis: this.analyzeForms(document, url),
      scriptAnalysis: this.analyzeScripts(document),
      cookieAnalysis: this.analyzeCookies(responseHeaders),
    };
  }

  private detectEPHI(content: string): EPHIMatch[] {
    const matches: EPHIMatch[] = [];
    const lines = content.split('\n');

    HIPAA_SECURITY_CONSTANTS.EPHI_PATTERNS.forEach((pattern, _index) => {
      lines.forEach((line, lineNumber) => {
        const match = pattern.exec(line);
        if (match) {
          matches.push({
            pattern: pattern.source,
            match: match[0],
            location: `Line ${lineNumber + 1}`,
            context: this.getContext(lines, lineNumber, 2),
            lineNumber: lineNumber + 1,
            riskLevel: this.assessEPHIRisk(match[0]),
          });
        }
      });
    });

    return matches;
  }

  private getContext(lines: string[], lineNumber: number, contextSize: number): string {
    const start = Math.max(0, lineNumber - contextSize);
    const end = Math.min(lines.length, lineNumber + contextSize + 1);
    return lines.slice(start, end).join('\n');
  }

  private assessEPHIRisk(match: string): 'critical' | 'high' | 'medium' | 'low' {
    // SSN patterns are critical
    if (/\d{3}-\d{2}-\d{4}/.test(match)) return 'critical';

    // Medical record numbers are high risk
    if (/medical\s+record|mrn/i.test(match)) return 'high';

    // Patient IDs are medium risk
    if (/patient\s+id/i.test(match)) return 'medium';

    return 'low';
  }

  private analyzeSecurityHeaders(headers: Record<string, string>): SecurityHeaderResult[] {
    const results: SecurityHeaderResult[] = [];

    HIPAA_SECURITY_CONSTANTS.SECURITY_HEADERS.forEach((headerName) => {
      const headerValue = headers[headerName];
      const present = !!headerValue;

      results.push({
        header: headerName,
        present,
        value: headerValue,
        secure: present ? this.isSecureHeaderValue(headerName, headerValue) : false,
        recommendation: present ? undefined : this.getHeaderRecommendation(headerName),
      });
    });

    return results;
  }

  private isSecureHeaderValue(headerName: string, value: string): boolean {
    switch (headerName) {
      case 'strict-transport-security':
        return (
          value.includes('max-age=') &&
          parseInt(value.match(/max-age=(\d+)/)?.[1] || '0') >= 31536000
        );
      case 'content-security-policy':
        return !value.includes("'unsafe-inline'") && !value.includes("'unsafe-eval'");
      case 'x-frame-options':
        return value.toLowerCase() === 'deny' || value.toLowerCase() === 'sameorigin';
      case 'x-content-type-options':
        return value.toLowerCase() === 'nosniff';
      case 'x-xss-protection':
        return value === '1; mode=block';
      default:
        return true;
    }
  }

  private getHeaderRecommendation(headerName: string): string {
    const recommendations: Record<string, string> = {
      'strict-transport-security':
        'Add HSTS header: Strict-Transport-Security: max-age=31536000; includeSubDomains',
      'content-security-policy': 'Add CSP header to prevent XSS attacks',
      'x-frame-options': 'Add X-Frame-Options: DENY to prevent clickjacking',
      'x-content-type-options': 'Add X-Content-Type-Options: nosniff',
      'x-xss-protection': 'Add X-XSS-Protection: 1; mode=block',
      'referrer-policy': 'Add Referrer-Policy: strict-origin-when-cross-origin',
    };

    return recommendations[headerName] || `Add ${headerName} header for security`;
  }

  private analyzeForms(document: HTMLElement, _baseUrl: string): FormAnalysisResult[] {
    const forms = document.querySelectorAll('form');
    const results: FormAnalysisResult[] = [];

    forms.forEach((form: HTMLElement, index: number) => {
      const action = form.getAttribute('action') || '';
      const method = form.getAttribute('method') || 'GET';
      const formId = form.getAttribute('id') || `form-${index}`;

      // Check for CSRF protection
      const csrfToken = form.querySelector('input[name*="csrf"], input[name*="token"]');

      // Check if action uses HTTPS
      const hasSSLAction = action.startsWith('https://') || action.startsWith('/') || action === '';

      // Find sensitive fields
      const sensitiveFields = this.findSensitiveFields(form);

      // Identify security issues
      const securityIssues: string[] = [];
      if (!csrfToken) securityIssues.push('Missing CSRF protection');
      if (!hasSSLAction && action.startsWith('http://'))
        securityIssues.push('Form submits over HTTP');
      if (method.toUpperCase() === 'GET' && sensitiveFields.length > 0) {
        securityIssues.push('Sensitive data submitted via GET method');
      }

      results.push({
        formId,
        action,
        method: method.toUpperCase(),
        hasCSRFProtection: !!csrfToken,
        hasSSLAction,
        sensitiveFields,
        securityIssues,
      });
    });

    return results;
  }

  private findSensitiveFields(form: HTMLElement): string[] {
    const sensitivePatterns = [
      'password',
      'ssn',
      'social',
      'credit',
      'card',
      'cvv',
      'pin',
      'medical',
      'health',
      'patient',
      'diagnosis',
      'treatment',
    ];

    const inputs = form.querySelectorAll('input, textarea, select');
    const sensitiveFields: string[] = [];

    inputs.forEach((input: HTMLElement) => {
      const name = input.getAttribute('name') || '';
      const id = input.getAttribute('id') || '';
      const type = input.getAttribute('type') || '';

      if (
        type === 'password' ||
        sensitivePatterns.some(
          (pattern) => name.toLowerCase().includes(pattern) || id.toLowerCase().includes(pattern),
        )
      ) {
        sensitiveFields.push(name || id || type);
      }
    });

    return sensitiveFields;
  }

  private analyzeScripts(document: HTMLElement): ScriptAnalysisResult {
    const scripts = document.querySelectorAll('script');
    const externalScripts: string[] = [];
    let inlineScripts = 0;
    const potentialXSSVulns: string[] = [];
    const securityIssues: string[] = [];

    scripts.forEach((script: HTMLElement) => {
      const src = script.getAttribute('src');

      if (src) {
        externalScripts.push(src);

        // Check for insecure external scripts
        if (src.startsWith('http://')) {
          securityIssues.push(`Insecure external script: ${src}`);
        }
      } else {
        inlineScripts++;

        // Check for potential XSS vulnerabilities in inline scripts
        const content = script.innerHTML;
        if (
          content.includes('eval(') ||
          content.includes('innerHTML') ||
          content.includes('document.write')
        ) {
          potentialXSSVulns.push('Potentially dangerous inline script detected');
        }
      }
    });

    return {
      externalScripts,
      inlineScripts,
      potentialXSSVulns,
      securityIssues,
    };
  }

  private analyzeCookies(headers: Record<string, string>): CookieAnalysisResult[] {
    const setCookieHeaders = Object.entries(headers)
      .filter(([key]) => key.toLowerCase() === 'set-cookie')
      .map(([, value]) => value);

    const results: CookieAnalysisResult[] = [];

    setCookieHeaders.forEach((cookieHeader) => {
      const cookies = Array.isArray(cookieHeader) ? cookieHeader : [cookieHeader];

      cookies.forEach((cookie) => {
        const parsed = this.parseCookie(cookie);
        results.push(parsed);
      });
    });

    return results;
  }

  private parseCookie(cookieString: string): CookieAnalysisResult {
    const parts = cookieString.split(';').map((part) => part.trim());
    const [nameValue] = parts;
    const [name] = nameValue.split('=');

    const attributes = parts.slice(1).reduce(
      (acc, part) => {
        const [key, value] = part.split('=');
        acc[key.toLowerCase()] = value || 'true';
        return acc;
      },
      {} as Record<string, string>,
    );

    const secure = 'secure' in attributes;
    const httpOnly = 'httponly' in attributes;
    const sameSite = typeof attributes.samesite === 'string' ? attributes.samesite : null;
    const domain = typeof attributes.domain === 'string' ? attributes.domain : '';
    const path = typeof attributes.path === 'string' ? attributes.path : '/';

    const securityIssues: string[] = [];
    if (!secure) securityIssues.push('Cookie not marked as Secure');
    if (!httpOnly) securityIssues.push('Cookie not marked as HttpOnly');
    if (!sameSite) securityIssues.push('Cookie missing SameSite attribute');

    return {
      name,
      secure,
      httpOnly,
      sameSite,
      domain,
      path,
      securityIssues,
    };
  }
}
