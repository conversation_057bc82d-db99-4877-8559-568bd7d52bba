/**
 * @file Configures and initializes Keycloak for authentication and sets up Express session middleware.
 * This file centralizes Keycloak adapter setup and session management for the backend application.
 * It uses environment variables for Keycloak client configuration and session secrets.
 */
import session from 'express-session';
import Keycloak from 'keycloak-connect';
import { env } from '@lib/env'; // Adjusted path

// Session store setup (in-memory for development)
// IMPORTANT: For production, use a persistent store like connect-redis, connect-mongo, etc.
const memoryStore = new session.MemoryStore();

/**
 * Express session middleware configured for the application.
 * Uses an in-memory store for development purposes and a secret from environment variables.
 * IMPORTANT: For production, replace `memoryStore` with a persistent session store (e.g., connect-redis).
 */
export const sessionMiddleware = session({
  secret: env.SESSION_SECRET, // Now properly typed and validated
  resave: false,
  saveUninitialized: true,
  store: memoryStore,
});

// This object mirrors the structure of keycloak.json, using camelCase for type compatibility
// It does NOT contain the client secret.
// This object mirrors the structure of keycloak.json
// It does NOT contain the client secret.
// Note: We have to use quoted properties for Keycloak config due to hyphens
// Using type assertion to avoid TypeScript errors with additional properties
// Manually construct the Keycloak URL to avoid interpolation issues
const keycloakServerUrl = `http://localhost:${env.KEYCLOAK_PORT}/auth`;

/**
 * Configuration object mimicking the structure of a keycloak.json file.
 * This is used to provide client-specific details to the Keycloak adapter.
 * It does NOT contain the client secret directly if using adapter options for that;
 * however, for confidential clients, 'credentials.secret' can be set here too.
 * The 'auth-server-url' is constructed manually to ensure correct port and path.
 */
const keycloakJsonConfig = {
  realm: env.KEYCLOAK_REALM,
  'auth-server-url': keycloakServerUrl, // Use manually constructed URL
  'ssl-required': 'none', // For local HTTP development
  resource: env.KEYCLOAK_CLIENT_ID_BACKEND, // Client ID for the backend
  'bearer-only': true, // Set to true for API-only backend that accepts Bearer tokens
  'confidential-port': 0,
  // Set credentials for the client
  credentials: {
    secret: env.KEYCLOAK_CLIENT_SECRET_BACKEND,
  },
  'public-client': false, // This must be false for confidential clients
} as Keycloak.KeycloakConfig;

// Adapter-specific options, including the store and the secret for confidential clients
/**
 * Adapter-specific options for the Keycloak Connect instance.
 * Includes the session store and the client secret for confidential clients.
 * This is the preferred way to provide the client secret when not using a keycloak.json file directly.
 */
const adapterOpts = {
  store: memoryStore,
  secret: env.KEYCLOAK_CLIENT_SECRET_BACKEND, // Client secret is provided here
};

// Initialize Keycloak with adapter options (including secret) and JSON-like config
/**
 * Initialized Keycloak Connect instance.
 * This instance is used as middleware in Express to protect routes and manage authentication.
 * It's configured using both adapter-specific options (like the session store and client secret)
 * and a JSON-like configuration object for client details.
 */
export const keycloak = new Keycloak(adapterOpts, keycloakJsonConfig);
