/**
 * @file Initializes and exports the Knex database instance for the application.
 * It reads the appropriate configuration based on the NODE_ENV environment variable.
 */
import knex, { Knex } from 'knex';
import config from '../../knexfile'; // Adjusted path
import { env } from '@lib/env';

// Determine the environment
const environment = env.NODE_ENV || 'development';
const knexConfig = config[environment];

if (!knexConfig) {
  throw new Error(`Knex configuration for environment '${environment}' not found.`);
}

const db: Knex = knex(knexConfig);

export default db;
