/**
 * Enhanced Error Handler Component
 * Comprehensive error handling with recovery options
 */

import React, { useState } from 'react';
import { ScanError, EnhancedScanResult } from '../types';

interface ErrorHandlerProps {
  error: ScanError;
  partialResults?: EnhancedScanResult;
  onRetry: () => void;
  onViewPartialResults?: () => void;
  onReportIssue?: (error: ScanError) => void;
  onDismiss: () => void;
}

export const EnhancedErrorHandler: React.FC<ErrorHandlerProps> = ({
  error,
  partialResults,
  onRetry,
  onViewPartialResults,
  onReportIssue,
  onDismiss
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [reportSent, setReportSent] = useState(false);

  const getErrorIcon = (type: string): string => {
    switch (type) {
      case 'network': return '🌐';
      case 'timeout': return '⏱️';
      case 'validation': return '⚠️';
      case 'server': return '🔧';
      default: return '❌';
    }
  };

  const getErrorColor = (type: string): string => {
    switch (type) {
      case 'network': return 'blue';
      case 'timeout': return 'orange';
      case 'validation': return 'yellow';
      case 'server': return 'red';
      default: return 'gray';
    }
  };

  const getRecoveryOptions = (error: ScanError) => {
    const options = [];

    if (error.recoverable) {
      options.push({
        label: 'Retry Scan',
        action: onRetry,
        style: 'primary' as const,
        description: 'Attempt to run the scan again'
      });
    }

    if (partialResults && partialResults.reliability.dataCompleteness > 50) {
      options.push({
        label: 'View Partial Results',
        action: onViewPartialResults,
        style: 'secondary' as const,
        description: `${partialResults.reliability.dataCompleteness}% of tests completed successfully`
      });
    }

    if (error.type === 'timeout') {
      options.push({
        label: 'Retry with Extended Timeout',
        action: () => onRetry(), // This would trigger a retry with longer timeouts
        style: 'secondary' as const,
        description: 'Increase timeout limits and retry'
      });
    }

    if (error.type === 'network') {
      options.push({
        label: 'Check Network & Retry',
        action: onRetry,
        style: 'secondary' as const,
        description: 'Verify network connectivity and try again'
      });
    }

    return options;
  };

  const handleReportIssue = () => {
    if (onReportIssue) {
      onReportIssue(error);
      setReportSent(true);
    }
  };

  const recoveryOptions = getRecoveryOptions(error);

  return (
    <div className={`error-container error-${error.type}`}>
      {/* Error Header */}
      <div className="error-header">
        <div className="error-icon" style={{ color: getErrorColor(error.type) }}>
          {getErrorIcon(error.type)}
        </div>
        <div className="error-info">
          <h3 className="error-title">Scan Issue Detected</h3>
          <p className="error-message">{error.message}</p>
          <div className="error-meta">
            <span className="error-type">{error.type.toUpperCase()}</span>
            <span className="error-time">
              {error.timestamp.toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>

      {/* Error Details */}
      {error.details && (
        <div className="error-details">
          <button 
            className="toggle-details"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? '▼' : '▶'} {showDetails ? 'Hide' : 'Show'} Technical Details
          </button>
          
          {showDetails && (
            <div className="details-content">
              <pre className="error-details-text">{error.details}</pre>
              
              {error.context && (
                <div className="error-context">
                  <h4>Context</h4>
                  <ul>
                    {error.context.phase && (
                      <li><strong>Phase:</strong> {error.context.phase}</li>
                    )}
                    {error.context.test && (
                      <li><strong>Test:</strong> {error.context.test}</li>
                    )}
                    {error.context.url && (
                      <li><strong>URL:</strong> {error.context.url}</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Suggested Action */}
      <div className="suggested-action">
        <h4>💡 Suggested Action</h4>
        <p>{error.suggestedAction}</p>
      </div>

      {/* Partial Results Info */}
      {partialResults && (
        <div className="partial-results-info">
          <h4>📊 Partial Results Available</h4>
          <div className="partial-stats">
            <div className="stat">
              <span className="stat-value">{partialResults.reliability.dataCompleteness}%</span>
              <span className="stat-label">Data Complete</span>
            </div>
            <div className="stat">
              <span className="stat-value">{partialResults.passedTests}</span>
              <span className="stat-label">Tests Passed</span>
            </div>
            <div className="stat">
              <span className="stat-value">{partialResults.failedTests}</span>
              <span className="stat-label">Tests Failed</span>
            </div>
            <div className="stat">
              <span className="stat-value">{partialResults.skippedTests}</span>
              <span className="stat-label">Tests Skipped</span>
            </div>
          </div>
          <p className="partial-note">
            While the scan didn't complete fully, you can still review the results 
            from the tests that did complete successfully.
          </p>
        </div>
      )}

      {/* Recovery Options */}
      {recoveryOptions.length > 0 && (
        <div className="recovery-options">
          <h4>🔧 Recovery Options</h4>
          <div className="options-grid">
            {recoveryOptions.map((option, index) => (
              <div key={index} className="recovery-option">
                <button 
                  className={`btn btn-${option.style}`}
                  onClick={option.action}
                >
                  {option.label}
                </button>
                <p className="option-description">{option.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Troubleshooting Tips */}
      <div className="troubleshooting-tips">
        <h4>🛠️ Troubleshooting Tips</h4>
        <div className="tips-by-type">
          {error.type === 'timeout' && (
            <div className="tip-section">
              <h5>Timeout Issues</h5>
              <ul>
                <li>Check if the target website is responding slowly</li>
                <li>Verify your internet connection is stable</li>
                <li>Some SSL checks may take longer for complex certificates</li>
                <li>Consider scanning during off-peak hours</li>
              </ul>
            </div>
          )}
          
          {error.type === 'network' && (
            <div className="tip-section">
              <h5>Network Issues</h5>
              <ul>
                <li>Verify the target URL is accessible from your network</li>
                <li>Check if there are firewall restrictions</li>
                <li>Ensure the website is not blocking automated requests</li>
                <li>Try accessing the site manually in a browser first</li>
              </ul>
            </div>
          )}
          
          {error.type === 'validation' && (
            <div className="tip-section">
              <h5>Validation Issues</h5>
              <ul>
                <li>Verify the URL format is correct (include https://)</li>
                <li>Check if the website requires authentication</li>
                <li>Ensure the site is publicly accessible</li>
                <li>Some sites may block automated scanning tools</li>
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="error-actions">
        <div className="primary-actions">
          {recoveryOptions.length > 0 ? (
            recoveryOptions.slice(0, 2).map((option, index) => (
              <button 
                key={index}
                className={`btn btn-${option.style}`}
                onClick={option.action}
              >
                {option.label}
              </button>
            ))
          ) : (
            <button className="btn btn-secondary" onClick={onDismiss}>
              Dismiss
            </button>
          )}
        </div>
        
        <div className="secondary-actions">
          {onReportIssue && (
            <button 
              className="btn btn-link"
              onClick={handleReportIssue}
              disabled={reportSent}
            >
              {reportSent ? '✓ Issue Reported' : '📧 Report Issue'}
            </button>
          )}
          
          <button className="btn btn-link" onClick={onDismiss}>
            Dismiss
          </button>
        </div>
      </div>

      {/* Help Contact */}
      <div className="help-contact">
        <p>
          Need additional help? Contact our support team with error ID: 
          <code>{error.timestamp.getTime().toString(36)}</code>
        </p>
      </div>
    </div>
  );
};

export default EnhancedErrorHandler;
