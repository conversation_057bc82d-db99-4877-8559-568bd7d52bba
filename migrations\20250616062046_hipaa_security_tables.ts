import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
  // HIPAA Security Scan Results Table
  await knex.schema.createTable('hipaa_security_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('target_url', 2048).notNullable();
    table.timestamp('scan_timestamp').defaultTo(knex.fn.now());
    table.integer('scan_duration'); // in milliseconds
    table.decimal('overall_score', 5, 2);
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']);
    table.specificType('pages_scanned', 'text[]'); // array of scanned pages
    table.specificType('tools_used', 'text[]'); // array of tools used
    table.enu('scan_status', ['pending', 'running', 'completed', 'failed']).defaultTo('pending');
    table.text('error_message');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());

    // Indexes
    table.index('target_url');
    table.index('scan_timestamp');
    table.index('risk_level');
  });

  // HIPAA Security Test Results Table
  await knex.schema.createTable('hipaa_security_test_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('hipaa_security_scans').onDelete('CASCADE');
    table.string('test_id', 100).notNullable();
    table.string('test_name', 255).notNullable();
    table.string('hipaa_section', 50).notNullable(); // e.g., '164.312(a)(1)'
    table.enu('category', ['technical', 'administrative', 'organizational', 'physical']).notNullable();
    table.boolean('passed').notNullable();
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']);
    table.text('description');
    table.text('failure_reason');
    table.jsonb('evidence'); // stores evidence data
    table.specificType('pages_tested', 'text[]');
    table.integer('remediation_priority').checkBetween([1, 5]);
    table.text('recommended_action');
    table.timestamp('created_at').defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('passed');
    table.index('category');
  });

  // HIPAA Security Vulnerabilities Table
  await knex.schema.createTable('hipaa_security_vulnerabilities', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('hipaa_security_scans').onDelete('CASCADE');
    table.string('vulnerability_type', 100).notNullable();
    table.enu('severity', ['critical', 'high', 'medium', 'low', 'info']);
    table.string('location', 2048).notNullable();
    table.text('description').notNullable();
    table.jsonb('evidence');
    table.integer('cwe_id');
    table.string('owasp_category', 100);
    table.text('remediation_guidance');
    table.timestamp('created_at').defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('severity');
  });

  // HIPAA Security Failure Evidence Table
  await knex.schema.createTable('hipaa_security_failure_evidence', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('test_result_id').notNullable().references('id').inTable('hipaa_security_test_results').onDelete('CASCADE');
    table.string('location', 2048).notNullable();
    table.enu('element_type', ['header', 'html', 'javascript', 'response', 'cookie', 'form']);
    table.text('actual_code').notNullable();
    table.text('expected_behavior').notNullable();
    table.integer('line_number');
    table.text('context');
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });
}


export async function down(knex: Knex): Promise<void> {
  // Drop tables in reverse order due to foreign key constraints
  await knex.schema.dropTableIfExists('hipaa_security_failure_evidence');
  await knex.schema.dropTableIfExists('hipaa_security_vulnerabilities');
  await knex.schema.dropTableIfExists('hipaa_security_test_results');
  await knex.schema.dropTableIfExists('hipaa_security_scans');
}

