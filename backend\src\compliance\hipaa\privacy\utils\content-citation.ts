// backend/src/compliance/hipaa/privacy/utils/content-citation.ts

import { ContentCitation } from '../types';

// Re-export for compatibility
export { ContentCitation };

export class ContentCitationExtractor {
  extractCitations(content: string, context: string): ContentCitation[] {
    const citations: ContentCitation[] = [];

    // Extract HIPAA section references
    const hipaaPattern = /164\.\d+\([a-z]\)(\(\d+\))?/gi;
    const matches = content.match(hipaaPattern);

    if (matches) {
      matches.forEach((match) => {
        const startIndex = content.indexOf(match);
        citations.push({
          originalText: match,
          location: {
            startIndex,
            endIndex: startIndex + match.length,
            section: 'HIPAA Security Rule',
          },
          context: {
            beforeText: content.substring(Math.max(0, startIndex - 100), startIndex),
            afterText: content.substring(
              startIndex + match.length,
              startIndex + match.length + 100,
            ),
            fullSentence: context,
          },
          confidence: 90,
          relevanceScore: 80,
        });
      });
    }

    // Extract other regulatory references
    const regulatoryPatterns = [/NIST\s+\d+/gi, /ISO\s+\d+/gi, /SOC\s+\d+/gi];

    regulatoryPatterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach((match) => {
          const startIndex = content.indexOf(match);
          citations.push({
            originalText: match,
            location: {
              startIndex,
              endIndex: startIndex + match.length,
              section: 'Regulatory Standard',
            },
            context: {
              beforeText: content.substring(Math.max(0, startIndex - 100), startIndex),
              afterText: content.substring(
                startIndex + match.length,
                startIndex + match.length + 100,
              ),
              fullSentence: context,
            },
            confidence: 70,
            relevanceScore: 60,
          });
        });
      }
    });

    return citations;
  }

  static mergeCitations(citations: ContentCitation[]): ContentCitation[] {
    // Remove duplicates and merge similar citations
    const merged = new Map<string, ContentCitation>();

    citations.forEach((citation) => {
      const key = `${citation.originalText}-${citation.location.section}`;
      if (!merged.has(key) || merged.get(key)!.confidence < citation.confidence) {
        merged.set(key, citation);
      }
    });

    return Array.from(merged.values());
  }

  static extractCitations(content: string, context: string): ContentCitation[] {
    const extractor = new ContentCitationExtractor();
    return extractor.extractCitations(content, context);
  }
}
