import type { Knex } from 'knex';

/**
 * Fix HIPAA Score Types Migration
 * Changes integer score and confidence fields to decimal to support floating-point values
 */

export async function up(knex: Knex): Promise<void> {
  // Fix hipaa_scans table
  await knex.schema.alterTable('hipaa_scans', (table) => {
    table.decimal('overall_score', 5, 2).notNullable().alter(); // Change from integer to decimal
  });

  // Fix hipaa_check_results table
  await knex.schema.alterTable('hipaa_check_results', (table) => {
    table.decimal('confidence', 5, 2).notNullable().alter(); // Change from integer to decimal
    table.decimal('overall_score', 5, 2).alter(); // Change from integer to decimal
  });

  // Fix hipaa_analysis_levels table
  await knex.schema.alterTable('hipaa_analysis_levels', (table) => {
    table.decimal('score', 5, 2).notNullable().alter(); // Change from integer to decimal
    table.decimal('confidence', 5, 2).notNullable().alter(); // Change from integer to decimal
  });

  // Fix hipaa_evidence table
  await knex.schema.alterTable('hipaa_evidence', (table) => {
    table.decimal('relevance', 5, 2).notNullable().alter(); // Change from integer to decimal
  });
}

export async function down(knex: Knex): Promise<void> {
  // Revert hipaa_scans table
  await knex.schema.alterTable('hipaa_scans', (table) => {
    table.integer('overall_score').notNullable().alter();
  });

  // Revert hipaa_check_results table
  await knex.schema.alterTable('hipaa_check_results', (table) => {
    table.integer('confidence').notNullable().alter();
    table.integer('overall_score').alter();
  });

  // Revert hipaa_analysis_levels table
  await knex.schema.alterTable('hipaa_analysis_levels', (table) => {
    table.integer('score').notNullable().alter();
    table.integer('confidence').notNullable().alter();
  });

  // Revert hipaa_evidence table
  await knex.schema.alterTable('hipaa_evidence', (table) => {
    table.integer('relevance').notNullable().alter();
  });
}
