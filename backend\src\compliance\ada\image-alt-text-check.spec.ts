import { checkImageAltText } from './image-alt-text-check';
import { AdaCheckResult } from './types';

// Mock global fetch unconditionally
global.fetch = jest.fn();

describe('checkImageAltText', () => {
  const targetUrl = 'https://example.com/ada-test';

  beforeEach(() => {
    (global.fetch as jest.Mock).mockClear();
  });

  const mockFetchSuccess = (htmlContent: string) => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      text: async () => htmlContent,
      status: 200,
      statusText: 'OK',
    });
  };

  const mockFetchFailure = (status = 500, statusText = 'Server Error') => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      text: async () => `Error: ${statusText}`,
      status,
      statusText,
    });
  };

  it('should pass and report no images if none are found', async () => {
    const html = '<html><body><p>No images here.</p></body></html>';
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(true);
    expect(result.name).toBe('Image Alt Text Presence');
    expect(result.checkId).toBe('ADA-IMG-001');
    expect(result.totalElements).toBe(0);
    expect(result.nonCompliantElements).toBe(0);
    expect(result.details).toEqual([
      expect.objectContaining({
        element: 'document',
        message: 'No images found on the page.',
        passed: true,
        severity: 'info',
      }),
    ]);
    expect(result.description).toBe('No images found on the page to check for alt text.');
  });

  it('should pass if all images have non-empty alt attributes', async () => {
    const html =
      '<html><body><img src="img1.jpg" alt="Good alt text"><img src="img2.png" alt="Another good one"></body></html>';
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(true);
    expect(result.totalElements).toBe(2);
    expect(result.nonCompliantElements).toBe(0);
    expect(result.details.length).toBe(2);
    result.details.forEach((detail) => expect(detail.passed).toBe(true));
    expect(result.description).toContain('All 2 image(s) on the page have alt attributes.');
  });

  it('should pass if all images are explicitly decorative (alt="")', async () => {
    const html =
      '<html><body><img src="deco1.gif" alt=""><img src="deco2.svg" alt=""></body></html>';
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(true);
    expect(result.totalElements).toBe(2);
    expect(result.nonCompliantElements).toBe(0);
    expect(result.details.length).toBe(2);
    result.details.forEach((detail) => {
      expect(detail.passed).toBe(true);
      expect(detail.severity).toBe('info');
      expect(detail.message).toContain('considered decorative');
    });
  });

  it('should fail if an image is missing the alt attribute', async () => {
    const html = '<html><body><img src="missing_alt.jpg"></body></html>';
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(false);
    expect(result.totalElements).toBe(1);
    expect(result.nonCompliantElements).toBe(1);
    expect(result.details[0]).toEqual(
      expect.objectContaining({
        passed: false,
        severity: 'high',
        message: expect.stringContaining('is missing the alt attribute'),
        altText: null,
      }),
    );
    expect(result.description).toContain('Found 1 image(s) without appropriate alt text');
  });

  it('should fail if an image alt attribute contains only whitespace', async () => {
    const html = '<html><body><img src="whitespace_alt.png" alt="   "></body></html>';
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(false);
    expect(result.totalElements).toBe(1);
    expect(result.nonCompliantElements).toBe(1);
    expect(result.details[0]).toEqual(
      expect.objectContaining({
        passed: false,
        severity: 'medium',
        message: expect.stringContaining('has an alt attribute with only whitespace'),
        altText: '   ',
      }),
    );
  });

  it('should correctly identify mixed compliance scenarios', async () => {
    const html = `
      <html><body>
        <img src="good.jpg" alt="Good">
        <img src="missing.jpg">
        <img src="decorative.gif" alt="">
        <img src="whitespace.png" alt=" ">
      </body></html>
    `;
    mockFetchSuccess(html);
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(false);
    expect(result.totalElements).toBe(4);
    expect(result.nonCompliantElements).toBe(2);
    expect(result.details.length).toBe(4);

    const goodImgDetail = result.details.find((d) => d.imgSrc === 'good.jpg');
    expect(goodImgDetail?.passed).toBe(true);

    const missingImgDetail = result.details.find((d) => d.imgSrc === 'missing.jpg');
    expect(missingImgDetail?.passed).toBe(false);
    expect(missingImgDetail?.severity).toBe('high');

    const decorativeImgDetail = result.details.find((d) => d.imgSrc === 'decorative.gif');
    expect(decorativeImgDetail?.passed).toBe(true);
    expect(decorativeImgDetail?.severity).toBe('info');

    const whitespaceImgDetail = result.details.find((d) => d.imgSrc === 'whitespace.png');
    expect(whitespaceImgDetail?.passed).toBe(false);
    expect(whitespaceImgDetail?.severity).toBe('medium');
  });

  it('should handle fetch failure gracefully', async () => {
    mockFetchFailure(404, 'Not Found');
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(false);
    expect(result.description).toContain('Error checking image alt text: Failed to fetch');
    expect(result.details[0]).toEqual(
      expect.objectContaining({
        passed: false,
        severity: 'critical',
        element: 'page_processing_error',
        message: expect.stringContaining(
          'Failed to process page for image alt text check: Failed to fetch',
        ),
      }),
    );
  });

  it('should handle fetch throwing an error', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network connection failed'));
    const result: AdaCheckResult = await checkImageAltText(targetUrl);

    expect(result.passed).toBe(false);
    expect(result.description).toContain(
      'Error checking image alt text: Network connection failed',
    );
    expect(result.details[0]).toEqual(
      expect.objectContaining({
        passed: false,
        severity: 'critical',
        element: 'page_processing_error',
        message: 'Failed to process page for image alt text check: Network connection failed',
      }),
    );
  });
});
