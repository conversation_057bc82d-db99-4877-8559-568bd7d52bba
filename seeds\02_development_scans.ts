import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries from the 'scans' table
  await knex('scans').del();

  // Find a user to associate the scan with
  const user = await knex('users').where({ email: '<EMAIL>' }).first();

  if (!user) {
    console.warn('Could not <NAME_EMAIL> to seed scans. Skipping scan seeding.');
    return;
  }

  // Inserts seed entries
  await knex('scans').insert([
    {
      // id will be generated by default
      user_id: user.id,
      url: 'https://example.com',
      status: 'completed', // 'pending', 'running', 'completed', 'failed'
      standards_scanned: JSON.stringify(['gdpr', 'wcag']),
      summary_report: JSON.stringify({
        score: 85,
        issues: { critical: 1, high: 2, medium: 5, low: 10 },
        passed_checks: 150,
        failed_checks: 18,
      }),
      // created_at will use default
      completed_at: knex.fn.now(),
    },
    {
      user_id: user.id,
      url: 'https://another-example.org',
      status: 'pending',
      standards_scanned: JSON.stringify(['hipaa']),
      // created_at will use default
    },
  ]);

  console.log('Seeded development scans successfully.');
}
