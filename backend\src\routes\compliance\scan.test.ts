/**
 * Integration tests for compliance scan API routes
 */
import request from 'supertest';
import express, { Request, Response, NextFunction } from 'express';

// Set a longer timeout for all tests
jest.setTimeout(30000);

// Mock Zod validation schemas first
jest.mock('../../lib/validators/scan-validators', () => {
  // Helper to create a Zod-like error object for mocks
  const createMockZodError = (errors = [{ message: 'Invalid input', path: [] as string[] }]) => ({
    format: () => ({ _errors: errors.map((e) => e.message) }),
    flatten: () => ({
      fieldErrors: errors.reduce((acc, err) => {
        if (err.path && err.path.length > 0) {
          // @ts-expect-error - Mocking Zod validation error // Simple mock, ignore complex typing
          acc[err.path.join('.')] = (acc[err.path.join('.')] || []).concat(err.message);
        }
        return acc;
      }, {}),
      formErrors: errors.filter((e) => !e.path || e.path.length === 0).map((e) => e.message),
    }),
    issues: errors,
  });

  return {
    CreateScanSchema: {
      safeParse: jest.fn().mockImplementation((data) => {
        if (!data)
          return {
            success: false,
            error: createMockZodError([{ message: 'No data provided', path: [] as string[] }]),
          };
        if (data.url && (typeof data.url !== 'string' || !data.url.startsWith('http'))) {
          return {
            success: false,
            error: createMockZodError([{ message: 'Invalid URL format', path: ['url' as string] }]),
          };
        }
        if (!data.standards || !Array.isArray(data.standards) || data.standards.length === 0) {
          return {
            success: false,
            error: createMockZodError([
              {
                message: 'Standards array is required and cannot be empty',
                path: ['standards' as string],
              },
            ]),
          };
        }
        // Simulate Zod: return the parsed data on success
        return { success: true, data: data };
      }),
    },
    GetScanParamsSchema: {
      safeParse: jest.fn().mockImplementation((params) => {
        // Simulate validation for scanId
        if (
          params &&
          typeof params.scanId === 'string' &&
          (params.scanId === 'valid-scan-id' ||
            params.scanId.startsWith('mock-scan-id-') ||
            params.scanId.match(
              /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/,
            ))
        ) {
          return { success: true, data: { scanId: params.scanId } };
        }
        return {
          success: false,
          error: createMockZodError([{ message: 'Invalid scan ID format', path: ['scanId'] }]),
        };
      }),
    },
  };
});

// Mock constants
jest.mock('../../lib/constants', () => ({
  ERROR_MESSAGES: {
    INVALID_INPUT: 'Invalid input',
    UNAUTHORIZED: 'Unauthorized',
    SCAN_CREATION_FAILED: 'Failed to create scan',
    FETCH_SCANS_FAILED: 'Failed to fetch scans',
    SCAN_NOT_FOUND: 'Scan not found',
    FETCH_SCAN_FAILED: 'Failed to fetch scan',
    INVALID_SCAN_ID_FORMAT: 'Invalid scan ID format',
  },
}));

// Import after mocking
import { ERROR_MESSAGES } from '../../lib/constants';

// Mock database
const mockDbQuery = {
  where: jest.fn().mockReturnThis(),
  first: jest.fn().mockResolvedValue(null),
  select: jest.fn().mockResolvedValue([]),
  insert: jest.fn().mockResolvedValue([1]),
  update: jest.fn().mockResolvedValue(1),
  returning: jest.fn().mockReturnThis(),
};

const mockDb = jest.fn().mockImplementation(() => mockDbQuery);
jest.mock('../../lib/db', () => mockDb);

// Also mock the database service
jest.mock('../../services/db-service', () => ({
  connectToDatabase: jest.fn().mockResolvedValue(true),
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

// Mock error classes
/**
 * Mock implementation of UserNotFoundError for testing
 */
class MockUserNotFoundError extends Error {
  /**
   * Creates a new MockUserNotFoundError
   * @param {string} message - The error message
   */
  constructor(message: string) {
    super(message);
    this.name = 'UserNotFoundError';
  }
}

/**
 * Mock implementation of ScanProcessingError for testing
 */
class MockScanProcessingError extends Error {
  scanId: string;

  /**
   * Creates a new MockScanProcessingError
   * @param {string} message - The error message
   * @param {string} scanId - ID of the scan that had an error
   */
  constructor(message: string, scanId: string) {
    super(message);
    this.name = 'ScanProcessingError';
    this.scanId = scanId;
  }
}

// Mock ScanService
const mockInitiateNewScan = jest.fn();
jest.mock('../../services/scan-service', () => ({
  __esModule: true,
  default: {
    initiateNewScan: mockInitiateNewScan,
  },
  UserNotFoundError: MockUserNotFoundError,
  ScanProcessingError: MockScanProcessingError,
}));

// Mock Keycloak
const mockProtect = jest
  .fn()
  .mockImplementation(
    () =>
      (req: Request & { kauth?: Record<string, unknown> }, _res: Response, next: NextFunction) => {
        // Add Keycloak auth context to request
        req.kauth = {
          grant: {
            access_token: {
              content: {
                sub: 'test-keycloak-id',
              },
            },
          },
        };
        next();
      },
  );

jest.mock('../../config/keycloak-config', () => ({
  getKeycloakInstance: jest.fn().mockReturnValue({
    protect: mockProtect,
  }),
}));

// Import the module under test after all mocks are set up
// eslint-disable-next-line @typescript-eslint/no-var-requires
const scanRoutes = require('./scan').default;

describe('Scan API Routes', () => {
  let app: express.Application;
  // Use a consistent scan ID that our mock will recognize as valid
  const validScanId = 'valid-scan-id';
  const validScanData = {
    url: 'https://example.com',
    standards: ['hipaa', 'gdpr'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    app.use(express.json());
    app.use('/', scanRoutes);
    mockDb.mockImplementation(() => mockDbQuery);
  });

  describe('POST /', () => {
    it('should create a new scan successfully', async () => {
      const mockScanResult = {
        id: 'test-scan-id',
        url: validScanData.url,
        status: 'PENDING',
        findings: [],
      };

      mockInitiateNewScan.mockResolvedValue(mockScanResult);

      const response = await request(app)
        .post('/')
        .send(validScanData)
        .expect('Content-Type', /json/)
        .expect(201);

      expect(response.body).toEqual(mockScanResult);
    });

    it('should return 400 for invalid input', async () => {
      const response = await request(app)
        .post('/')
        .send({ url: 'not-a-url', standards: [] })
        .expect(400);

      expect(response.body.message).toBe(ERROR_MESSAGES.INVALID_INPUT);
    });

    it('should return 404 when user is not found', async () => {
      const userNotFoundError = new MockUserNotFoundError('User not found');
      mockInitiateNewScan.mockRejectedValue(userNotFoundError);

      const response = await request(app).post('/').send(validScanData).expect(404);

      expect(response.body.message).toBe(userNotFoundError.message);
    });

    it('should return 500 when scan processing fails', async () => {
      const scanProcessingError = new MockScanProcessingError(
        'Scan processing failed',
        'test-scan-id',
      );
      mockInitiateNewScan.mockRejectedValue(scanProcessingError);

      const response = await request(app).post('/').send(validScanData).expect(500);

      expect(response.body.message).toBe(scanProcessingError.message);
      expect(response.body.scanId).toBe(scanProcessingError.scanId);
    });
  });

  /**
   * Tests for listing all scans for a user
   */
  describe('GET /', () => {
    it('should return all scans for a user', async () => {
      const mockScans = [
        { id: 'scan-1', url: 'https://example1.com', status: 'COMPLETED' },
        { id: 'scan-2', url: 'https://example2.com', status: 'PENDING' },
      ];

      // Configure mock database for this test
      mockDb.mockImplementation(() => ({
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue(mockScans),
      }));

      const response = await request(app).get('/').expect(200);

      expect(response.body).toEqual(mockScans);
    });
  });

  /**
   * Tests for retrieving a specific scan by ID
   */
  describe('GET /:scanId', () => {
    it('should return a specific scan with findings', async () => {
      const mockScan = {
        id: validScanId,
        url: 'https://example.com',
        status: 'COMPLETED',
      };

      const mockFindings = [
        { id: 'finding-1', scanId: validScanId, rule: 'rule-1', severity: 'HIGH' },
      ];

      mockDb.mockImplementation((table: string) => {
        if (table === 'scans') {
          return {
            where: jest.fn().mockReturnThis(),
            first: jest.fn().mockResolvedValue(mockScan),
          };
        } else if (table === 'compliance_findings') {
          return {
            where: jest.fn().mockReturnThis(),
            select: jest.fn().mockResolvedValue(mockFindings),
          };
        }
        return {};
      });

      const response = await request(app).get(`/${validScanId}`).expect(200);

      expect(response.body).toEqual({
        ...mockScan,
        findings: mockFindings,
      });
    });

    it('should return 404 when scan is not found', async () => {
      mockDb.mockImplementation(() => ({
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null),
      }));

      const response = await request(app).get(`/${validScanId}`).expect(404);

      expect(response.body.message).toBe(ERROR_MESSAGES.SCAN_NOT_FOUND);
    });
  });
});
