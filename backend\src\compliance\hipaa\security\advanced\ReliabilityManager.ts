/**
 * PART 7: Advanced Reliability Management System
 * Implements circuit breakers, retry logic, and graceful degradation
 */

import { EventEmitter } from 'events';
import { PerformanceMonitor } from './PerformanceMonitor';

export interface ReliabilityConfig {
  circuitBreaker: {
    failureThreshold: number;
    recoveryTimeout: number;
    halfOpenMaxCalls: number;
  };
  retry: {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
    jitterEnabled: boolean;
  };
  timeout: {
    default: number;
    ssl: number;
    network: number;
    database: number;
  };
  gracefulDegradation: {
    enabled: boolean;
    minDataCompleteness: number;
    fallbackModes: string[];
  };
  healthCheck: {
    interval: number;
    timeout: number;
    retries: number;
  };
}

export interface ServiceHealthDetails {
  version?: string;
  endpoint?: string;
  lastError?: string;
  connectionCount?: number;
  memoryUsage?: number;
  cpuUsage?: number;
  diskUsage?: number;
  [key: string]: string | number | boolean | undefined;
}

export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
  errorRate: number;
  uptime: number;
  details: ServiceHealthDetails;
}

export interface ReliabilityMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  retriedRequests: number;
  circuitBreakerTrips: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  lastIncident: Date | null;
}

export class ReliabilityManager extends EventEmitter {
  private config: ReliabilityConfig;
  private performanceMonitor: PerformanceMonitor;
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private serviceHealth: Map<string, ServiceHealth> = new Map();
  private metrics: ReliabilityMetrics;
  private healthCheckIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: ReliabilityConfig, performanceMonitor: PerformanceMonitor) {
    super();
    this.config = config;
    this.performanceMonitor = performanceMonitor;
    this.metrics = this.initializeMetrics();
    this.startHealthChecks();
  }

  // Circuit Breaker Implementation
  async executeWithCircuitBreaker<T>(
    serviceKey: string,
    operation: () => Promise<T>,
    fallback?: () => Promise<T>,
  ): Promise<T> {
    const circuitBreaker = this.getOrCreateCircuitBreaker(serviceKey);

    if (circuitBreaker.state === 'open') {
      if (this.shouldAttemptReset(circuitBreaker)) {
        circuitBreaker.state = 'half-open';
        circuitBreaker.successCount = 0;
      } else {
        this.metrics.circuitBreakerTrips++;
        this.emit('circuitBreakerOpen', { serviceKey, circuitBreaker });

        if (fallback) {
          return await fallback();
        }
        throw new Error(`Circuit breaker is open for service: ${serviceKey}`);
      }
    }

    try {
      const result = await this.executeWithTimeout(operation, this.config.timeout.default);
      this.onCircuitBreakerSuccess(serviceKey);
      this.metrics.successfulRequests++;
      return result;
    } catch (error) {
      this.onCircuitBreakerFailure(serviceKey);
      this.metrics.failedRequests++;

      if (fallback && circuitBreaker.state !== 'closed') {
        return await fallback();
      }
      throw error;
    }
  }

  // Enhanced Retry Logic with Exponential Backoff
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    customConfig?: Partial<ReliabilityConfig['retry']>,
  ): Promise<T> {
    const retryConfig = { ...this.config.retry, ...customConfig };
    let lastError: Error;

    for (let attempt = 0; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        this.metrics.totalRequests++;
        const result = await operation();

        if (attempt > 0) {
          this.metrics.retriedRequests++;
          this.emit('retrySucceeded', { attempt, totalAttempts: retryConfig.maxAttempts });
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        // Don't retry on the last attempt
        if (attempt === retryConfig.maxAttempts) {
          break;
        }

        // Check if error is retryable
        if (!this.isRetryableError(error as Error)) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateRetryDelay(attempt, retryConfig);

        this.emit('retryAttempt', {
          attempt: attempt + 1,
          totalAttempts: retryConfig.maxAttempts,
          delay,
          error: error as Error,
        });

        await this.sleep(delay);
      }
    }

    this.emit('retryFailed', {
      totalAttempts: retryConfig.maxAttempts,
      finalError: lastError!,
    });

    throw lastError!;
  }

  // Graceful Degradation
  async executeWithGracefulDegradation<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperations: Array<() => Promise<Partial<T>>>,
    serviceKey: string,
  ): Promise<T | Partial<T>> {
    if (!this.config.gracefulDegradation.enabled) {
      return await primaryOperation();
    }

    try {
      return await this.executeWithCircuitBreaker(serviceKey, primaryOperation);
    } catch (error) {
      this.emit('primaryOperationFailed', { serviceKey, error });

      // Try fallback operations in order
      for (let i = 0; i < fallbackOperations.length; i++) {
        try {
          const fallbackResult = await fallbackOperations[i]();
          this.emit('fallbackSucceeded', {
            serviceKey,
            fallbackIndex: i,
            partialResult: true,
          });
          return fallbackResult;
        } catch (fallbackError) {
          this.emit('fallbackFailed', {
            serviceKey,
            fallbackIndex: i,
            error: fallbackError,
          });
          continue;
        }
      }

      // All fallbacks failed
      throw error;
    }
  }

  // Health Check System
  private startHealthChecks(): void {
    const services = ['zap-client', 'ssl-analyzer', 'content-analyzer', 'database'];

    services.forEach((service) => {
      this.scheduleHealthCheck(service);
    });
  }

  private scheduleHealthCheck(service: string): void {
    const interval = setInterval(async () => {
      await this.performHealthCheck(service);
    }, this.config.healthCheck.interval);

    this.healthCheckIntervals.set(service, interval);
  }

  private async performHealthCheck(service: string): Promise<void> {
    const startTime = Date.now();
    let status: ServiceHealth['status'] = 'healthy';
    let details: ServiceHealthDetails = {};

    try {
      // Perform service-specific health check
      switch (service) {
        case 'zap-client':
          details = await this.checkZapClientHealth();
          break;
        case 'ssl-analyzer':
          details = await this.checkSSLAnalyzerHealth();
          break;
        case 'content-analyzer':
          details = await this.checkContentAnalyzerHealth();
          break;
        case 'database':
          details = await this.checkDatabaseHealth();
          break;
      }
    } catch (error) {
      status = 'unhealthy';
      details = { error: (error as Error).message };
    }

    const responseTime = Date.now() - startTime;
    const circuitBreaker = this.circuitBreakers.get(service);

    // Determine status based on circuit breaker state and response time
    if (circuitBreaker?.state === 'open') {
      status = 'unhealthy';
    } else if (responseTime > this.config.healthCheck.timeout) {
      status = 'degraded';
    }

    const health: ServiceHealth = {
      service,
      status,
      lastCheck: new Date(),
      responseTime,
      errorRate: this.calculateErrorRate(service),
      uptime: this.calculateUptime(service),
      details,
    };

    this.serviceHealth.set(service, health);
    this.emit('healthCheckCompleted', health);

    // Update overall system health
    this.updateSystemHealth();
  }

  // Service-specific health checks
  private async checkZapClientHealth(): Promise<ServiceHealthDetails> {
    // Mock health check - in real implementation, this would ping ZAP proxy
    return {
      proxyStatus: 'running',
      version: '2.11.1',
      activeScans: 0,
    };
  }

  private async checkSSLAnalyzerHealth(): Promise<ServiceHealthDetails> {
    // Mock health check - in real implementation, this would test SSL connectivity
    return {
      sslLibraryVersion: '1.1.1',
      certificateStoreAccess: true,
    };
  }

  private async checkContentAnalyzerHealth(): Promise<ServiceHealthDetails> {
    // Mock health check - in real implementation, this would test content parsing
    return {
      regexEngineStatus: 'operational',
      patternLibraryLoaded: true,
    };
  }

  private async checkDatabaseHealth(): Promise<ServiceHealthDetails> {
    // Mock health check - in real implementation, this would test database connectivity
    return {
      connectionPool: 'healthy',
      responseTime: '< 50ms',
      activeConnections: 5,
    };
  }

  // Utility methods
  private getOrCreateCircuitBreaker(serviceKey: string): CircuitBreakerState {
    if (!this.circuitBreakers.has(serviceKey)) {
      this.circuitBreakers.set(serviceKey, {
        state: 'closed',
        failures: 0,
        lastFailureTime: null,
        successCount: 0,
        nextAttemptTime: null,
      });
    }
    return this.circuitBreakers.get(serviceKey)!;
  }

  private shouldAttemptReset(circuitBreaker: CircuitBreakerState): boolean {
    return (
      circuitBreaker.lastFailureTime !== null &&
      Date.now() - circuitBreaker.lastFailureTime.getTime() >
        this.config.circuitBreaker.recoveryTimeout
    );
  }

  private onCircuitBreakerSuccess(serviceKey: string): void {
    const circuitBreaker = this.getOrCreateCircuitBreaker(serviceKey);
    circuitBreaker.failures = 0;
    circuitBreaker.lastFailureTime = null;

    if (circuitBreaker.state === 'half-open') {
      circuitBreaker.successCount++;
      if (circuitBreaker.successCount >= this.config.circuitBreaker.halfOpenMaxCalls) {
        circuitBreaker.state = 'closed';
        this.emit('circuitBreakerClosed', { serviceKey });
      }
    }
  }

  private onCircuitBreakerFailure(serviceKey: string): void {
    const circuitBreaker = this.getOrCreateCircuitBreaker(serviceKey);
    circuitBreaker.failures++;
    circuitBreaker.lastFailureTime = new Date();

    if (circuitBreaker.failures >= this.config.circuitBreaker.failureThreshold) {
      circuitBreaker.state = 'open';
      circuitBreaker.nextAttemptTime = new Date(
        Date.now() + this.config.circuitBreaker.recoveryTimeout,
      );
      this.emit('circuitBreakerOpened', { serviceKey, circuitBreaker });
    }
  }

  private calculateRetryDelay(attempt: number, config: ReliabilityConfig['retry']): number {
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
    const cappedDelay = Math.min(exponentialDelay, config.maxDelay);

    if (config.jitterEnabled) {
      // Add jitter to prevent thundering herd
      const jitter = Math.random() * cappedDelay * 0.1;
      return cappedDelay + jitter;
    }

    return cappedDelay;
  }

  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /5\d\d/, // 5xx HTTP errors
      /ECONNRESET/,
      /ENOTFOUND/,
      /ETIMEDOUT/,
    ];

    return retryablePatterns.some((pattern) => pattern.test(error.message));
  }

  private async executeWithTimeout<T>(operation: () => Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout),
      ),
    ]);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private calculateErrorRate(service: string): number {
    const circuitBreaker = this.circuitBreakers.get(service);
    if (!circuitBreaker) return 0;

    const totalRequests = circuitBreaker.failures + circuitBreaker.successCount;
    return totalRequests > 0 ? circuitBreaker.failures / totalRequests : 0;
  }

  private calculateUptime(service: string): number {
    const health = this.serviceHealth.get(service);
    if (!health) return 100;

    // Simplified uptime calculation - in real implementation, this would track historical data
    return health.status === 'healthy' ? 100 : health.status === 'degraded' ? 75 : 0;
  }

  private updateSystemHealth(): void {
    const allServices = Array.from(this.serviceHealth.values());
    const healthyServices = allServices.filter((s) => s.status === 'healthy').length;
    const totalServices = allServices.length;

    const systemHealth = totalServices > 0 ? (healthyServices / totalServices) * 100 : 100;

    this.emit('systemHealthUpdated', {
      overallHealth: systemHealth,
      services: allServices,
      timestamp: new Date(),
    });
  }

  private initializeMetrics(): ReliabilityMetrics {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      retriedRequests: 0,
      circuitBreakerTrips: 0,
      averageResponseTime: 0,
      errorRate: 0,
      uptime: 100,
      lastIncident: null,
    };
  }

  // Public API methods
  getServiceHealth(service?: string): ServiceHealth | ServiceHealth[] | null {
    if (service) {
      return this.serviceHealth.get(service) || null;
    }
    return Array.from(this.serviceHealth.values());
  }

  getCircuitBreakerState(service: string): CircuitBreakerState | null {
    return this.circuitBreakers.get(service) || null;
  }

  getReliabilityMetrics(): ReliabilityMetrics {
    return { ...this.metrics };
  }

  updateConfig(newConfig: Partial<ReliabilityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  shutdown(): void {
    // Clear all health check intervals
    this.healthCheckIntervals.forEach((interval) => clearInterval(interval));
    this.healthCheckIntervals.clear();
    this.emit('shutdown');
  }
}

interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failures: number;
  lastFailureTime: Date | null;
  successCount: number;
  nextAttemptTime: Date | null;
}
