import { NucleiClient } from './nuclei-client';

/**
 * Test script to verify Nuclei integration
 */
async function testNucleiIntegration() {
  console.log('🧪 Testing Nuclei Integration...');
  console.log('================================');

  const nucleiClient = new NucleiClient();

  try {
    // Test 1: Check if Nuclei is available
    console.log('\n📋 Test 1: Checking Nuclei availability...');
    const isAvailable = await nucleiClient.isAvailable();

    if (isAvailable) {
      console.log('✅ Nuclei is available and working');
    } else {
      console.log('❌ Nuclei is not available');
      console.log('💡 Please install Nuclei using:');
      console.log('   Windows: .\\scripts\\install-nuclei.ps1');
      console.log('   Linux/macOS: ./scripts/install-nuclei.sh');
      return;
    }

    // Test 2: Update templates
    console.log('\n📋 Test 2: Updating Nuclei templates...');
    const templatesUpdated = await nucleiClient.updateTemplates();

    if (templatesUpdated) {
      console.log('✅ Templates updated successfully');
    } else {
      console.log('⚠️ Template update failed, but continuing with existing templates');
    }

    // Test 3: Run a simple scan
    console.log('\n📋 Test 3: Running test scan on example.com...');
    const vulnerabilities = await nucleiClient.scanForHipaaVulnerabilities({
      targetUrl: 'https://example.com',
      timeout: 30000,
      tags: ['ssl', 'headers'],
      severity: ['critical', 'high', 'medium', 'low'],
    });

    console.log(`✅ Scan completed: ${vulnerabilities.length} findings`);

    if (vulnerabilities.length > 0) {
      console.log('\n📊 Sample findings:');
      vulnerabilities.slice(0, 3).forEach((vuln, index) => {
        console.log(`   ${index + 1}. ${vuln.type} (${vuln.severity})`);
        console.log(`      Location: ${vuln.location}`);
        console.log(`      Description: ${vuln.description.substring(0, 100)}...`);
      });

      if (vulnerabilities.length > 3) {
        console.log(`   ... and ${vulnerabilities.length - 3} more findings`);
      }
    } else {
      console.log('   No vulnerabilities found (this is good!)');
    }

    console.log('\n🎉 Nuclei integration test completed successfully!');
    console.log('✅ Ready for HIPAA security scanning');
  } catch (error) {
    console.error('\n❌ Nuclei integration test failed:', error);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Ensure Nuclei is installed and in PATH');
    console.log('2. Check NUCLEI_PATH environment variable');
    console.log('3. Verify internet connectivity for template updates');
    console.log('4. Check file permissions (Linux/macOS)');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testNucleiIntegration().catch(console.error);
}

export { testNucleiIntegration };
