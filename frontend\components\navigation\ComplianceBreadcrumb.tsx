import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home, Shield, FileText, Lock } from 'lucide-react';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  current?: boolean;
}

export interface ComplianceBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

/**
 * Compliance Breadcrumb Navigation Component
 * Provides hierarchical navigation for compliance pages
 */
export const ComplianceBreadcrumb: React.FC<ComplianceBreadcrumbProps> = ({
  items,
  className = '',
}) => {
  return (
    <nav className={`flex text-sm text-gray-600 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />}

            {item.current ? (
              <span className="flex items-center gap-1 text-gray-900 font-medium">
                {item.icon}
                {item.label}
              </span>
            ) : item.href ? (
              <Link
                href={item.href}
                className="flex items-center gap-1 hover:text-blue-600 transition-colors"
              >
                {item.icon}
                {item.label}
              </Link>
            ) : (
              <span className="flex items-center gap-1">
                {item.icon}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Preset breadcrumb configurations for common compliance pages
export const createComplianceBreadcrumbs = {
  hipaaDashboard: (): BreadcrumbItem[] => [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: 'HIPAA Compliance',
      current: true,
      icon: <Shield className="h-4 w-4" />,
    },
  ],

  hipaaPrivacy: (): BreadcrumbItem[] => [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: 'HIPAA Compliance',
      href: '/dashboard/hipaa',
      icon: <Shield className="h-4 w-4" />,
    },
    {
      label: 'Privacy Scans',
      current: true,
      icon: <FileText className="h-4 w-4" />,
    },
  ],

  hipaaPrivacyResult: (scanId?: string): BreadcrumbItem[] => [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: 'HIPAA Compliance',
      href: '/dashboard/hipaa',
      icon: <Shield className="h-4 w-4" />,
    },
    {
      label: 'Privacy Scans',
      href: '/dashboard/hipaa/privacy',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      label: scanId ? `Scan ${scanId.slice(0, 8)}...` : 'Scan Results',
      current: true,
    },
  ],

  hipaaSecurity: (): BreadcrumbItem[] => [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: 'HIPAA Compliance',
      href: '/dashboard/hipaa',
      icon: <Shield className="h-4 w-4" />,
    },
    {
      label: 'Security Scans',
      current: true,
      icon: <Lock className="h-4 w-4" />,
    },
  ],

  hipaaSecurityResult: (scanId?: string): BreadcrumbItem[] => [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
    },
    {
      label: 'HIPAA Compliance',
      href: '/dashboard/hipaa',
      icon: <Shield className="h-4 w-4" />,
    },
    {
      label: 'Security Scans',
      href: '/dashboard/hipaa/security',
      icon: <Lock className="h-4 w-4" />,
    },
    {
      label: scanId ? `Scan ${scanId.slice(0, 8)}...` : 'Scan Results',
      current: true,
    },
  ],

  // Generic compliance breadcrumb builder
  custom: (path: string[]): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      {
        label: 'Dashboard',
        href: '/dashboard',
        icon: <Home className="h-4 w-4" />,
      },
    ];

    let currentPath = '/dashboard';

    path.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === path.length - 1;

      // Get appropriate icon based on segment
      let icon: React.ReactNode = undefined;
      if (segment === 'compliance') icon = <Shield className="h-4 w-4" />;
      else if (segment === 'hipaa') icon = <Shield className="h-4 w-4" />;
      else if (segment === 'privacy') icon = <FileText className="h-4 w-4" />;
      else if (segment === 'security') icon = <Lock className="h-4 w-4" />;

      breadcrumbs.push({
        label: segment.charAt(0).toUpperCase() + segment.slice(1),
        href: isLast ? undefined : currentPath,
        current: isLast,
        icon,
      });
    });

    return breadcrumbs;
  },
};

// Hook for automatic breadcrumb generation based on current path
export const useBreadcrumbFromPath = (pathname: string): BreadcrumbItem[] => {
  const segments = pathname.split('/').filter(Boolean);

  // Handle specific known routes
  if (pathname === '/dashboard/hipaa') {
    return createComplianceBreadcrumbs.hipaaDashboard();
  }

  if (pathname === '/dashboard/hipaa/privacy') {
    return createComplianceBreadcrumbs.hipaaPrivacy();
  }

  if (pathname === '/dashboard/hipaa/security') {
    return createComplianceBreadcrumbs.hipaaSecurity();
  }

  if (pathname.startsWith('/dashboard/hipaa/privacy/')) {
    const scanId = segments[segments.length - 1];
    return createComplianceBreadcrumbs.hipaaPrivacyResult(scanId);
  }

  if (pathname.startsWith('/dashboard/hipaa/security/')) {
    const scanId = segments[segments.length - 1];
    return createComplianceBreadcrumbs.hipaaSecurityResult(scanId);
  }

  // Fallback to generic breadcrumb generation
  if (segments.length > 1) {
    return createComplianceBreadcrumbs.custom(segments.slice(1));
  }

  return [
    {
      label: 'Dashboard',
      current: true,
      icon: <Home className="h-4 w-4" />,
    },
  ];
};
