'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';
import {
  BookOpen,
  Shield,
  FileText,
  Users,
  Lock,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Download,
  Copy,
  Eye,
  ChevronUp,
  Globe,
  Database,
  UserCheck,
} from 'lucide-react';

const GuidancePage = () => {
  const [expandedTemplate, setExpandedTemplate] = useState<string | null>(null);
  const [copiedTemplate, setCopiedTemplate] = useState<string | null>(null);

  const copyToClipboard = async (text: string, templateId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedTemplate(templateId);
      setTimeout(() => setCopiedTemplate(null), 2000);
    } catch (err) {
      // Silently handle clipboard errors
      setCopiedTemplate(null);
    }
  };

  const guidanceCategories = [
    {
      id: 'privacy-policy',
      title: 'Privacy Policy Compliance',
      description: 'Essential elements for HIPAA-compliant privacy policies',
      icon: <FileText className="h-6 w-6" />,
      color: 'bg-blue-500',
      items: [
        'Required HIPAA privacy notice elements',
        'Patient rights disclosure',
        'Uses and disclosures of PHI',
        'Contact information for privacy officer',
        'Complaint procedures',
        'Business associate agreements',
        'Minimum necessary standards',
        'Amendment and access procedures',
      ],
    },
    {
      id: 'technical-safeguards',
      title: 'Technical Safeguards',
      description: 'Technical measures to protect ePHI',
      icon: <Shield className="h-6 w-6" />,
      color: 'bg-green-500',
      items: [
        'Access control systems and unique user identification',
        'Audit controls and comprehensive logging',
        'Integrity controls for ePHI alteration/destruction',
        'Person or entity authentication mechanisms',
        'Transmission security and encryption',
        'Automatic logoff procedures',
        'Encryption and decryption standards',
      ],
    },
    {
      id: 'gdpr-compliance',
      title: 'GDPR & Data Protection',
      description: 'European data protection and privacy requirements',
      icon: <Globe className="h-6 w-6" />,
      color: 'bg-indigo-500',
      items: [
        'Cookie consent mechanisms',
        'Data subject rights (access, portability, erasure)',
        'Privacy by design principles',
        'Data processing lawful basis',
        'Data protection impact assessments',
        'Cross-border data transfer safeguards',
        'Breach notification procedures',
      ],
    },
    {
      id: 'administrative-safeguards',
      title: 'Administrative Safeguards',
      description: 'Policies and procedures for HIPAA compliance',
      icon: <Users className="h-6 w-6" />,
      color: 'bg-purple-500',
      items: [
        'Security officer designation and responsibilities',
        'Workforce training and access management',
        'Information access management procedures',
        'Security awareness and training programs',
        'Contingency plan and data backup procedures',
        'Incident response and reporting procedures',
        'Business associate agreement management',
      ],
    },
    {
      id: 'physical-safeguards',
      title: 'Physical Safeguards',
      description: 'Physical protection of ePHI and systems',
      icon: <Lock className="h-6 w-6" />,
      color: 'bg-orange-500',
      items: [
        'Facility access controls and visitor management',
        'Workstation use restrictions and positioning',
        'Device and media controls for ePHI storage',
        'Physical access logging and monitoring',
        'Secure disposal procedures for ePHI',
        'Workstation security and screen locks',
        'Environmental controls and monitoring',
      ],
    },
    {
      id: 'ada-accessibility',
      title: 'ADA Accessibility Compliance',
      description: 'Web accessibility standards and requirements',
      icon: <UserCheck className="h-6 w-6" />,
      color: 'bg-teal-500',
      items: [
        'WCAG 2.1 AA compliance standards',
        'Keyboard navigation accessibility',
        'Screen reader compatibility',
        'Color contrast requirements',
        'Alternative text for images',
        'Form accessibility and labeling',
        'Video captions and transcripts',
      ],
    },
  ];

  const privacyPolicyTemplates = [
    {
      id: 'hipaa-privacy-notice',
      title: 'HIPAA Privacy Notice Template',
      description: 'Complete HIPAA-compliant privacy notice template',
      category: 'HIPAA',
      template: `NOTICE OF PRIVACY PRACTICES

This notice describes how medical information about you may be used and disclosed and how you can get access to this information. Please review it carefully.

USES AND DISCLOSURES OF PROTECTED HEALTH INFORMATION

We may use and disclose your protected health information (PHI) for the following purposes:

Treatment: We may use your PHI to provide, coordinate, or manage your health care and related services.

Payment: We may use and disclose your PHI to obtain payment for services we provide to you.

Health Care Operations: We may use and disclose your PHI for health care operations purposes.

YOUR RIGHTS REGARDING YOUR PHI

You have the following rights regarding your PHI:
• Right to request restrictions on uses and disclosures
• Right to receive confidential communications
• Right to inspect and copy your PHI
• Right to amend your PHI
• Right to receive an accounting of disclosures
• Right to a paper copy of this notice

CONTACT INFORMATION

Privacy Officer: [Name]
Address: [Address]
Phone: [Phone Number]
Email: [Email Address]

COMPLAINTS

You may file a complaint with us or with the Secretary of Health and Human Services if you believe your privacy rights have been violated.

Effective Date: [Date]`,
    },
    {
      id: 'gdpr-privacy-policy',
      title: 'GDPR Privacy Policy Template',
      description: 'GDPR-compliant privacy policy template',
      category: 'GDPR',
      template: `PRIVACY POLICY

1. DATA CONTROLLER
[Company Name]
[Address]
[Contact Information]

2. TYPES OF DATA WE COLLECT
• Personal identification information
• Contact information
• Usage data and cookies
• Special categories of data (if applicable)

3. LAWFUL BASIS FOR PROCESSING
We process your personal data based on:
• Consent
• Contract performance
• Legal obligation
• Legitimate interests

4. YOUR RIGHTS UNDER GDPR
• Right of access
• Right to rectification
• Right to erasure
• Right to restrict processing
• Right to data portability
• Right to object
• Rights related to automated decision making

5. DATA RETENTION
We retain your personal data only for as long as necessary for the purposes outlined in this policy.

6. INTERNATIONAL TRANSFERS
[Details about data transfers outside the EU]

7. CONTACT INFORMATION
Data Protection Officer: [Name]
Email: [Email]
Phone: [Phone]

Last updated: [Date]`,
    },
  ];

  const commonIssues = [
    {
      issue: 'Missing Privacy Policy',
      severity: 'critical',
      description: 'No privacy policy found on the website',
      solution: 'Create and publish a comprehensive HIPAA privacy notice using our template',
    },
    {
      issue: 'Incomplete Contact Information',
      severity: 'high',
      description: 'Privacy officer contact details not provided',
      solution: 'Add privacy officer name, title, and contact information',
    },
    {
      issue: 'Missing Cookie Consent',
      severity: 'high',
      description: 'No GDPR-compliant cookie consent mechanism found',
      solution: 'Implement cookie consent banner with granular controls',
    },
    {
      issue: 'Weak SSL/TLS Configuration',
      severity: 'high',
      description: 'Outdated encryption protocols detected',
      solution: 'Update to TLS 1.2 or higher with strong cipher suites',
    },
    {
      issue: 'Missing Security Headers',
      severity: 'medium',
      description: 'Important security headers not implemented',
      solution: 'Implement HSTS, CSP, and other security headers',
    },
    {
      issue: 'Accessibility Violations',
      severity: 'medium',
      description: 'WCAG 2.1 AA compliance issues detected',
      solution: 'Fix color contrast, keyboard navigation, and screen reader issues',
    },
    {
      issue: 'Missing Business Associate Agreements',
      severity: 'high',
      description: 'Third-party integrations without proper BAAs',
      solution: 'Execute business associate agreements with all vendors handling PHI',
    },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">HIPAA Compliance Guidance</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive guidance to help you achieve and maintain HIPAA compliance for your
          healthcare organization.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link href="/dashboard/scan/new">
          <Button className="w-full h-16 text-lg">
            <CheckCircle className="mr-2 h-5 w-5" />
            Start New Scan
          </Button>
        </Link>
        <Link href="/dashboard/scans">
          <Button variant="outline" className="w-full h-16 text-lg">
            <FileText className="mr-2 h-5 w-5" />
            View Past Scans
          </Button>
        </Link>
        <Link href="/hipaa-security-live">
          <Button variant="outline" className="w-full h-16 text-lg">
            <Shield className="mr-2 h-5 w-5" />
            Live HIPAA Security Scanner
          </Button>
        </Link>
      </div>

      {/* Guidance Categories */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">Compliance Areas</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {guidanceCategories.map((category) => (
            <Card key={category.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${category.color} text-white`}>
                    {category.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {category.items.map((item, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Privacy Policy Templates */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">Privacy Policy Templates</h2>
        <p className="text-gray-600">
          Ready-to-use templates for creating compliant privacy policies and notices.
        </p>
        <div className="space-y-4">
          {privacyPolicyTemplates.map((template) => (
            <Card key={template.id} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{template.title}</CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                    <Badge className="mt-2 bg-blue-100 text-blue-800">{template.category}</Badge>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setExpandedTemplate(expandedTemplate === template.id ? null : template.id)
                      }
                    >
                      {expandedTemplate === template.id ? (
                        <>
                          <ChevronUp className="h-4 w-4 mr-1" />
                          Hide
                        </>
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(template.template, template.id)}
                    >
                      {copiedTemplate === template.id ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              {expandedTemplate === template.id && (
                <CardContent>
                  <div className="bg-gray-50 border rounded-lg p-4">
                    <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                      {template.template}
                    </pre>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      </div>

      {/* Common Issues */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">Common Compliance Issues</h2>
        <div className="space-y-4">
          {commonIssues.map((issue, index) => (
            <Card key={index} className="border-l-4 border-l-orange-500">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      <h3 className="font-semibold text-lg">{issue.issue}</h3>
                      <Badge className={`${getSeverityColor(issue.severity)} text-white`}>
                        {issue.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-gray-600 mb-3">{issue.description}</p>
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <p className="text-sm text-green-800">
                        <strong>Solution:</strong> {issue.solution}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Resources */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">Additional Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <h3 className="font-semibold">HHS HIPAA Guidelines</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Official HIPAA compliance guidelines from the Department of Health and Human
                Services.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit HHS.gov
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <Shield className="h-5 w-5 text-green-500" />
                <h3 className="font-semibold">Security Risk Assessment</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Tools and templates for conducting HIPAA security risk assessments.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Download Templates
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <Users className="h-5 w-5 text-purple-500" />
                <h3 className="font-semibold">Training Materials</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                HIPAA training resources for your workforce and business associates.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Access Training
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <Globe className="h-5 w-5 text-indigo-500" />
                <h3 className="font-semibold">GDPR Resources</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                European data protection guidelines and implementation resources.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit GDPR.eu
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <UserCheck className="h-5 w-5 text-teal-500" />
                <h3 className="font-semibold">ADA Compliance</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Web accessibility guidelines and WCAG 2.1 implementation resources.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                WCAG Guidelines
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-3">
                <Database className="h-5 w-5 text-orange-500" />
                <h3 className="font-semibold">Business Associate Agreements</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Sample BAA templates and vendor management resources.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Download BAA Template
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default GuidancePage;
