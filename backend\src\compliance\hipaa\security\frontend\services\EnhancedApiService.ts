/**
 * Enhanced API Service with Circuit Breaker and Retry Logic
 * Implements reliability patterns based on validation findings
 */

import {
  EnhancedScanConfig,
  EnhancedScanResult,
  ScanProgress,
  ScanError,
  APIResponse,
  CircuitBreakerState,
} from '../types';

class CircuitBreaker {
  private state: CircuitBreakerState = {
    state: 'closed',
    failures: 0,
    lastFailureTime: null,
    successCount: 0,
    nextAttemptTime: null,
  };

  private readonly failureThreshold = 5;
  private readonly recoveryTimeout = 30000; // 30 seconds
  private readonly halfOpenMaxCalls = 3;

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state.state === 'open') {
      if (this.shouldAttemptReset()) {
        this.state.state = 'half-open';
        this.state.successCount = 0;
      } else {
        throw new Error('Circuit breaker is open - service temporarily unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private shouldAttemptReset(): boolean {
    return (
      this.state.lastFailureTime !== null &&
      Date.now() - this.state.lastFailureTime.getTime() > this.recoveryTimeout
    );
  }

  private onSuccess(): void {
    this.state.failures = 0;
    this.state.lastFailureTime = null;

    if (this.state.state === 'half-open') {
      this.state.successCount++;
      if (this.state.successCount >= this.halfOpenMaxCalls) {
        this.state.state = 'closed';
      }
    }
  }

  private onFailure(): void {
    this.state.failures++;
    this.state.lastFailureTime = new Date();

    if (this.state.failures >= this.failureThreshold) {
      this.state.state = 'open';
      this.state.nextAttemptTime = new Date(Date.now() + this.recoveryTimeout);
    }
  }

  getState(): CircuitBreakerState {
    return { ...this.state };
  }
}

export class EnhancedApiService {
  private baseUrl: string;
  private circuitBreaker: CircuitBreaker;
  private retryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
  };

  constructor(baseUrl: string = '/api/hipaa-security') {
    this.baseUrl = baseUrl;
    this.circuitBreaker = new CircuitBreaker();
  }

  // Enhanced scan initiation with retry logic
  async startScan(config: EnhancedScanConfig): Promise<APIResponse<{ scanId: string }>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // Enhanced progress monitoring with WebSocket fallback
  async getProgress(scanId: string): Promise<APIResponse<ScanProgress>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/scan/${scanId}/progress`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // Enhanced results retrieval with partial results support
  async getResults(
    scanId: string,
    includePartial: boolean = true,
  ): Promise<APIResponse<EnhancedScanResult>> {
    return this.executeWithRetry(async () => {
      const url = `${this.baseUrl}/scan/${scanId}/results?includePartial=${includePartial}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // Cancel scan with cleanup
  async cancelScan(scanId: string): Promise<APIResponse<{ cancelled: boolean }>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/scan/${scanId}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // Retry failed tests
  async retryFailedTests(
    scanId: string,
    testIds: string[],
  ): Promise<APIResponse<{ scanId: string }>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/scan/${scanId}/retry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testIds }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // Health check endpoint
  async healthCheck(): Promise<APIResponse<{ status: string; timestamp: Date }>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(`${this.baseUrl}/health`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.createSuccessResponse(data);
    });
  }

  // WebSocket connection for real-time updates
  connectWebSocket(
    scanId: string,
    onMessage: (data: ScanProgress | EnhancedScanResult | ScanError) => void,
    onError: (error: Error) => void,
  ): WebSocket {
    const wsUrl = `${this.baseUrl.replace('http', 'ws')}/scan/${scanId}/ws`;
    const ws = new WebSocket(wsUrl);

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        onError(new Error('Failed to parse WebSocket message'));
      }
    };

    ws.onerror = () => {
      onError(new Error('WebSocket connection error'));
    };

    ws.onclose = () => {
      // Attempt to reconnect after a delay
      setTimeout(() => {
        if (ws.readyState === WebSocket.CLOSED) {
          this.connectWebSocket(scanId, onMessage, onError);
        }
      }, 5000);
    };

    return ws;
  }

  // Execute operation with circuit breaker and retry logic
  private async executeWithRetry<T>(
    operation: () => Promise<APIResponse<T>>,
  ): Promise<APIResponse<T>> {
    const startTime = Date.now();
    let lastError: Error | null = null;
    let retryCount = 0;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        const result = await this.circuitBreaker.execute(operation);

        // Add metadata to successful response
        result.metadata = {
          ...result.metadata,
          duration: Date.now() - startTime,
          retryCount,
        };

        return result;
      } catch (error) {
        lastError = error as Error;
        retryCount = attempt;

        // Don't retry on the last attempt
        if (attempt === this.retryConfig.maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt),
          this.retryConfig.maxDelay,
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + Math.random() * 1000;

        await this.sleep(jitteredDelay);
      }
    }

    // Create error response
    return this.createErrorResponse(lastError!, Date.now() - startTime, retryCount);
  }

  private createSuccessResponse<T>(data: T): APIResponse<T> {
    return {
      success: true,
      data,
      metadata: {
        requestId: this.generateRequestId(),
        timestamp: new Date(),
        duration: 0, // Will be set by executeWithRetry
        retryCount: 0, // Will be set by executeWithRetry
      },
    };
  }

  private createErrorResponse<T = never>(
    error: Error,
    duration: number,
    retryCount: number,
  ): APIResponse<T> {
    const scanError: ScanError = {
      type: this.categorizeError(error),
      message: error.message,
      details: error.stack,
      recoverable: this.isRecoverableError(error),
      suggestedAction: this.getSuggestedAction(error),
      timestamp: new Date(),
      context: {
        phase: 'api-request',
      },
    };

    return {
      success: false,
      error: scanError,
      metadata: {
        requestId: this.generateRequestId(),
        timestamp: new Date(),
        duration,
        retryCount,
      },
    };
  }

  private categorizeError(error: Error): ScanError['type'] {
    const message = error.message.toLowerCase();

    if (message.includes('timeout') || message.includes('timed out')) {
      return 'timeout';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('400') || message.includes('validation')) {
      return 'validation';
    }
    if (message.includes('500') || message.includes('server')) {
      return 'server';
    }

    return 'unknown';
  }

  private isRecoverableError(error: Error): boolean {
    const message = error.message.toLowerCase();

    // Non-recoverable errors
    if (message.includes('400') || message.includes('401') || message.includes('403')) {
      return false;
    }

    // Recoverable errors
    return (
      message.includes('timeout') ||
      message.includes('network') ||
      message.includes('500') ||
      message.includes('502') ||
      message.includes('503')
    );
  }

  private getSuggestedAction(error: Error): string {
    const type = this.categorizeError(error);

    switch (type) {
      case 'timeout':
        return 'The operation timed out. Try again or check if the target website is responding slowly.';
      case 'network':
        return 'Network connectivity issue detected. Check your internet connection and try again.';
      case 'validation':
        return 'Invalid request data. Please verify your configuration and try again.';
      case 'server':
        return 'Server error occurred. Please try again in a few moments.';
      default:
        return 'An unexpected error occurred. Please try again or contact support if the issue persists.';
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Get circuit breaker status for monitoring
  getCircuitBreakerStatus(): CircuitBreakerState {
    return this.circuitBreaker.getState();
  }
}
