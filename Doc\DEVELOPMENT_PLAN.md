# Comply Checker - Development Plan

## 1. Introduction

This document outlines the development plan for the "Comply Checker" web application. Comply Checker is a SaaS MVP designed to help Small to Medium-sized Businesses (SMBs) scan their websites for compliance with various regulations, initially focusing on HIPAA, GDPR, ADA, and WCAG. This plan adheres to the guidelines specified in the `.cursorrules` (which I understand is the content of `.windsurfrules` as per project memory) and incorporates insights from the provided compliance documentation.

**Project Name:** Comply Checker
**Domain:** ComplyChecker.com
**Description:** SaaS MVP for GDPR/CCPA, HIPAA, ADA, WCAG compliance scanning, targeting SMBs.

## 2. Project Goals and Scope

### 2.1. Goals

- Develop a functional MVP capable of scanning websites against selected compliance standards (HIPAA, GDPR, ADA, WCAG).
- Provide users with actionable reports highlighting compliance issues and suggestions for remediation.
- Ensure a user-friendly interface for non-technical users.
- Achieve fast page load times (< 2s) and scan times (< 5s for typical sites).
- Implement secure APIs and authentication.
- Prioritize free/open-source tools as per constraints.
- Lay a foundation for future expansion to include CCPA and other compliance standards.

### 2.2. Scope

**In Scope:**

- User registration and authentication (leveraging Keycloak).
- Dashboard for managing scans and viewing reports.
- Ability to initiate scans by providing a website URL.
- Automated checks for:

  - HIPAA (based on provided documentation and general knowledge)
  - GDPR (based on provided documentation and general knowledge)
  - ADA (based on provided documentation and general knowledge)
  - WCAG (based on provided documentation and general knowledge)

- Generation of compliance reports.
- Frontend built with Next.js, React, TypeScript, TailwindCSS, Shadcn-UI.
- Backend built with Node.js, Express, TypeScript.
- PostgreSQL database.
- Basic integrations with Listmonk (for potential notifications) and n8n (for potential workflow automation, if applicable later).

**Out of Scope (for MVP):**

- Automated remediation of compliance issues.
- Advanced AI-driven analysis beyond rule-based checks.
- Comprehensive legal advice (application will provide technical checks).
- Mobile application (web-first).
- Automated test suites (manual testing for MVP, as per user request, though Jest/Playwright are in rules for future).
- CCPA specific module (can be added post-MVP).

## 3. Technology Stack

As per `.cursorrules`:

- **Frontend:** TypeScript, Next.js (v14.x), React (v18.x), TailwindCSS
- **UI Components:** Shadcn-UI
- **Backend:** TypeScript, Node.js (v20.x), Express (v4.x)
- **Database:** PostgreSQL (v16.x) with `node-postgres` (pg) client and `pgcrypto` for encryption.
- **Authentication:** Keycloak
- **Email/Newsletter (Optional for MVP core scan):** Listmonk
- **Workflow Automation (Optional for MVP core scan):** n8n
- **Development Environment:** Local Windows, WSL2, Docker Desktop
- **Deployment:** Contabo VPS, Docker

## 4. Project Structure

Based on `.cursorrules`:

```
comply-checker/
├── frontend/
│   ├── app/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── (auth)/              # Keycloak integration, sign-in, sign-up callbacks
│   │   │   └── [..nextauth]/page.tsx # Or specific Keycloak pages
│   │   ├── dashboard/
│   │   │   ├── page.tsx
│   │   │   └── scans/
│   │   │       ├── [scanId]/page.tsx # View scan report
│   │   │       └── new/page.tsx      # New scan page
│   │   ├── compliance/             # Informational pages about compliance types
│   │   │   ├── hipaa/
│   │   │   │   └── info-page.tsx
│   │   │   ├── gdpr/
│   │   │   │   └── info-page.tsx
│   │   │   ├── ada/
│   │   │   │   └── info-page.tsx
│   │   │   └── wcag/
│   │   │       └── info-page.tsx
│   │   └── (legal)/
│   │       ├── privacy-policy/page.tsx
│   │       └── terms-of-service/page.tsx
│   ├── components/
│   │   ├── ui/                   # Shadcn-UI components + custom reusable UI
│   │   ├── layout/
│   │   │   ├── Navbar.tsx
│   │   │   └── Footer.tsx
│   │   ├── compliance/
│   │   │   ├── ScanForm.tsx
│   │   │   ├── ReportDisplay.tsx
│   │   │   ├── hipaa/            # HIPAA specific UI components for reports
│   │   │   ├── gdpr/             # GDPR specific UI components for reports
│   │   │   ├── ada/              # ADA specific UI components for reports
│   │   │   └── wcag/             # WCAG specific UI components for reports
│   │   └── auth/
│   │       └── UserProfileButton.tsx
│   ├── lib/
│   │   ├── api.ts                # Frontend API client for backend
│   │   └── utils.ts              # Frontend utilities
│   ├── styles/
│   │   └── globals.css
│   ├── public/
│   ├── next.config.mjs           # Using .mjs for Next.js 14+
│   └── tsconfig.json
├── backend/
│   ├── src/
│   │   ├── routes/
│   │   │   ├── index.ts          # Main router
│   │   │   ├── health.ts
│   │   │   ├── auth.ts           # Auth related routes
│   │   │   └── compliance/
│   │   │       ├── index.ts      # Compliance router
│   │   │       └── scan.ts       # Route to initiate scans and get results
│   │   ├── compliance/
│   │   │   ├── common/
│   │   │   │   ├── AbstractScanner.ts
│   │   │   │   ├── types.ts
│   │   │   │   └── web-scraper.ts
│   │   │   ├── hipaa/
│   │   │   │   ├── hipaa-scanner.ts
│   │   │   │   ├── access-control.ts
│   │   │   │   ├── audit-controls.ts
│   │   │   │   ├── types.ts
│   │   │   │   └── index.ts
│   │   │   ├── gdpr/
│   │   │   │   ├── gdpr-scanner.ts
│   │   │   │   ├── data-discovery.ts
│   │   │   │   ├── consent-management.ts
│   │   │   │   ├── types.ts
│   │   │   │   └── index.ts
│   │   │   ├── ada/
│   │   │   │   ├── ada-scanner.ts
│   │   │   │   ├── image-alt-text.ts
│   │   │   │   ├── keyboard-navigation.ts
│   │   │   │   ├── types.ts
│   │   │   │   └── index.ts
│   │   │   └── wcag/
│   │   │       ├── wcag-scanner.ts
│   │   │       ├── contrast-ratio.ts
│   │   │       ├── aria-attributes.ts
│   │   │       ├── types.ts
│   │   │       └── index.ts
│   │   ├── services/
│   │   │   ├── keycloak-service.ts
│   │   │   ├── db-service.ts       # Database interaction (Knex)
│   │   ├── lib/
│   │   │   └── env.ts              # Zod environment variable validation
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   └── error-handler.ts
│   │   ├── types/
│   │   │   └── index.ts            # Shared backend types
│   │   ├── config/
│   │   │   ├── index.ts
│   │   │   └── database.ts         # Knex configuration
│   │   ├── index.ts                # Main backend entry point
│   │   └── app.ts                  # Express app configuration
│   ├── Dockerfile
│   └── tsconfig.json
├── migrations/                     # Knex migrations (YYYYMMDDHHMMSS_description.sql)
├── docker/                         # Docker configurations
│   ├── docker-compose.yml
│   ├── docker-compose.staging.yml
├── .env.example
├── .env.example.staging
├── package.json                    # Root package.json (consider workspaces)
├── tsconfig.base.json              # Base tsconfig for shared settings
├── .gitignore
├── .prettierrc.json
├── .eslintrc.json
├── .lintstagedrc
├── BUGS.md
└── DEVELOPMENT_PLAN.md             # This file
```

## 5. Development Phases and Sequence

**Phase 0: Project Setup & Core Infrastructure (1-2 Weeks)**

- **Tasks:** Monorepo setup, Next.js/Express app init, Docker (PostgreSQL, Keycloak, MailHog), Linting/Formatting, CI (build, lint).
- **Deliverables:** Runnable empty apps, Docker setup, Dev tools configured.

**Phase 1: Authentication & User Management (1 Week)**

- **Tasks:** Keycloak integration (frontend/backend), User registration/login, Secure API routes, DB schema (users, scans), Knex setup & migration.
- **Deliverables:** Functional auth, Secure APIs.

**Phase 2: Core Scanning Engine & Dashboard (2 Weeks)**

- **Tasks:** Dashboard layout, New Scan UI, Backend API for scan initiation, Basic web scraper (`web-scraper.ts` with Cheerio/JSDOM), `AbstractScanner.ts`, Store scan requests in DB, Display scan history.
- **Deliverables:** URL submission for scanning, Backend fetches content, Dashboard shows scan history.

**Phase 3: HIPAA Compliance Module (2-3 Weeks)**

- **Tasks:** Analyze HIPAA docs for automatable checks (privacy policy link, HTTPS, etc.), Implement `hipaa-scanner.ts`, Develop check functions (e.g., `access-control.ts`), Define `types.ts`, Integrate scanner, UI for HIPAA results.
- **Deliverables:** HIPAA scanning, Report section. **Manual Testing.**
- **Status (Short-Term Goal):** Initial module (Privacy Policy check) implemented and integrated into a stable frontend/backend system. Further enhancements and additional checks planned for long-term development.

**Phase 4: GDPR Compliance Module (2-3 Weeks)**

- **Tasks:** Analyze GDPR docs (cookie consent, privacy policy link, etc.), Implement `gdpr-scanner.ts`, Develop check functions, Define `types.ts`, Integrate scanner, UI for GDPR results.
- **Deliverables:** GDPR scanning, Report section. **Manual Testing.**
- **Status (Short-Term Goal):** Initial module (Cookie Consent check) implemented and integrated into a stable frontend/backend system. Further enhancements and additional checks planned for long-term development.

**Phase 5: ADA Compliance Module (2-3 Weeks)**

- **Tasks:** Analyze ADA docs (alt text, `lang` attr, form labels, ARIA hints - consider `axe-core`), Implement `ada-scanner.ts`, Develop check functions, Define `types.ts`, Integrate scanner, UI for ADA results.
- **Deliverables:** ADA scanning, Report section. **Manual Testing.**
- **Status (Short-Term Goal):** Initial module (Image Alt Text check) implemented and integrated into a stable frontend/backend system. Further enhancements and additional checks planned for long-term development.

**Phase 6: WCAG Compliance Module (2-3 Weeks)**

- **Tasks:** Analyze WCAG docs (A, AA levels - color contrast, text resize, keyboard nav, ARIA - consider `axe-core`), Implement `wcag-scanner.ts`, Develop check functions, Define `types.ts`, Integrate scanner, UI for WCAG results.
- **Deliverables:** WCAG scanning, Report section. **Manual Testing.**
- **Status (Short-Term Goal):** Initial module (Page Title Presence check) implemented and integrated into a stable frontend/backend system. Further enhancements and additional checks planned for long-term development.

**Phase 7: Reporting, UI Polish & Finalization (1-2 Weeks)**

- **Tasks:** Consolidate reports, Improve UI/UX, Add informational pages (Privacy, ToS), Thorough manual testing, Bug fixing (`BUGS.md`), Prepare `.env.example` files.
- **Deliverables:** Polished MVP, Comprehensive reporting, Setup docs.

**Phase 8: Deployment Setup (1 Week)**

- **Tasks:** Prepare `docker-compose.staging.yml` & production `docker-compose.yml`, Configure Contabo VPS, Setup Docker, Deploy containers, Configure DNS, SSL (Let's Encrypt), Test on staging/prod.
- **Deliverables:** Deployed app on Contabo VPS.

## 6. Frontend Details

- **Pages:** `/`, `/auth/sign-in`, `/dashboard`, `/dashboard/scans/new`, `/dashboard/scans/[scanId]`, `/legal/privacy-policy`, `/legal/terms-of-service`.
- **Key Components:** `ui/` (Shadcn), `layout/Navbar.tsx`, `compliance/ScanForm.tsx`, `compliance/ReportDisplay.tsx`.
- **State Management:** React Context API (start simple).
- **API Client:** `lib/api.ts` (typed `fetch` or `axios`).

## 7. Backend Details

- **API Routes (`/api/v1/...`):** `POST /compliance/scan`, `GET /compliance/scans`, `GET /compliance/scans/{scanId}`, `GET /health`.
- **Compliance Modules (`backend/src/compliance/`):** Each standard with `[standard]-scanner.ts`, check functions, `types.ts`.
  - **Scanning Process:** API -> Scraper -> Scanner(s) -> Aggregate Results -> Store in DB.
- **Database Interaction:** `db-service.ts` (Knex.js).
- **Security:** Zod validation, `helmet`, `express-rate-limit`, `pgcrypto`.

## 8. Database Schema (Conceptual)

- **Users:** `id (PK)`, `keycloak_id`, `email`, `created_at`, `updated_at`.
- **Scans:** `id (PK)`, `user_id (FK)`, `url`, `status`, `standards_scanned (JSONB)`, `summary_report (JSONB)`, `created_at`, `completed_at`.
- **ComplianceFindings:** `id (PK)`, `scan_id (FK)`, `standard`, `check_id`, `description`, `passed (BOOL)`, `severity`, `details (JSONB)`, `remediation_suggestion`.

## 9. Key Challenges and Mitigations

- **Accuracy of Automated Checks:** State limitations, focus on defined checks.
- **Dynamic Websites:** Start simple (Cheerio/JSDOM), consider Puppeteer/Playwright if essential (adds complexity).
- **Rate Limiting by Target Sites:** Respectful scanning, clear User-Agent.
- **Complexity of Rules:** Iterate, use provided MDs.
- **Performance:** Optimize, consider background jobs post-MVP if needed.

## 10. Non-Functional Requirements (NFRs)

- **Performance:** Page load < 2s, Scan time < 5s.
- **Security:** OWASP Top 10, Secure auth, Encryption, Input validation.
- **Usability:** Intuitive for non-technical users.
- **Scalability:** MVP focus, architecture for future growth.
- **Maintainability:** Clean code, JSDoc, adhere to `.cursorrules`.

## 11. Deployment

- **Platform:** Contabo VPS.
- **Method:** Docker containers (Docker Compose).
- **CI/CD:** GitHub Actions (build), Manual deploy for MVP.
- **Environment Variables:** `.env` files.
- **Database:** PostgreSQL (Docker or managed).
- **SSL:** Let's Encrypt.

## 12. Future Considerations (Post-MVP)

- CCPA module.
- Advanced checks/AI.
- PDF reports.
- User roles/teams.
- Billing.
- Full CI/CD.

This plan is a living document and will evolve.
