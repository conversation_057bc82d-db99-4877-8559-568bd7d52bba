{"success": true, "data": {"scanId": "224bfee7-a85a-400a-91ce-e026a9585133", "status": "completed", "message": "HIPAA security scan completed successfully", "result": {"scanId": "224bfee7-a85a-400a-91ce-e026a9585133", "targetUrl": "https://www.gethealthie.com/", "scanTimestamp": "2025-06-21T17:01:28.488Z", "scanDuration": 10029, "overallScore": 84, "riskLevel": "critical", "passedTests": [{"testId": "HTTPS-001", "testName": "HTTPS Protocol Enforcement", "hipaaSection": "164.312(e)(1)", "description": "Verify that the website uses HTTPS protocol", "category": "technical", "passed": true, "evidence": "HTTPS protocol detected", "pagesTested": ["https://www.gethealthie.com/"], "timestamp": "2025-06-21T17:01:20.677Z"}, {"testId": "SSL-001", "testName": "SSL Certificate Validation", "hipaaSection": "164.312(e)(1)", "description": "Verify SSL certificate is valid and secure", "category": "technical", "passed": true, "evidence": "SSL certificate is valid. Grade: A, TLS: TLSv1.2", "pagesTested": ["www.gethealthie.com"], "timestamp": "2025-06-21T17:01:22.805Z"}, {"testId": "PRIVACY-001", "testName": "Privacy Policy Presence", "hipaaSection": "164.520", "description": "Verify that a privacy policy is accessible", "category": "administrative", "passed": true, "evidence": "Privacy policy found at: /privacy", "pagesTested": ["/privacy"], "timestamp": "2025-06-21T17:01:24.142Z"}, {"testId": "ACCESS-001", "testName": "Access Control Verification", "hipaaSection": "164.312(a)(1)", "description": "Verify that administrative endpoints require authentication", "category": "technical", "passed": true, "evidence": "All tested administrative endpoints are properly protected", "pagesTested": ["/admin", "/dashboard", "/account", "/api/users", "/management"], "timestamp": "2025-06-21T17:01:25.883Z"}, {"testId": "COOKIE-001", "testName": "Cookie Security Analysis", "hipaaSection": "164.312(a)(2)(i)", "description": "Verify that cookies are configured securely", "category": "technical", "passed": true, "evidence": "All 1 cookies are properly secured with Secure, HttpOnly, and SameSite attributes", "pagesTested": ["https://www.gethealthie.com/"], "timestamp": "2025-06-21T17:01:27.251Z"}], "failedTests": [{"testId": "HEADERS-001", "testName": "Security Headers Analysis", "hipaaSection": "164.312(e)(2)", "description": "Verify essential security headers are present", "category": "technical", "passed": false, "failureReason": "Missing 4 of 5 required security headers", "riskLevel": "medium", "failureEvidence": [{"location": "https://www.gethealthie.com/", "elementType": "header", "actualCode": "Missing header: x-frame-options", "expectedBehavior": "x-frame-options header should be present", "context": "HTTP response headers"}, {"location": "https://www.gethealthie.com/", "elementType": "header", "actualCode": "Missing header: x-content-type-options", "expectedBehavior": "x-content-type-options header should be present", "context": "HTTP response headers"}, {"location": "https://www.gethealthie.com/", "elementType": "header", "actualCode": "Missing header: content-security-policy", "expectedBehavior": "content-security-policy header should be present", "context": "HTTP response headers"}, {"location": "https://www.gethealthie.com/", "elementType": "header", "actualCode": "Missing header: x-xss-protection", "expectedBehavior": "x-xss-protection header should be present", "context": "HTTP response headers"}], "recommendedAction": "Implement missing security headers: x-frame-options, x-content-type-options, content-security-policy, x-xss-protection", "remediationPriority": 2, "timestamp": "2025-06-21T17:01:23.618Z"}, {"testId": "CONTENT-001", "testName": "ePHI Exposure Check", "hipaaSection": "164.312(a)(2)(i)", "description": "Check for potential ePHI exposure in page content", "category": "technical", "passed": false, "failureReason": "Potential ePHI exposure detected: 2 pattern types found", "riskLevel": "critical", "failureEvidence": [{"location": "https://www.gethealthie.com/", "elementType": "html", "actualCode": "Phone pattern found 1 times (samples: **********)", "expectedBehavior": "ePHI should not be exposed in public content", "context": "Content analysis for Phone exposure (risk: medium)"}, {"location": "https://www.gethealthie.com/", "elementType": "html", "actualCode": "Credit Card pattern found 2 times (samples: 1695387984553611, 1695387984553611)", "expectedBehavior": "ePHI should not be exposed in public content", "context": "Content analysis for Credit Card exposure (risk: critical)"}], "recommendedAction": "Review and remove any exposed ePHI from public pages. Implement data masking for sensitive information.", "remediationPriority": 1, "timestamp": "2025-06-21T17:01:26.999Z"}], "technicalSafeguards": {"category": "technical", "totalTests": 6, "passedTests": 4, "failedTests": 2, "score": 67, "riskLevel": "critical", "criticalIssues": 1, "highIssues": 0, "mediumIssues": 1, "lowIssues": 0}, "administrativeSafeguards": {"category": "administrative", "totalTests": 1, "passedTests": 1, "failedTests": 0, "score": 100, "riskLevel": "low", "criticalIssues": 0, "highIssues": 0, "mediumIssues": 0, "lowIssues": 0}, "organizationalSafeguards": {"category": "organizational", "totalTests": 0, "passedTests": 0, "failedTests": 0, "score": 100, "riskLevel": "low", "criticalIssues": 0, "highIssues": 0, "mediumIssues": 0, "lowIssues": 0}, "physicalSafeguards": {"category": "physical", "totalTests": 0, "passedTests": 0, "failedTests": 0, "score": 100, "riskLevel": "low", "criticalIssues": 0, "highIssues": 0, "mediumIssues": 0, "lowIssues": 0}, "vulnerabilities": [{"id": "missing-x-frame-options", "type": "Missing Security <PERSON>", "severity": "medium", "location": "https://www.gethealthie.com/", "description": "Missing x-frame-options security header", "evidence": {"missingHeader": "x-frame-options"}, "remediationGuidance": "Implement x-frame-options header for enhanced security"}, {"id": "missing-x-content-type-options", "type": "Missing Security <PERSON>", "severity": "medium", "location": "https://www.gethealthie.com/", "description": "Missing x-content-type-options security header", "evidence": {"missingHeader": "x-content-type-options"}, "remediationGuidance": "Implement x-content-type-options header for enhanced security"}, {"id": "missing-content-security-policy", "type": "Missing Security <PERSON>", "severity": "medium", "location": "https://www.gethealthie.com/", "description": "Missing content-security-policy security header", "evidence": {"missingHeader": "content-security-policy"}, "remediationGuidance": "Implement content-security-policy header for enhanced security"}], "pagesScanned": ["https://www.gethealthie.com/", "https://www.gethealthie.com/platform-overview", "https://www.gethealthie.com/intake-onboarding", "https://www.gethealthie.com/charting", "https://www.gethealthie.com/care-plans"], "toolsUsed": ["HTTP Client", "SSL Analyzer", "Content Analyzer", "Nuc<PERSON>i <PERSON>r (if available)"], "scanStatus": "completed"}}}