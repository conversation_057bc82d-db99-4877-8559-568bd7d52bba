import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries from the 'compliance_findings' table
  await knex('compliance_findings').del();

  // Find a scan to associate the findings with
  const scan = await knex('scans').where({ url: 'https://example.com' }).first();

  if (!scan) {
    console.warn(
      'Could not find scan for https://example.com to seed findings. Skipping finding seeding.',
    );
    return;
  }

  // Inserts seed entries
  await knex('compliance_findings').insert([
    {
      // id will be generated by default
      scan_id: scan.id,
      standard: 'gdpr',
      check_id: 'gdpr_cookie_consent_v1.2',
      description: 'Website does not have a clear cookie consent banner.',
      passed: false,
      severity: 'high',
      details: JSON.stringify({
        element_selector: '#cookie-banner',
        recommendation_code: 'SHOW_BANNER_ON_LOAD',
      }),
      remediation_suggestion:
        'Implement a cookie consent banner that is displayed on first visit and allows users to accept or reject cookies.',
      // created_at will use default
    },
    {
      scan_id: scan.id,
      standard: 'wcag',
      check_id: 'wcag_alt_text_missing_v2.1_img.1.1.1',
      description: 'Image missing alternative text.',
      passed: false,
      severity: 'medium',
      details: JSON.stringify({
        image_src: 'https://example.com/images/important_figure.jpg',
        element_xpath: '/html/body/main/img[1]',
      }),
      remediation_suggestion: 'Add a descriptive alt attribute to the image tag.',
    },
    {
      scan_id: scan.id,
      standard: 'wcag',
      check_id: 'wcag_color_contrast_v2.1_text.1.4.3',
      description: 'Text has insufficient color contrast with background.',
      passed: false,
      severity: 'high',
      details: JSON.stringify({
        element_selector: '.low-contrast-text',
        current_contrast_ratio: '2.5:1',
        required_contrast_ratio: '4.5:1',
      }),
      remediation_suggestion:
        'Adjust text or background color to meet WCAG AA contrast ratio requirements (4.5:1 for normal text).',
    },
  ]);

  console.log('Seeded development compliance findings successfully.');
}
