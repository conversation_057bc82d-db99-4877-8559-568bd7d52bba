import { NucleiClient } from '../services/nuclei-client';
import { ContentAnalyzer } from '../services/content-analyzer';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export class EPHIDetectionTest {
  private nucleiClient: NucleiClient;
  private contentAnalyzer: ContentAnalyzer;

  constructor(nucleiClient: NucleiClient, contentAnalyzer: ContentAnalyzer) {
    this.nucleiClient = nucleiClient;
    this.contentAnalyzer = contentAnalyzer;
  }

  async runEPHIDetectionTests(
    targetUrl: string,
    pagesToScan: string[],
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    // Test 1: ePHI Exposure in Public Pages
    results.push(await this.testEPHIExposure(targetUrl, pagesToScan));

    // Test 2: ePHI in Error Messages
    results.push(await this.testEPHIInErrors(targetUrl));

    // Test 3: ePHI in JavaScript/Client-Side Code
    results.push(await this.testEPHIInClientCode(targetUrl, pagesToScan));

    return results;
  }

  private async testEPHIExposure(
    targetUrl: string,
    pagesToScan: string[],
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'ephi-detection-exposure';
    const testName = 'ePHI Exposure Detection';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.INTEGRITY_CONTROLS;

    try {
      const ephiExposures: FailureEvidence[] = [];
      const cleanPages: string[] = [];

      for (const page of pagesToScan) {
        try {
          const fullUrl = `${targetUrl}${page}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          if (response.statusCode === 200) {
            const contentAnalysis = this.contentAnalyzer.analyzeContent(
              response.body,
              fullUrl,
              response.responseHeaders,
            );

            if (contentAnalysis.hasEPHI) {
              contentAnalysis.ephiMatches.forEach((match) => {
                ephiExposures.push({
                  location: fullUrl,
                  elementType: 'html',
                  actualCode: match.match,
                  expectedBehavior: 'ePHI should not be exposed in public pages',
                  context: `ePHI detected: ${match.pattern} at ${match.location}`,
                  lineNumber: match.lineNumber,
                });
              });
            } else {
              cleanPages.push(page);
            }
          }
        } catch (error) {
          // Page access error - continue with other pages
          continue;
        }
      }

      if (ephiExposures.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Detect ePHI exposure in publicly accessible pages',
          category: 'technical',
          passed: false,
          failureReason: `${ephiExposures.length} ePHI exposures found in public pages`,
          riskLevel: 'critical',
          failureEvidence: ephiExposures,
          recommendedAction: 'Remove or properly protect all ePHI from public pages',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in publicly accessible pages',
        category: 'technical',
        passed: true,
        evidence: `No ePHI exposure detected in ${cleanPages.length} scanned pages`,
        pagesTested: cleanPages,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in publicly accessible pages',
        category: 'technical',
        passed: false,
        failureReason: `ePHI detection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'ePHI detection test should complete successfully',
            context: 'ePHI detection test error',
          },
        ],
        recommendedAction: 'Review test configuration and page accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testEPHIInErrors(targetUrl: string): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'ephi-detection-errors';
    const testName = 'ePHI in Error Messages';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.INTEGRITY_CONTROLS;

    try {
      const errorPages = [
        '/nonexistent-page-404',
        '/admin/unauthorized-403',
        '/api/invalid-endpoint',
        '/error-test',
      ];

      const ephiInErrors: FailureEvidence[] = [];
      const cleanErrors: string[] = [];

      for (const errorPage of errorPages) {
        try {
          const fullUrl = `${targetUrl}${errorPage}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          // Check error responses (4xx, 5xx)
          if (response.statusCode >= 400) {
            const contentAnalysis = this.contentAnalyzer.analyzeContent(
              response.body,
              fullUrl,
              response.responseHeaders,
            );

            if (contentAnalysis.hasEPHI) {
              contentAnalysis.ephiMatches.forEach((match) => {
                ephiInErrors.push({
                  location: fullUrl,
                  elementType: 'response',
                  actualCode: `HTTP ${response.statusCode}: ${match.match}`,
                  expectedBehavior: 'Error messages should not contain ePHI',
                  context: `ePHI in error response: ${match.pattern}`,
                  lineNumber: match.lineNumber,
                });
              });
            } else {
              cleanErrors.push(`${errorPage} (${response.statusCode})`);
            }
          }
        } catch (error) {
          // Network errors are expected for some test URLs
          continue;
        }
      }

      if (ephiInErrors.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Detect ePHI exposure in error messages',
          category: 'technical',
          passed: false,
          failureReason: `${ephiInErrors.length} ePHI exposures found in error messages`,
          riskLevel: 'high',
          failureEvidence: ephiInErrors,
          recommendedAction: 'Sanitize error messages to remove any ePHI content',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in error messages',
        category: 'technical',
        passed: true,
        evidence: `No ePHI found in error messages: ${cleanErrors.join(', ')}`,
        pagesTested: errorPages,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in error messages',
        category: 'technical',
        passed: false,
        failureReason: `ePHI error detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'ePHI error detection should complete successfully',
            context: 'ePHI error detection test error',
          },
        ],
        recommendedAction: 'Review error handling and ePHI detection configuration',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testEPHIInClientCode(
    targetUrl: string,
    pagesToScan: string[],
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'ephi-detection-client-code';
    const testName = 'ePHI in Client-Side Code';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.INTEGRITY_CONTROLS;

    try {
      const clientCodeIssues: FailureEvidence[] = [];
      const cleanPages: string[] = [];

      for (const page of pagesToScan) {
        try {
          const fullUrl = `${targetUrl}${page}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          if (response.statusCode === 200) {
            const contentAnalysis = this.contentAnalyzer.analyzeContent(
              response.body,
              fullUrl,
              response.responseHeaders,
            );

            // Check for ePHI in JavaScript code
            const scriptIssues = contentAnalysis.scriptAnalysis.securityIssues.filter((issue) =>
              HIPAA_SECURITY_CONSTANTS.EPHI_PATTERNS.some((pattern) => pattern.test(issue)),
            );

            if (scriptIssues.length > 0) {
              scriptIssues.forEach((issue) => {
                clientCodeIssues.push({
                  location: fullUrl,
                  elementType: 'javascript',
                  actualCode: issue,
                  expectedBehavior: 'Client-side code should not contain ePHI',
                  context: 'ePHI detected in JavaScript code',
                });
              });
            }

            // Check for ePHI in inline scripts
            if (contentAnalysis.scriptAnalysis.inlineScripts > 0) {
              // Additional check for ePHI patterns in the HTML that might be in script tags
              const scriptMatches = HIPAA_SECURITY_CONSTANTS.EPHI_PATTERNS.filter((pattern) =>
                pattern.test(response.body),
              );

              if (scriptMatches.length > 0) {
                clientCodeIssues.push({
                  location: fullUrl,
                  elementType: 'javascript',
                  actualCode: 'Inline scripts contain potential ePHI patterns',
                  expectedBehavior: 'Inline scripts should not contain ePHI',
                  context: `${scriptMatches.length} ePHI patterns detected in scripts`,
                });
              }
            }

            if (clientCodeIssues.length === 0) {
              cleanPages.push(page);
            }
          }
        } catch (error) {
          // Page access error - continue with other pages
          continue;
        }
      }

      if (clientCodeIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Detect ePHI exposure in client-side code',
          category: 'technical',
          passed: false,
          failureReason: `${clientCodeIssues.length} ePHI exposures found in client-side code`,
          riskLevel: 'high',
          failureEvidence: clientCodeIssues,
          recommendedAction: 'Remove ePHI from client-side code and use server-side processing',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in client-side code',
        category: 'technical',
        passed: true,
        evidence: `No ePHI detected in client-side code of ${cleanPages.length} pages`,
        pagesTested: cleanPages,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Detect ePHI exposure in client-side code',
        category: 'technical',
        passed: false,
        failureReason: `Client-side ePHI detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Client-side ePHI detection should complete successfully',
            context: 'Client-side ePHI detection test error',
          },
        ],
        recommendedAction: 'Review client-side code analysis and test configuration',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }
}
