# Comply Checker Project Rules

## 🎯 Project Overview
This is a comprehensive SaaS platform for automated compliance scanning across HIPAA, GDPR, WCAG, and ADA standards. The system provides real-time analysis, detailed reporting, and actionable recommendations for healthcare organizations and businesses requiring compliance verification.

## 🏗️ Architecture & Stack
- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Express.js with TypeScript, PostgreSQL, Knex.js
- **Authentication**: Keycloak SSO integration
- **Containerization**: Docker with docker-compose
- **Security Scanning**: Nuclei vulnerability scanner integration
- **AI Analysis**: DistilBERT for HIPAA privacy policy analysis (upgrading to LegalBERT)

## 🚫 STRICT PROHIBITIONS

### TypeScript - ZERO TOLERANCE POLICY
- **NEVER use `any` type** - Always define precise interfaces
- **NEVER use `any[]` arrays** - Always specify array element types  
- **NEVER use `object` type** - Define specific interfaces
- **NEVER use `@ts-ignore`** - Use `@ts-expect-error` with explanation only when absolutely necessary
- **NEVER disable TypeScript strict mode** - All projects must use strict: true

### Code Quality
- **NEVER commit code with linting errors** - <PERSON><PERSON> pre-commit hooks will prevent this
- **NEVER use console.log in production** - Use proper logging with Winston
- **NEVER hardcode sensitive data** - Use environment variables
- **NEVER bypass security checks** - All endpoints must have proper authentication
- **NEVER use deprecated dependencies** - Keep dependencies updated

## ✅ REQUIRED PRACTICES

### TypeScript Standards
- **ALWAYS define interfaces** for all data structures
- **ALWAYS use union types** for known value sets: `type RiskLevel = 'critical' | 'high' | 'medium' | 'low'`
- **ALWAYS handle errors with `unknown` type** and proper type guards
- **ALWAYS enable strict mode** in tsconfig.json
- **ALWAYS use precise return types** for all functions

### Naming Conventions
- **Files**: `kebab-case.ts` (e.g., `hipaa-dashboard-api.ts`)
- **Components**: `PascalCase.tsx` (e.g., `ComplianceDashboard.tsx`)
- **Variables/Functions**: `camelCase` (e.g., `scanResults`, `calculateScore()`)
- **Constants**: `SCREAMING_SNAKE_CASE` (e.g., `MAX_SCAN_TIMEOUT`)
- **Interfaces/Types**: `PascalCase` (e.g., `HipaaScanResult`)
- **Database Tables**: `snake_case` (e.g., `compliance_findings`)

### Git Workflow
- **Branch naming**: `feature/`, `bugfix/`, `hotfix/` prefixes
- **Commit messages**: Conventional Commits format (`feat(scope): description`)
- **Pre-commit checks**: Lint, type-check, and test must pass
- **Pull requests**: Require code review and all checks passing

## 🏥 COMPLIANCE REQUIREMENTS

### HIPAA Compliance
- **3-Level Analysis System**: Level 1 (pattern matching), Level 2 (NLP), Level 3 (AI)
- **Security Safeguards**: Technical, Administrative, Physical, Organizational
- **ePHI Protection**: Automated detection and protection of electronic Protected Health Information
- **Risk-Weighted Scoring**: Security risk severity determines overall compliance score

### WCAG AA Accessibility
- **Color Contrast**: All combinations must meet 4.5:1 ratio requirement
- **Keyboard Navigation**: Full keyboard accessibility required
- **Screen Reader Support**: Comprehensive ARIA labels and semantic HTML
- **Touch Targets**: Minimum 44px for mobile accessibility

### GDPR Compliance (Expanding)
- **Cookie Consent**: Proper consent mechanisms required
- **Data Processing**: Clear data usage disclosure
- **Right to be Forgotten**: Data deletion capabilities

### ADA Compliance (Expanding)
- **Image Alt Text**: All images must have descriptive alt text
- **Form Accessibility**: Proper labels and error handling
- **Navigation**: Consistent and accessible navigation patterns

## 🎨 DESIGN SYSTEM

### WCAG AA Compliant Color Palette
- **Primary Blue**: #0055A4 (Professional, trustworthy)
- **Accent Purple**: #663399 (Modern, sophisticated)  
- **Background Gray**: #F5F5F5 (Clean, neutral)
- **Text Dark Gray**: #333333 (High contrast, readable)

### Risk Level Colors
- **Critical Risk**: #DC2626 (Red)
- **High Risk**: #EA580C (Orange)
- **Medium Risk**: #D97706 (Yellow)
- **Low Risk**: #059669 (Green)

### Typography & Spacing
- **Font Family**: ui-sans-serif, system-ui, sans-serif
- **Base Unit**: 0.25rem (4px)
- **Container Max Width**: 1200px
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)

## 🔐 SECURITY REQUIREMENTS

### Authentication & Authorization
- **Keycloak SSO**: All authentication through Keycloak
- **JWT Tokens**: Secure token-based authentication
- **Role-based Access**: Configurable user permissions
- **Session Management**: Secure session handling

### Data Protection
- **Encryption**: TLS 1.3 for all communications
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Protection**: Parameterized queries only
- **XSS Prevention**: Content Security Policy implementation
- **Rate Limiting**: API protection and throttling

### Vulnerability Scanning
- **Nuclei Integration**: Comprehensive security vulnerability scanning
- **SSL/TLS Analysis**: Certificate validation and encryption strength
- **Security Headers**: HSTS, CSP, X-Frame-Options validation
- **Regular Updates**: Monthly security patch reviews

## 📁 PROJECT STRUCTURE

### Adding New Compliance Standards
When adding new compliance standards (e.g., SOX, PCI-DSS):

**Backend Structure**:
```
backend/src/compliance/[standard]/
├── index.ts
├── types.ts  
├── orchestrator.ts
├── checks/
└── utils/
```

**Frontend Structure**:
```
frontend/
├── app/dashboard/[standard]/
├── components/compliance/[standard]/
└── types/[standard].ts
```

### Component Organization
- **Base UI**: `frontend/components/ui/` (shadcn/ui components)
- **Layout**: `frontend/components/layout/` (Header, Sidebar, Footer)
- **Feature-Specific**: `frontend/components/compliance/[standard]/`
- **Common Utilities**: `frontend/components/common/`

## 🧪 TESTING REQUIREMENTS

### Test Coverage
- **Unit Tests**: Minimum 80% coverage for business logic
- **Integration Tests**: All API endpoints must have integration tests
- **E2E Tests**: Critical user workflows must have end-to-end tests
- **Accessibility Tests**: WCAG AA compliance verification

### Test Organization
- **Backend Tests**: `backend/src/**/*.test.ts`
- **Frontend Tests**: `frontend/__tests__/**/*.test.tsx`
- **E2E Tests**: `frontend/__tests__/e2e/**/*.e2e.test.ts`

## 📚 DOCUMENTATION STANDARDS

### Code Documentation
- **JSDoc Comments**: All public functions must have JSDoc
- **Interface Documentation**: All TypeScript interfaces documented
- **API Documentation**: OpenAPI/Swagger specifications required
- **README Files**: Each major module needs README.md

### Architecture Documentation
- **Decision Records**: Document architectural decisions
- **API Changes**: Document breaking changes
- **Migration Guides**: Provide upgrade instructions
- **Troubleshooting**: Common issues and solutions

## 🚀 DEPLOYMENT & ENVIRONMENT

### Environment Management
- **Development**: Local development with Docker
- **Staging**: Docker-based staging environment
- **Production**: Optimized production containers
- **Environment Variables**: All configuration through .env files

### Performance Requirements
- **Page Load**: < 3 seconds for dashboard pages
- **API Response**: < 500ms for most endpoints
- **Scan Processing**: Progress indicators for long-running scans
- **Database Queries**: Optimized with proper indexing

## 📋 QUALITY GATES

### Pre-commit Requirements
- [ ] TypeScript compilation passes (zero errors)
- [ ] ESLint passes (zero errors/warnings)
- [ ] Unit tests pass (100% of existing tests)
- [ ] No `any` types in new code
- [ ] WCAG AA compliance maintained
- [ ] Security best practices followed

### Pre-deployment Requirements  
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Security scan passes
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Migration scripts tested

---

*These project rules ensure consistent, secure, accessible, and maintainable code across the Comply Checker platform. All team members and AI assistants must follow these guidelines without exception.*
