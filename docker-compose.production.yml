# Production Docker Compose Configuration
# Optimized for VPS deployment with monitoring and security

version: '3.8'

services:
  # Main application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: comply-checker-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - DATABASE_URL=postgresql://complyuser:${POSTGRES_PASSWORD}@postgres:5432/complydb
      - REDIS_URL=redis://redis:6379
      - KEYCLOAK_URL=http://keycloak:8080
      - KEYCLOAK_REALM=comply-checker
      - <PERSON><PERSON>YCLOAK_CLIENT_ID=comply-checker-backend
      - <PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}
      - API_KEY=${API_KEY}
      - LOG_LEVEL=info
      - ENABLE_METRICS=true
      - ENABLE_HEALTH_CHECK=true
    ports:
      - "3000:3000"
      - "9090:9090"  # Metrics port
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - comply-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: comply-checker-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=complydb
      - POSTGRES_USER=complyuser
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      - /etc/localtime:/etc/localtime:ro
    networks:
      - comply-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U complyuser -d complydb"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: comply-checker-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - /etc/localtime:/etc/localtime:ro
    networks:
      - comply-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Keycloak Authentication
  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: comply-checker-keycloak
    restart: unless-stopped
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
      - KC_DB=postgres
      - KC_DB_URL=*******************************************
      - KC_DB_USERNAME=complyuser
      - KC_DB_PASSWORD=${POSTGRES_PASSWORD}
      - KC_HOSTNAME=localhost
      - KC_HTTP_ENABLED=true
      - KC_HEALTH_ENABLED=true
      - KC_METRICS_ENABLED=true
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - keycloak_data:/opt/keycloak/data
      - /etc/localtime:/etc/localtime:ro
    networks:
      - comply-network
    command: start --optimized
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: comply-checker-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - backend
      - keycloak
    networks:
      - comply-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: comply-checker-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
      - /etc/localtime:/etc/localtime:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - comply-network
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Grafana Dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: comply-checker-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - prometheus
    networks:
      - comply-network
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

# Networks
networks:
  comply-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  keycloak_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
