// backend/src/compliance/hipaa/types.ts

import { z } from 'zod';

/**
 * HIPAA Check Categories for organizing different types of compliance checks
 */
export enum HipaaCheckCategory {
  PRESENCE = 'presence',
  ACCESSIBILITY = 'accessibility',
  CONTENT_STRUCTURE = 'content_structure',
  HIPAA_SPECIFIC = 'hipaa_specific',
  QUALITY = 'quality',
  LEGAL_COMPLIANCE = 'legal_compliance',
  CONTACT_INFO = 'contact_info',
}

/**
 * Severity levels for HIPAA compliance findings
 */
export enum HipaaSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info',
}

/**
 * Types of findings that can be identified during HIPAA analysis
 */
export enum HipaaFindingType {
  MISSING_CONTENT = 'missing_content',
  INCOMPLETE_CONTENT = 'incomplete_content',
  INCORRECT_FORMAT = 'incorrect_format',
  ACCESSIBILITY_ISSUE = 'accessibility_issue',
  ACCESSIBILITY_PASS = 'accessibility_pass',
  QUALITY_ISSUE = 'quality_issue',
  EXACT_MATCH = 'exact_match',
  PARTIAL_MATCH = 'partial_match',
  CONTEXT_MATCH = 'context_match',
  CONCEPT_MATCH = 'concept_match',
  ENTITY_FOUND = 'entity_found',
  CONCEPT_IDENTIFIED = 'concept_identified',
  COMPLIANCE_GAP = 'compliance_gap',
  RISK_FACTOR = 'risk_factor',
  LEGAL_ISSUE = 'legal_issue',
  READABILITY_ISSUE = 'readability_issue',
  ERROR = 'error',
}

/**
 * Individual finding within a HIPAA compliance check
 */
export interface HipaaFinding {
  type: HipaaFindingType;
  location: string;
  content: string;
  severity: HipaaSeverity;
  message: string;
  suggestion: string;
  context?: string;
  confidence?: number; // 0-100 confidence score
}

/**
 * Metrics collected during HIPAA analysis
 */
export interface HipaaMetrics {
  processingTime: number; // milliseconds
  contentLength: number; // characters
  sectionsFound?: number;
  patternsMatched?: number;
  entitiesExtracted?: number;
  readabilityScore?: number;
  // Additional metrics for specific checks
  linksFound?: number;
  contactMethodsFound?: number;
  accessibilityScore?: number;
  // Multi-page analysis metrics
  pagesAnalyzed?: number; // Number of privacy policy pages successfully analyzed
  totalPagesFound?: number; // Total number of privacy policy pages discovered
}

/**
 * Context information for HIPAA analysis
 */
export interface HipaaContext {
  url: string;
  pageTitle?: string;
  lastModified?: string;
  contentType?: string;
  language?: string;
}

/**
 * Detailed information about a HIPAA compliance check
 */
export interface HipaaCheckDetails {
  summary: string;
  findings: HipaaFinding[];
  metrics: HipaaMetrics;
  context: HipaaContext;
  // Allow additional properties for backward compatibility
  [key: string]: unknown;
}

/**
 * Resources for HIPAA compliance remediation
 */
export interface HipaaResource {
  title: string;
  url: string;
  type: 'documentation' | 'template' | 'guide' | 'regulation';
  description: string;
}

/**
 * Remediation guidance for HIPAA compliance issues
 */
export interface HipaaRemediation {
  priority: 'critical' | 'high' | 'medium' | 'low';
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive';
  steps: string[];
  resources: HipaaResource[];
  timeline: string;
  estimatedCost?: string;
}

/**
 * Evidence collected during HIPAA compliance analysis
 */
export interface HipaaEvidence {
  type: 'text_excerpt' | 'link' | 'screenshot' | 'metadata';
  content: string;
  location: string;
  timestamp: string;
  relevance: number; // 0-100 relevance score
}

/**
 * Metadata for HIPAA compliance checks
 */
export interface HipaaCheckMetadata {
  checkVersion: string;
  processingTime: number;
  analysisLevels: number[]; // Which levels (1, 2, 3) were executed
  error?: string;
  warnings?: string[];
}

/**
 * Enhanced result of a single HIPAA compliance check with 3-level analysis support
 */
export interface HipaaCheckResult {
  checkId: string;
  name: string;
  category: HipaaCheckCategory;
  passed: boolean;
  severity: HipaaSeverity;
  confidence: number; // 0-100 confidence score
  description: string;
  details: HipaaCheckDetails;
  remediation: HipaaRemediation;
  evidence?: HipaaEvidence[];
  metadata: HipaaCheckMetadata;

  // 3-Level Analysis Results
  levelResults?: {
    level1?: Level1Result;
    level2?: Level2Result;
    level3?: Level3Result;
  };
  overallScore?: number; // 0-100 combined score from all levels
}

// ============================================================================
// 3-LEVEL ANALYSIS TYPES
// ============================================================================

/**
 * Level 1 Analysis Result - Basic Phrase Matching
 */
export interface Level1Result {
  level: 1;
  method:
    | 'Basic Phrase Matching'
    | 'HIPAA-specific Pattern Matching'
    | 'General Privacy Pattern Matching';
  score: number; // 0-100
  foundPatterns: number;
  totalPatterns: number;
  confidence: number; // 0-100
  findings: Level1Finding[];
  processingTime: number;
}

export interface Level1Finding {
  type: 'exact_match' | 'missing_pattern';
  content: string;
  location: number;
  requirement: string;
  severity: HipaaSeverity;
  pattern: string;
}

/**
 * Level 2 Analysis Result - NLP with Compromise.js
 */
export interface Level2Result {
  level: 2;
  method: 'NLP with Compromise.js';
  score: number; // 0-100
  confidence: number; // 0-100
  entities: ExtractedEntities;
  privacyStatements: PrivacyStatement[];
  rightsStatements: RightsStatement[];
  findings: Level2Finding[];
  processingTime: number;
}

export interface ExtractedEntities {
  people: string[];
  organizations: string[];
  dates: string[];
  places: string[];
  phoneNumbers: string[];
  emails: string[];
}

export interface PrivacyStatement {
  text: string;
  type: 'protection' | 'disclosure' | 'usage' | 'rights';
  confidence: number;
  location: number;
}

export interface RightsStatement {
  text: string;
  rightType: 'access' | 'amendment' | 'restriction' | 'accounting' | 'copy';
  confidence: number;
  location: number;
}

export interface Level2Finding {
  type: 'context_match' | 'entity_found' | 'concept_identified';
  content: string;
  context: string;
  confidence: number;
  interpretation: string;
  entities?: string[];
}

/**
 * Level 3 Analysis Result - AI with LegalBERT (upgraded from DistilBERT)
 */
export interface Level3Result {
  level: 3;
  method: 'AI Analysis with LegalBERT' | 'AI Analysis with DistilBERT'; // Support both for compatibility
  score: number; // 0-100
  confidence: number; // 0-100
  identifiedGaps: ComplianceGap[];
  riskFactors: RiskFactor[];
  recommendations: AIRecommendation[];
  findings: Level3Finding[];
  positiveFindings?: PositiveFinding[]; // Compliant elements found
  processingTime: number;
}

export interface PositiveFinding {
  id: string;
  checkId: string;
  category: HipaaCheckCategory;
  title: string;
  description: string;
  evidenceText: string;
  confidenceScore: number;
  complianceContribution: number;
  reinforcementMessage: string;
  hipaaRequirement: string;
  bestPracticeLevel: 'basic' | 'good' | 'excellent';
}

// ============================================================================
// ENHANCED CONTENT CITATION AND FINDINGS TYPES
// ============================================================================

/**
 * Content citation with exact text location and context
 */
export interface ContentCitation {
  originalText: string; // Exact text found in policy
  location: {
    startIndex: number; // Character position in document
    endIndex: number;
    section?: string; // Section heading if available
    paragraph?: number; // Paragraph number
  };
  context: {
    beforeText: string; // 100 chars before
    afterText: string; // 100 chars after
    fullSentence: string; // Complete sentence containing match
  };
  confidence: number; // 0-100 confidence in citation accuracy
  relevanceScore: number; // 0-100 relevance to HIPAA requirement
}

/**
 * Enhanced recommendation with detailed implementation guidance
 */
export interface EnhancedRecommendation {
  action: string; // What to do
  implementation: string; // How to do it
  exampleLanguage: string; // Specific language to add
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high' | 'critical';
  timeline: string; // Estimated time to fix
  priority: number; // 1-10 priority ranking
  businessImpact: {
    riskReduction: string; // Risk mitigation
    complianceImprovement: number; // Expected score improvement
    auditPreparedness: string; // Audit readiness impact
  };
}

/**
 * Enhanced finding with citations and detailed recommendations
 */
export interface EnhancedFinding {
  id: string;
  type: 'positive' | 'missing' | 'risk';
  title: string;
  description: string;
  hipaaRequirement: string;
  hipaaReference: string; // CFR citation
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  citations: ContentCitation[]; // Evidence from policy
  recommendation?: EnhancedRecommendation;
  confidence: number;
  category: HipaaCheckCategory;
  checkId: string;
}

/**
 * Enhanced positive finding with improvement suggestions
 */
export interface DetailedPositiveFinding extends PositiveFinding {
  fullContext: string; // Complete surrounding context
  hipaaReference: string; // CFR citation
  citations: ContentCitation[];
  relatedChecks: string[]; // Related check IDs
  improvementSuggestions: string[]; // How to make it even better
}

export interface ComplianceGap {
  requirement: string;
  description: string;
  severity: HipaaSeverity;
  legalBasis: string;
  suggestion: string;
  confidence: number;
}

export interface RiskFactor {
  type: 'privacy' | 'security' | 'legal' | 'operational';
  description: string;
  severity: HipaaSeverity;
  likelihood: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigation: string;
}

export interface AIRecommendation {
  priority: number; // 1-10
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive';
  impact: 'low' | 'medium' | 'high';
}

export interface Level3Finding {
  type: 'compliance_gap' | 'risk_factor' | 'legal_issue';
  content: string;
  analysis: string;
  confidence: number;
  severity: HipaaSeverity;
  legalBasis: string;
  recommendation: string;
}

// ============================================================================
// ENHANCED SCAN RESULT TYPES
// ============================================================================

/**
 * Summary of a HIPAA scan with key metrics
 */
export interface HipaaScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  overallScore: number; // 0-100
  complianceLevel: 'compliant' | 'mostly_compliant' | 'partially_compliant' | 'non_compliant';
  riskLevel: 'critical' | 'high' | 'medium' | 'low'; // Risk level based on issues and score
  analysisLevelsUsed: number[]; // [1, 2, 3]
}

/**
 * High-level recommendation for HIPAA compliance improvement
 */
export interface HipaaRecommendation {
  id: string;
  priority: number; // 1-10
  title: string;
  description: string;
  category: HipaaCheckCategory;
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  resources: HipaaResource[];
  relatedChecks: string[]; // checkIds
}

/**
 * Metadata for a complete HIPAA scan
 */
export interface HipaaScanMetadata {
  version: string;
  processingTime: number;
  checksPerformed: number;
  analysisLevelsUsed: number[];
  cacheHits: number;
  errors: string[];
  warnings: string[];
  userAgent: string;
  scanOptions: HipaaScanOptions;
}

/**
 * Options for configuring HIPAA scans
 */
export interface HipaaScanOptions {
  timeout?: number; // milliseconds
  maxRedirects?: number;
  userAgent?: string;
  includeSubdomains?: boolean;
  enableLevel1?: boolean; // Basic phrase matching
  enableLevel2?: boolean; // NLP analysis
  enableLevel3?: boolean; // AI analysis
  cacheResults?: boolean;
  generateReport?: boolean;
}

/**
 * Enhanced result of a complete HIPAA scan with 3-level analysis
 */
export interface HipaaScanResult {
  targetUrl: string;
  timestamp: string;
  overallScore: number; // 0-100 compliance score
  overallPassed: boolean;
  summary: HipaaScanSummary;
  checks: HipaaCheckResult[];
  recommendations: HipaaRecommendation[];
  metadata: HipaaScanMetadata;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Configuration for check execution
 */
export interface CheckOptions {
  timeout?: number;
  retries?: number;
  cacheEnabled?: boolean;
  analysisLevel?: 1 | 2 | 3 | 'all';
  userAgent?: string;
  enableLevel1?: boolean;
  enableLevel2?: boolean;
  enableLevel3?: boolean;
}

/**
 * Pattern matching configuration
 */
export interface PatternMatch {
  pattern: RegExp;
  text: string;
  position: number;
  requirement: string;
  weight: number;
}

/**
 * HIPAA specific check configuration
 */
export interface HipaaSpecificCheckConfig {
  name: string;
  description: string;
  patterns: readonly RegExp[];
  required: boolean;
  severity: 'critical' | 'high' | 'medium' | 'low';
  hipaaReference: string;
  failureMessage: string;
  successMessage: string;
  recommendation: {
    action: string;
    implementation: string;
    exampleLanguage: string;
    effort: 'minimal' | 'moderate' | 'significant';
    impact: 'low' | 'medium' | 'high' | 'critical';
    timeline: string;
  };
}

/**
 * Enhanced HIPAA section configuration
 */
export interface EnhancedHipaaSectionConfig {
  patterns: readonly RegExp[];
  weight: number;
  description: string;
  required: boolean;
  hipaaReference: string;
  exampleLanguage: string;
}

/**
 * Content section identified during analysis
 */
export interface ContentSection {
  title: string;
  content: string;
  startPosition: number;
  endPosition: number;
  level: number; // heading level (1-6)
  subsections: ContentSection[];
}

/**
 * Link found during privacy policy discovery
 */
export interface PrivacyPolicyLink {
  url: string;
  text: string;
  type: 'privacy_policy' | 'privacy_notice' | 'hipaa_notice' | 'legal_notice' | 'compliance_notice';
  accessible: boolean;
  format: 'html' | 'pdf' | 'doc' | 'txt';
}

/**
 * Structure analysis result
 */
export interface StructureAnalysis {
  hasProperHeadings: boolean;
  headingLevels: number[];
  sectionsCount: number;
  averageSectionLength: number;
  organizationScore: number; // 0-100
  readabilityScore: number; // 0-100
}

// ============================================================================
// ZOD VALIDATION SCHEMAS
// ============================================================================

/**
 * Validation schema for HIPAA scan requests
 */
export const HipaaScanRequestSchema = z.object({
  targetUrl: z
    .string()
    .url('Invalid URL format')
    .refine((url) => {
      const parsed = new URL(url);
      return ['http:', 'https:'].includes(parsed.protocol);
    }, 'Only HTTP and HTTPS protocols are allowed')
    .refine((url) => {
      const parsed = new URL(url);
      return !['localhost', '127.0.0.1', '0.0.0.0'].includes(parsed.hostname);
    }, 'Local URLs are not allowed'),

  options: z
    .object({
      timeout: z.number().min(5000).max(60000).default(30000),
      maxRedirects: z.number().min(0).max(10).default(5),
      userAgent: z.string().max(200).optional(),
      includeSubdomains: z.boolean().default(false),
      enableLevel1: z.boolean().default(true),
      enableLevel2: z.boolean().default(true),
      enableLevel3: z.boolean().default(true),
      cacheResults: z.boolean().default(true),
      generateReport: z.boolean().default(false),
    })
    .optional(),
});

export type HipaaScanRequest = z.infer<typeof HipaaScanRequestSchema>;

/**
 * Validation schema for check options
 */
export const CheckOptionsSchema = z.object({
  timeout: z.number().min(1000).max(60000).optional(),
  retries: z.number().min(0).max(5).optional(),
  cacheEnabled: z.boolean().optional(),
  analysisLevel: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal('all')]).optional(),
});

/**
 * Validation schema for URL validation
 */
export const UrlValidationSchema = z.object({
  url: z.string().url('Invalid URL format'),
  checkAccessibility: z.boolean().default(true),
  followRedirects: z.boolean().default(true),
});

export type UrlValidationRequest = z.infer<typeof UrlValidationSchema>;

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Custom error class for HIPAA scan failures
 */
export class HipaaScanError extends Error {
  /**
   * Creates a new HipaaScanError
   */
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: unknown,
    public readonly cause?: Error,
  ) {
    super(message);
    this.name = 'HipaaScanError';
  }
}

/**
 * Custom error class for validation failures
 */
export class HipaaValidationError extends Error {
  /**
   * Creates a new HipaaValidationError
   */
  constructor(
    message: string,
    public readonly field: string,
    public readonly value: unknown,
    public readonly cause?: Error,
  ) {
    super(message);
    this.name = 'HipaaValidationError';
  }
}

/**
 * API response wrapper for HIPAA endpoints
 */
export interface HipaaApiResponse<T> {
  success: boolean;
  data: T;
  metadata: {
    timestamp: string;
    version: string;
    processingTime: number;
  };
  errors?: Array<{
    code: string;
    message: string;
    field?: string;
  }>;
  warnings?: Array<{
    code: string;
    message: string;
  }>;
}

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================

/**
 * Legacy HIPAA check result for backward compatibility
 * @deprecated Use HipaaCheckResult instead
 */
export interface LegacyHipaaCheckResult {
  checkId: string;
  name: string;
  passed: boolean;
  description?: string;
  details?: Record<string, unknown> | string;
  evidence?: unknown;
}

/**
 * Legacy HIPAA scan result for backward compatibility
 * @deprecated Use HipaaScanResult instead
 */
export interface LegacyHipaaScanResult {
  targetUrl: string;
  timestamp: string;
  overallPassed: boolean;
  checks: LegacyHipaaCheckResult[];
}
