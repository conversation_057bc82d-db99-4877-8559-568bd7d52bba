import { AdaCheckResult, AdaCheckDetail } from './types';
import { J<PERSON><PERSON> } from 'jsdom';

/**
 * Checks a given URL for ADA compliance regarding image alt attributes.
 * @param {string} targetUrl - The URL of the page to check.
 * @returns {Promise<AdaCheckResult>} A Promise resolving to an AdaCheckResult object.
 */
export async function checkImageAltText(targetUrl: string): Promise<AdaCheckResult> {
  const result: AdaCheckResult = {
    checkId: 'ADA-IMG-001',
    name: 'Image Alt Text Presence',
    description: 'Checks if all images have appropriate alt text.',
    passed: true, // Assume true until a failure is found
    details: [],
    totalElements: 0,
    nonCompliantElements: 0,
  };

  try {
    const response = await fetch(targetUrl, { headers: { 'User-Agent': 'ComplyChecker/1.0' } });
    if (!response.ok) {
      throw new Error(
        `Failed to fetch ${targetUrl}. Status: ${response.status} ${response.statusText}`,
      );
    }
    const html = await response.text();
    const dom = new JSDOM(html);
    const document = dom.window.document;

    const images = Array.from(document.querySelectorAll('img'));
    result.totalElements = images.length;
    let nonCompliantImages = 0;

    if (images.length === 0) {
      result.details.push({
        element: 'document',
        message: 'No images found on the page.',
        passed: true,
        severity: 'info',
        htmlLocation: '',
        imgSrc: '',
      });
      result.description = 'No images found on the page to check for alt text.';
      return result;
    }

    images.forEach((img, index) => {
      const imgSrc = img.src || `(Image ${index + 1} - no src)`;
      const altAttribute = img.getAttribute('alt');
      let imagePassed = true;
      let message = `Image ${index + 1} (${imgSrc.substring(0, 50)}...) has appropriate alt text.`;
      let severity: AdaCheckDetail['severity'] = 'info';

      if (altAttribute === null) {
        imagePassed = false;
        message = `Image ${index + 1} (${imgSrc.substring(0, 50)}...) is missing the alt attribute.`;
        severity = 'high';
        nonCompliantImages++;
      } else if (altAttribute.trim() === '') {
        if (altAttribute !== '') {
          // Specifically alt=" " or similar, not alt=""
          imagePassed = false;
          message = `Image ${index + 1} (${imgSrc.substring(0, 50)}...) has an alt attribute with only whitespace. This is often an error.`;
          severity = 'medium';
          nonCompliantImages++;
        } else {
          // alt="" is considered decorative and thus compliant for this check's scope
          message = `Image ${index + 1} (${imgSrc.substring(0, 50)}...) has an empty alt attribute (alt=""), considered decorative.`;
          severity = 'info';
        }
      }

      result.details.push({
        element: `img[src="${imgSrc}"] (Image ${index + 1})`,
        message: message,
        passed: imagePassed,
        severity: severity,
        htmlLocation: img.outerHTML.substring(0, 100) + '...',
        imgSrc: imgSrc,
        altText: altAttribute,
      });

      if (!imagePassed) {
        result.passed = false;
      }
    });

    result.nonCompliantElements = nonCompliantImages;
    if (nonCompliantImages > 0) {
      result.description = `Found ${nonCompliantImages} image(s) without appropriate alt text out of ${images.length} total images.`;
    } else {
      result.description = `All ${images.length} image(s) on the page have alt attributes. Empty alt attributes (alt="") are considered decorative and compliant.`;
    }
  } catch (error: unknown) {
    result.passed = false;
    const errMessage = error instanceof Error ? error.message : String(error);
    result.description = `Error checking image alt text: ${errMessage}`;
    result.details.push({
      element: 'page_processing_error',
      message: `Failed to process page for image alt text check: ${errMessage}`,
      passed: false,
      severity: 'critical',
      htmlLocation: '',
      imgSrc: '',
    });
  }

  return result;
}
