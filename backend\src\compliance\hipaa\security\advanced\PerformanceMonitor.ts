/**
 * PART 7: Advanced Performance Monitoring System
 * Enhanced based on validation findings for better reliability
 */

import { EventEmitter } from 'events';

export interface PerformanceMetrics {
  scanId: string;
  startTime: Date;
  endTime?: Date;
  totalDuration: number;

  // Test execution metrics
  testExecutionTimes: Record<string, number>;
  testSuccessRates: Record<string, number>;
  testRetryCount: Record<string, number>;

  // Network metrics
  networkLatency: number;
  averageResponseTime: number;
  timeouts: string[];
  networkErrors: number;

  // Resource usage
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };

  // Reliability metrics
  circuitBreakerTrips: number;
  retryAttempts: number;
  partialResultsGenerated: boolean;
  dataCompleteness: number;

  // Performance scores
  performanceScore: number; // 0-100
  reliabilityScore: number; // 0-100
  efficiencyScore: number; // 0-100
}

export interface PerformanceAlert {
  id: string;
  type: 'warning' | 'critical';
  category: 'performance' | 'reliability' | 'resource';
  message: string;
  threshold: number;
  actualValue: number;
  timestamp: Date;
  scanId: string;
  suggestions: string[];
}

export class PerformanceMonitor extends EventEmitter {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private alerts: PerformanceAlert[] = [];
  private thresholds = {
    maxScanDuration: 300000, // 5 minutes
    maxTestDuration: 30000, // 30 seconds
    maxNetworkLatency: 5000, // 5 seconds
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    minSuccessRate: 0.8, // 80%
    maxRetryRate: 0.2, // 20%
    minDataCompleteness: 0.7, // 70%
  };

  startMonitoring(scanId: string): void {
    const metrics: PerformanceMetrics = {
      scanId,
      startTime: new Date(),
      totalDuration: 0,
      testExecutionTimes: {},
      testSuccessRates: {},
      testRetryCount: {},
      networkLatency: 0,
      averageResponseTime: 0,
      timeouts: [],
      networkErrors: 0,
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.getCpuUsage(),
      circuitBreakerTrips: 0,
      retryAttempts: 0,
      partialResultsGenerated: false,
      dataCompleteness: 0,
      performanceScore: 100,
      reliabilityScore: 100,
      efficiencyScore: 100,
    };

    this.metrics.set(scanId, metrics);
    this.emit('monitoringStarted', { scanId, metrics });
  }

  recordTestExecution(scanId: string, testName: string, duration: number, success: boolean): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    // Record execution time
    if (!metrics.testExecutionTimes[testName]) {
      metrics.testExecutionTimes[testName] = duration;
    } else {
      // Average with previous executions
      metrics.testExecutionTimes[testName] = (metrics.testExecutionTimes[testName] + duration) / 2;
    }

    // Update success rate
    const currentSuccessRate = metrics.testSuccessRates[testName] || 1;
    const totalExecutions = (metrics.testRetryCount[testName] || 0) + 1;
    metrics.testSuccessRates[testName] = success
      ? (currentSuccessRate * (totalExecutions - 1) + 1) / totalExecutions
      : (currentSuccessRate * (totalExecutions - 1)) / totalExecutions;

    // Check for performance alerts
    this.checkTestPerformanceAlerts(scanId, testName, duration, success);

    this.emit('testExecuted', { scanId, testName, duration, success });
  }

  recordNetworkMetrics(
    scanId: string,
    latency: number,
    responseTime: number,
    timeout: boolean,
    _error: boolean,
  ): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    // Update network metrics
    metrics.networkLatency = (metrics.networkLatency + latency) / 2;
    metrics.averageResponseTime = (metrics.averageResponseTime + responseTime) / 2;

    if (timeout) {
      metrics.timeouts.push(new Date().toISOString());
    }

    if (_error) {
      metrics.networkErrors++;
    }

    // Check for network alerts
    this.checkNetworkAlerts(scanId, latency, responseTime, timeout, _error);

    this.emit('networkMetricsUpdated', { scanId, latency, responseTime, timeout, error: _error });
  }

  recordRetryAttempt(scanId: string, testName: string): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    metrics.retryAttempts++;
    metrics.testRetryCount[testName] = (metrics.testRetryCount[testName] || 0) + 1;

    // Check retry rate alerts
    this.checkRetryRateAlerts(scanId);

    this.emit('retryRecorded', { scanId, testName });
  }

  recordCircuitBreakerTrip(scanId: string): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    metrics.circuitBreakerTrips++;

    this.createAlert(scanId, {
      type: 'critical',
      category: 'reliability',
      message: 'Circuit breaker activated due to repeated failures',
      threshold: 1,
      actualValue: metrics.circuitBreakerTrips,
      suggestions: [
        'Check network connectivity',
        'Verify target website availability',
        'Consider increasing timeout values',
        'Review scan configuration',
      ],
    });

    this.emit('circuitBreakerTripped', { scanId });
  }

  recordPartialResults(scanId: string, completeness: number): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    metrics.partialResultsGenerated = true;
    metrics.dataCompleteness = completeness;

    if (completeness < this.thresholds.minDataCompleteness) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'reliability',
        message: 'Low data completeness in scan results',
        threshold: this.thresholds.minDataCompleteness,
        actualValue: completeness,
        suggestions: [
          'Review failed tests and retry if possible',
          'Check network stability during scan',
          'Consider extending timeout values',
          'Verify target website accessibility',
        ],
      });
    }

    this.emit('partialResultsRecorded', { scanId, completeness });
  }

  finishMonitoring(scanId: string): PerformanceMetrics | null {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return null;

    metrics.endTime = new Date();
    metrics.totalDuration = metrics.endTime.getTime() - metrics.startTime.getTime();

    // Update final resource usage
    metrics.memoryUsage = this.getMemoryUsage();
    metrics.cpuUsage = this.getCpuUsage();

    // Calculate performance scores
    this.calculatePerformanceScores(metrics);

    // Check final alerts
    this.checkFinalAlerts(scanId, metrics);

    this.emit('monitoringFinished', { scanId, metrics });

    return metrics;
  }

  private checkTestPerformanceAlerts(
    scanId: string,
    testName: string,
    duration: number,
    success: boolean,
  ): void {
    if (duration > this.thresholds.maxTestDuration) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'performance',
        message: `Test ${testName} exceeded maximum duration`,
        threshold: this.thresholds.maxTestDuration,
        actualValue: duration,
        suggestions: [
          'Check if target website is responding slowly',
          'Consider increasing test timeout',
          'Verify network connectivity',
          'Review test complexity',
        ],
      });
    }

    if (!success) {
      const metrics = this.metrics.get(scanId);
      if (metrics) {
        const successRate = metrics.testSuccessRates[testName] || 1;
        if (successRate < this.thresholds.minSuccessRate) {
          this.createAlert(scanId, {
            type: 'critical',
            category: 'reliability',
            message: `Test ${testName} has low success rate`,
            threshold: this.thresholds.minSuccessRate,
            actualValue: successRate,
            suggestions: [
              'Review test configuration',
              'Check target website compatibility',
              'Verify network stability',
              'Consider test-specific adjustments',
            ],
          });
        }
      }
    }
  }

  private checkNetworkAlerts(
    scanId: string,
    latency: number,
    responseTime: number,
    timeout: boolean,
    _error: boolean,
  ): void {
    if (latency > this.thresholds.maxNetworkLatency) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'performance',
        message: 'High network latency detected',
        threshold: this.thresholds.maxNetworkLatency,
        actualValue: latency,
        suggestions: [
          'Check internet connection speed',
          'Verify target website location',
          'Consider running scan during off-peak hours',
          'Check for network congestion',
        ],
      });
    }

    if (timeout) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'reliability',
        message: 'Network timeout occurred',
        threshold: 0,
        actualValue: 1,
        suggestions: [
          'Increase timeout values',
          'Check target website availability',
          'Verify network stability',
          'Consider retry mechanisms',
        ],
      });
    }
  }

  private checkRetryRateAlerts(scanId: string): void {
    const metrics = this.metrics.get(scanId);
    if (!metrics) return;

    const totalTests = Object.keys(metrics.testExecutionTimes).length;
    const retryRate = totalTests > 0 ? metrics.retryAttempts / totalTests : 0;

    if (retryRate > this.thresholds.maxRetryRate) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'reliability',
        message: 'High retry rate detected',
        threshold: this.thresholds.maxRetryRate,
        actualValue: retryRate,
        suggestions: [
          'Review scan configuration',
          'Check target website stability',
          'Verify network connectivity',
          'Consider adjusting retry policies',
        ],
      });
    }
  }

  private checkFinalAlerts(scanId: string, metrics: PerformanceMetrics): void {
    // Check total scan duration
    if (metrics.totalDuration > this.thresholds.maxScanDuration) {
      this.createAlert(scanId, {
        type: 'warning',
        category: 'performance',
        message: 'Scan exceeded maximum duration',
        threshold: this.thresholds.maxScanDuration,
        actualValue: metrics.totalDuration,
        suggestions: [
          'Consider reducing scan scope',
          'Optimize test configuration',
          'Check target website performance',
          'Review network conditions',
        ],
      });
    }

    // Check memory usage
    if (metrics.memoryUsage.heapUsed > this.thresholds.maxMemoryUsage) {
      this.createAlert(scanId, {
        type: 'critical',
        category: 'resource',
        message: 'High memory usage detected',
        threshold: this.thresholds.maxMemoryUsage,
        actualValue: metrics.memoryUsage.heapUsed,
        suggestions: [
          'Reduce concurrent operations',
          'Optimize memory usage in tests',
          'Consider scan batching',
          'Monitor for memory leaks',
        ],
      });
    }
  }

  private calculatePerformanceScores(metrics: PerformanceMetrics): void {
    // Performance score based on execution times
    const avgTestDuration =
      Object.values(metrics.testExecutionTimes).reduce((a, b) => a + b, 0) /
        Object.values(metrics.testExecutionTimes).length || 0;
    metrics.performanceScore = Math.max(
      0,
      100 - (avgTestDuration / this.thresholds.maxTestDuration) * 50,
    );

    // Reliability score based on success rates and errors
    const avgSuccessRate =
      Object.values(metrics.testSuccessRates).reduce((a, b) => a + b, 0) /
        Object.values(metrics.testSuccessRates).length || 1;
    const errorPenalty = (metrics.networkErrors + metrics.circuitBreakerTrips) * 10;
    metrics.reliabilityScore = Math.max(0, avgSuccessRate * 100 - errorPenalty);

    // Efficiency score based on resource usage and retries
    const retryPenalty = metrics.retryAttempts * 5;
    const memoryPenalty = (metrics.memoryUsage.heapUsed / this.thresholds.maxMemoryUsage) * 20;
    metrics.efficiencyScore = Math.max(0, 100 - retryPenalty - memoryPenalty);
  }

  private createAlert(
    scanId: string,
    alertData: Omit<PerformanceAlert, 'id' | 'timestamp' | 'scanId'>,
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      scanId,
      ...alertData,
    };

    this.alerts.push(alert);
    this.emit('alertCreated', alert);
  }

  private getMemoryUsage() {
    return process.memoryUsage();
  }

  private getCpuUsage() {
    return process.cpuUsage();
  }

  // Public methods for accessing data
  getMetrics(scanId: string): PerformanceMetrics | undefined {
    return this.metrics.get(scanId);
  }

  getAllMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  getAlerts(scanId?: string): PerformanceAlert[] {
    return scanId ? this.alerts.filter((alert) => alert.scanId === scanId) : this.alerts;
  }

  clearMetrics(scanId: string): void {
    this.metrics.delete(scanId);
    this.alerts = this.alerts.filter((alert) => alert.scanId !== scanId);
  }

  getThresholds() {
    return { ...this.thresholds };
  }

  updateThresholds(newThresholds: Partial<typeof this.thresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    this.emit('thresholdsUpdated', this.thresholds);
  }
}
