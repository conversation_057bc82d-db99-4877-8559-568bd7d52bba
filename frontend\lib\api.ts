// lib/api.ts
import { getKeycloakInstance } from './keycloak';
import { Api<PERSON>allOptions, <PERSON><PERSON>, <PERSON>th<PERSON><PERSON>, CreateScanPayload } from '@/backend-types/index';

const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';

/**
 * Retrieves the authentication token from Keycloak.
 * Updates the token if it's about to expire.
 * @returns {Promise<string | undefined>} A promise that resolves to the token string or undefined.
 */
async function getAuthToken(): Promise<string | undefined> {
  if (typeof window === 'undefined') return undefined; // No token on server-side
  const kc = getKeycloakInstance();
  if (kc && kc.authenticated && kc.token) {
    try {
      await kc.updateToken(5); // Update if less than 5s validity
      return kc.token;
    } catch (error) {
      // In a real app, use a logger here
      // console.error('Failed to refresh token for API call:', error);
      return undefined;
    }
  }
  return undefined;
}

/**
 * Generic function to make API calls.
 * @template T The expected type of the response data.
 * @param {string} endpoint The API endpoint to call (e.g., '/users').
 * @param {ApiCallOptions} [options] Configuration options for the call. Defaults to an empty object.
 * @returns {Promise<T>} A promise that resolves to the API response data.
 * @throws {Error} If the API call fails or returns an error status.
 */
async function apiCall<T>(endpoint: string, options: ApiCallOptions = {}): Promise<T> {
  const { method = 'GET', body } = options;
  const headers: Record<string, string> = { ...options.headers };

  const token = await getAuthToken();
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  if (method === 'POST' || method === 'PUT' || method === 'PATCH') {
    headers['Content-Type'] = 'application/json';
  }

  const config: RequestInit = {
    method,
    headers,
  };

  if (body !== undefined) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${apiBaseUrl}${endpoint}`, config);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(errorData.message || `API call failed with status ${response.status}`);
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json() as Promise<T>;
  }
  return null as unknown as T;
}

/**
 * Enhanced scan payload with options for advanced analysis
 */
interface EnhancedScanPayload extends CreateScanPayload {
  options?: {
    enableEnhancedAnalysis?: boolean;
    analysisDepth?: 'basic' | 'comprehensive';
    includePerformanceMetrics?: boolean;
  };
}

/**
 * Submits a new scan request.
 * @param {CreateScanPayload | EnhancedScanPayload} payload The payload containing the URL and standards.
 * @returns {Promise<Scan>} A promise that resolves to the scan object.
 */
export const submitScan = (payload: CreateScanPayload | EnhancedScanPayload): Promise<Scan> => {
  return apiCall<Scan>('/compliance/scans', {
    method: 'POST',
    body: payload,
  });
};

/**
 * Lists all scans for the authenticated user.
 * @returns {Promise<Scan[]>} A promise that resolves to an array of scans.
 */
export const listScans = (): Promise<Scan[]> => {
  return apiCall<Scan[]>('/compliance/scans');
};

/**
 * Fetches the details of a specific scan.
 * @param {string} scanId The ID of the scan to retrieve.
 * @returns {Promise<Scan>} A promise that resolves to the scan details.
 */
/**
 * Fetches the current authenticated user's profile.
 * @returns {Promise<AuthUser>} A promise that resolves to the user's profile data.
 */
export const getAuthMe = (): Promise<AuthUser> => {
  return apiCall<AuthUser>('/auth/me'); // Assuming '/auth/me' is the endpoint
};

/**
 * HIPAA Security Scan Types
 */
interface HipaaSecurityScanRequest {
  targetUrl: string;
  maxPages?: number;
  scanDepth?: number;
  timeout?: number;
  enableVulnerabilityScanning?: boolean;
  enableSSLAnalysis?: boolean;
  enableContentAnalysis?: boolean;
}

interface HipaaSecurityScanResponse {
  success: boolean;
  data: {
    scanId: string;
    status: string;
    message: string;
    result: Record<string, unknown>;
  };
}

/**
 * Submits a HIPAA security scan request.
 * @param {HipaaSecurityScanRequest} payload The payload containing the scan configuration.
 * @returns {Promise<HipaaSecurityScanResponse>} A promise that resolves to the scan response.
 */
export const submitHipaaSecurityScan = (
  payload: HipaaSecurityScanRequest,
): Promise<HipaaSecurityScanResponse> => {
  return apiCall<HipaaSecurityScanResponse>('/hipaa-security/scan', {
    method: 'POST',
    body: payload,
  });
};

export const getScanDetails = (scanId: string): Promise<Scan> => {
  return apiCall<Scan>(`/compliance/scans/${scanId}`);
};
