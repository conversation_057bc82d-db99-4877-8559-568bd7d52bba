#!/bin/bash

# Production Deployment Script for HIPAA Compliance Checker
# Optimized for VPS deployment with comprehensive checks

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="/var/backups/comply-checker"
LOG_FILE="/var/log/comply-checker-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check available memory (minimum 4GB recommended)
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 4 ]]; then
        warning "System has less than 4GB RAM. Performance may be affected."
    fi
    
    # Check available disk space (minimum 10GB)
    DISK_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_GB -lt 10 ]]; then
        warning "Less than 10GB disk space available. Consider freeing up space."
    fi
    
    success "System requirements check completed"
}

# Load environment variables
load_environment() {
    log "Loading environment configuration..."
    
    if [[ ! -f "$PROJECT_ROOT/.env.production" ]]; then
        error "Production environment file (.env.production) not found"
    fi
    
    # Source the environment file
    set -a
    source "$PROJECT_ROOT/.env.production"
    set +a
    
    # Validate required environment variables
    REQUIRED_VARS=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "KEYCLOAK_ADMIN_PASSWORD"
        "KEYCLOAK_CLIENT_SECRET"
        "API_KEY"
    )
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error "Required environment variable $var is not set"
        fi
    done
    
    success "Environment configuration loaded"
}

# Create backup
create_backup() {
    log "Creating backup of current deployment..."
    
    # Create backup directory
    sudo mkdir -p "$BACKUP_DIR"
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    
    # Backup database if running
    if docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" ps postgres | grep -q "Up"; then
        log "Backing up database..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec -T postgres \
            pg_dump -U complyuser complydb > "$BACKUP_PATH.sql" || warning "Database backup failed"
    fi
    
    # Backup application data
    if [[ -d "$PROJECT_ROOT/data" ]]; then
        sudo tar -czf "$BACKUP_PATH.tar.gz" -C "$PROJECT_ROOT" data/ || warning "Data backup failed"
    fi
    
    success "Backup created at $BACKUP_PATH"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if ports are available
    PORTS=(80 443 3000 5432 6379 8080)
    for port in "${PORTS[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            warning "Port $port is already in use"
        fi
    done
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    # Validate Docker Compose file
    if ! docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" config &> /dev/null; then
        error "Docker Compose configuration is invalid"
    fi
    
    success "Pre-deployment checks completed"
}

# Build and deploy
deploy_application() {
    log "Starting application deployment..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose -f docker-compose.production.yml pull
    
    # Build application
    log "Building application..."
    docker-compose -f docker-compose.production.yml build --no-cache backend
    
    # Start services
    log "Starting services..."
    docker-compose -f docker-compose.production.yml up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    SERVICES=(postgres redis backend keycloak nginx)
    for service in "${SERVICES[@]}"; do
        if ! docker-compose -f docker-compose.production.yml ps "$service" | grep -q "Up"; then
            error "Service $service failed to start"
        fi
    done
    
    success "Application deployed successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations
    docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec -T backend \
        npm run migrate:latest || error "Database migration failed"
    
    success "Database migrations completed"
}

# Health checks
health_checks() {
    log "Running health checks..."
    
    # Check application health endpoint
    for i in {1..30}; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            success "Application health check passed"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            error "Application health check failed after 30 attempts"
        fi
        
        log "Waiting for application to be ready... (attempt $i/30)"
        sleep 10
    done
    
    # Check database connectivity
    if ! docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec -T postgres \
        psql -U complyuser -d complydb -c "SELECT 1;" &> /dev/null; then
        error "Database connectivity check failed"
    fi
    
    # Check Keycloak
    for i in {1..20}; do
        if curl -f http://localhost:8080/health/ready &> /dev/null; then
            success "Keycloak health check passed"
            break
        fi
        
        if [[ $i -eq 20 ]]; then
            warning "Keycloak health check failed - may need manual configuration"
            break
        fi
        
        log "Waiting for Keycloak to be ready... (attempt $i/20)"
        sleep 15
    done
    
    success "Health checks completed"
}

# Setup monitoring (optional)
setup_monitoring() {
    if [[ "${ENABLE_MONITORING:-false}" == "true" ]]; then
        log "Setting up monitoring stack..."
        
        docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" \
            --profile monitoring up -d
        
        success "Monitoring stack deployed"
    fi
}

# Post-deployment tasks
post_deployment() {
    log "Running post-deployment tasks..."
    
    # Setup log rotation
    sudo tee /etc/logrotate.d/comply-checker > /dev/null <<EOF
$PROJECT_ROOT/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
}
EOF
    
    # Setup systemd service for auto-restart
    sudo tee /etc/systemd/system/comply-checker.service > /dev/null <<EOF
[Unit]
Description=Comply Checker Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$PROJECT_ROOT
ExecStart=/usr/local/bin/docker-compose -f docker-compose.production.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.production.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable comply-checker.service
    
    success "Post-deployment tasks completed"
}

# Cleanup old resources
cleanup() {
    log "Cleaning up old resources..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove old backups (keep last 7 days)
    find "$BACKUP_DIR" -name "backup_*" -mtime +7 -delete 2>/dev/null || true
    
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting production deployment of HIPAA Compliance Checker"
    
    check_root
    check_requirements
    load_environment
    create_backup
    pre_deployment_checks
    deploy_application
    run_migrations
    health_checks
    setup_monitoring
    post_deployment
    cleanup
    
    success "Production deployment completed successfully!"
    log "Application is available at: http://localhost"
    log "Keycloak admin console: http://localhost:8080"
    
    if [[ "${ENABLE_MONITORING:-false}" == "true" ]]; then
        log "Grafana dashboard: http://localhost:3001"
        log "Prometheus metrics: http://localhost:9091"
    fi
    
    log "Check logs at: $LOG_FILE"
}

# Run main function
main "$@"
