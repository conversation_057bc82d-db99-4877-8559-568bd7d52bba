// backend/src/compliance/hipaa/privacy/database/hipaa-privacy-db.ts

import knex from '../../../../lib/db';
import { Knex } from 'knex';
import {
  HipaaScanResult,
  HipaaCheckResult,
  HipaaFinding,
  HipaaEvidence,
  HipaaRecommendation,
  Level1Result,
  Level2Result,
  Level3Result,
} from '../types';

// Database record types
interface DatabaseRecord {
  [key: string]: unknown;
}

import { HipaaRemediation } from '../types';

/**
 * Database integration functions for HIPAA compliance analysis
 * Handles storage and retrieval of comprehensive 3-level analysis results
 */
export class HipaaDatabase {
  /**
   * Saves a complete HIPAA scan result to the database
   */
  static async saveScanResult(scanId: string, scanResult: HipaaScanResult): Promise<string> {
    console.log('💾 [Database Storage] Starting enhanced HIPAA results storage');
    console.log('📊 [Database Storage] Scan details:', {
      scanId: scanId,
      targetUrl: scanResult.targetUrl,
      overallScore: scanResult.overallScore,
      overallPassed: scanResult.overallPassed,
      checksCount: scanResult.checks.length,
      recommendationsCount: scanResult.recommendations.length,
      processingTime: scanResult.metadata.processingTime + 'ms',
    });

    const trx = await knex.transaction();

    try {
      console.log('🔄 [Database Storage] Starting database transaction...');

      // Insert main HIPAA scan record
      console.log('📝 [Database Storage] Inserting main HIPAA scan record...');
      const [hipaaScan] = await trx('hipaa_scans')
        .insert({
          scan_id: scanId,
          target_url: scanResult.targetUrl,
          overall_score: Math.round(scanResult.overallScore),
          overall_passed: scanResult.overallPassed,
          compliance_level: scanResult.summary.complianceLevel,
          analysis_levels_used: JSON.stringify(scanResult.summary.analysisLevelsUsed),
          processing_time_ms: scanResult.metadata.processingTime,
          total_checks: scanResult.summary.totalChecks,
          passed_checks: scanResult.summary.passedChecks,
          failed_checks: scanResult.summary.failedChecks,
          critical_issues: scanResult.summary.criticalIssues,
          high_issues: scanResult.summary.highIssues,
          medium_issues: scanResult.summary.mediumIssues,
          low_issues: scanResult.summary.lowIssues,
          scan_options: JSON.stringify(scanResult.metadata.scanOptions),
          cache_hits: scanResult.metadata.cacheHits,
          errors: JSON.stringify(scanResult.metadata.errors),
          warnings: JSON.stringify(scanResult.metadata.warnings),
          user_agent: scanResult.metadata.userAgent,
          scan_version: scanResult.metadata.version,
          completed_at: new Date(),
        })
        .returning('id');

      const hipaaScanId = hipaaScan.id;
      console.log('✅ [Database Storage] Main scan record saved with ID:', hipaaScanId);

      // Save each check result
      console.log('📋 [Database Storage] Saving check results...');
      for (let i = 0; i < scanResult.checks.length; i++) {
        const checkResult = scanResult.checks[i];
        console.log(
          `📝 [Database Storage] Saving check ${i + 1}/${scanResult.checks.length}: ${checkResult.name}`,
        );
        await this.saveCheckResult(trx, hipaaScanId, checkResult);
      }
      console.log('✅ [Database Storage] All check results saved');

      // Save recommendations
      console.log('💡 [Database Storage] Saving recommendations...');
      for (let i = 0; i < scanResult.recommendations.length; i++) {
        const recommendation = scanResult.recommendations[i];
        console.log(
          `📝 [Database Storage] Saving recommendation ${i + 1}/${scanResult.recommendations.length}: ${recommendation.title}`,
        );
        await this.saveRecommendation(trx, hipaaScanId, recommendation);
      }
      console.log('✅ [Database Storage] All recommendations saved');

      console.log('🔄 [Database Storage] Committing transaction...');
      await trx.commit();

      console.log('🎯 [Database Storage] Enhanced HIPAA results storage complete:', {
        hipaaScanId: hipaaScanId,
        scanId: scanId,
        checksStored: scanResult.checks.length,
        recommendationsStored: scanResult.recommendations.length,
        totalFindings: scanResult.checks.reduce(
          (sum, check) => sum + check.details.findings.length,
          0,
        ),
      });

      return hipaaScanId;
    } catch (error) {
      console.error('❌ [Database Storage] Error saving enhanced HIPAA results:', error);
      await trx.rollback();
      throw error;
    }
  }

  /**
   * Saves an individual HIPAA check result
   */
  private static async saveCheckResult(
    trx: Knex.Transaction,
    hipaaScanId: string,
    checkResult: HipaaCheckResult,
  ): Promise<string> {
    // Insert check result
    const [checkRecord] = await trx('hipaa_check_results')
      .insert({
        hipaa_scan_id: hipaaScanId,
        check_id: checkResult.checkId,
        name: checkResult.name,
        category: checkResult.category,
        passed: checkResult.passed,
        severity: checkResult.severity,
        confidence: Math.round(checkResult.confidence),
        description: checkResult.description,
        overall_score: checkResult.overallScore ? Math.round(checkResult.overallScore) : null,
        processing_time_ms: checkResult.metadata.processingTime,
        check_version: checkResult.metadata.checkVersion,
        analysis_levels_executed: JSON.stringify(checkResult.metadata.analysisLevels),
        errors: checkResult.metadata.error ? JSON.stringify([checkResult.metadata.error]) : null,
        warnings: checkResult.metadata.warnings
          ? JSON.stringify(checkResult.metadata.warnings)
          : null,
      })
      .returning('id');

    const checkResultId = checkRecord.id;

    // Save findings
    for (const finding of checkResult.details.findings) {
      await this.saveFinding(trx, checkResultId, finding);
    }

    // Save remediation
    await this.saveRemediation(trx, checkResultId, checkResult.remediation);

    // Save evidence if present
    if (checkResult.evidence) {
      for (const evidence of checkResult.evidence) {
        await this.saveEvidence(trx, checkResultId, evidence);
      }
    }

    // Save level results if present
    if (checkResult.levelResults) {
      if (checkResult.levelResults.level1) {
        await this.saveLevel1Result(trx, checkResultId, checkResult.levelResults.level1);
      }
      if (checkResult.levelResults.level2) {
        await this.saveLevel2Result(trx, checkResultId, checkResult.levelResults.level2);
      }
      if (checkResult.levelResults.level3) {
        await this.saveLevel3Result(trx, checkResultId, checkResult.levelResults.level3);
      }
    }

    return checkResultId;
  }

  /**
   * Saves a finding to the database
   */
  private static async saveFinding(
    trx: Knex.Transaction,
    checkResultId: string,
    finding: HipaaFinding,
  ): Promise<void> {
    await trx('hipaa_findings').insert({
      check_result_id: checkResultId,
      type: finding.type,
      location: finding.location,
      content: finding.content,
      severity: finding.severity,
      message: finding.message,
      suggestion: finding.suggestion,
      context: finding.context,
      confidence: finding.confidence ? Math.round(finding.confidence) : null,
    });
  }

  /**
   * Saves remediation guidance to the database
   */
  private static async saveRemediation(
    trx: Knex.Transaction,
    checkResultId: string,
    remediation: HipaaRemediation,
  ): Promise<void> {
    // Safe JSON stringify helper
    const stringifyJsonSafely = (data: unknown) => {
      if (data === null || data === undefined) {
        return JSON.stringify([]);
      }
      try {
        return JSON.stringify(data);
      } catch (error) {
        console.warn('Failed to stringify JSON:', data, error);
        return JSON.stringify([]);
      }
    };

    await trx('hipaa_remediation').insert({
      check_result_id: checkResultId,
      priority: remediation.priority || 'low',
      effort: remediation.effort || 'minimal',
      steps: stringifyJsonSafely(remediation.steps),
      resources: stringifyJsonSafely(remediation.resources),
      timeline: remediation.timeline || 'N/A',
      estimated_cost: remediation.estimatedCost || null,
    });
  }

  /**
   * Saves evidence to the database
   */
  private static async saveEvidence(
    trx: Knex.Transaction,
    checkResultId: string,
    evidence: HipaaEvidence,
  ): Promise<void> {
    await trx('hipaa_evidence').insert({
      check_result_id: checkResultId,
      type: evidence.type,
      content: evidence.content,
      location: evidence.location,
      relevance: Math.round(evidence.relevance),
      evidence_timestamp: new Date(evidence.timestamp),
    });
  }

  /**
   * Saves Level 1 analysis result
   */
  private static async saveLevel1Result(
    trx: Knex.Transaction,
    checkResultId: string,
    level1: Level1Result,
  ): Promise<void> {
    await trx('hipaa_analysis_levels').insert({
      check_result_id: checkResultId,
      level: 1,
      method: level1.method,
      score: Math.round(level1.score),
      confidence: Math.round(level1.confidence),
      processing_time_ms: level1.processingTime,
      found_patterns: level1.foundPatterns,
      total_patterns: level1.totalPatterns,
      level_findings: JSON.stringify(level1.findings),
    });
  }

  /**
   * Saves Level 2 analysis result
   */
  private static async saveLevel2Result(
    trx: Knex.Transaction,
    checkResultId: string,
    level2: Level2Result,
  ): Promise<void> {
    await trx('hipaa_analysis_levels').insert({
      check_result_id: checkResultId,
      level: 2,
      method: level2.method,
      score: Math.round(level2.score),
      confidence: Math.round(level2.confidence),
      processing_time_ms: level2.processingTime,
      entities: JSON.stringify(level2.entities),
      privacy_statements: JSON.stringify(level2.privacyStatements),
      rights_statements: JSON.stringify(level2.rightsStatements),
      level_findings: JSON.stringify(level2.findings),
    });
  }

  /**
   * Saves Level 3 analysis result
   */
  private static async saveLevel3Result(
    trx: Knex.Transaction,
    checkResultId: string,
    level3: Level3Result,
  ): Promise<void> {
    // Safe JSON stringify helper
    const stringifyJsonSafely = (data: unknown) => {
      if (data === null || data === undefined) {
        return JSON.stringify([]);
      }
      try {
        return JSON.stringify(data);
      } catch (error) {
        console.warn('Failed to stringify JSON:', data, error);
        return JSON.stringify([]);
      }
    };

    await trx('hipaa_analysis_levels').insert({
      check_result_id: checkResultId,
      level: 3,
      method: level3.method,
      score: Math.round(level3.score),
      confidence: Math.round(level3.confidence),
      processing_time_ms: level3.processingTime,
      compliance_gaps: stringifyJsonSafely(level3.identifiedGaps),
      risk_factors: stringifyJsonSafely(level3.riskFactors),
      ai_recommendations: stringifyJsonSafely(level3.recommendations),
      level_findings: stringifyJsonSafely(level3.findings),
      positive_findings: stringifyJsonSafely(
        (level3 as Level3Result & { positiveFindings?: unknown }).positiveFindings,
      ),
    });
  }

  /**
   * Saves a recommendation to the database
   */
  private static async saveRecommendation(
    trx: Knex.Transaction,
    hipaaScanId: string,
    recommendation: HipaaRecommendation,
  ): Promise<void> {
    // Safe JSON stringify helper
    const stringifyJsonSafely = (data: unknown) => {
      if (data === null || data === undefined) {
        return JSON.stringify([]);
      }
      try {
        return JSON.stringify(data);
      } catch (error) {
        console.warn('Failed to stringify JSON:', data, error);
        return JSON.stringify([]);
      }
    };

    await trx('hipaa_recommendations').insert({
      hipaa_scan_id: hipaaScanId,
      recommendation_id: recommendation.id,
      priority: recommendation.priority,
      title: recommendation.title,
      description: recommendation.description,
      category: recommendation.category,
      effort: recommendation.effort,
      impact: recommendation.impact,
      timeline: recommendation.timeline,
      resources: stringifyJsonSafely(recommendation.resources),
      related_checks: stringifyJsonSafely(recommendation.relatedChecks),
    });
  }

  /**
   * Retrieves a complete HIPAA scan result from the database
   */
  static async getScanResult(scanId: string): Promise<HipaaScanResult | null> {
    const scanRecord = await knex('hipaa_scans').where('scan_id', scanId).first();

    if (!scanRecord) {
      return null;
    }

    // Get check results using the hipaa_scans.id (internal ID)
    const checkResults = await this.getCheckResults(scanRecord.id);

    // Get recommendations using the hipaa_scans.id (internal ID)
    const recommendations = await this.getRecommendations(scanRecord.id);

    // Safe JSON parsing helper
    const parseJsonSafely = (jsonString: string | null | undefined, fallback: unknown = []) => {
      if (!jsonString || typeof jsonString !== 'string' || jsonString.trim() === '') {
        return fallback;
      }
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`Failed to parse JSON: ${jsonString}`, error);
        return fallback;
      }
    };

    // Calculate risk level based on issues
    const calculateRiskLevel = (
      criticalIssues: number,
      highIssues: number,
      mediumIssues: number,
      _lowIssues: number,
    ): 'critical' | 'high' | 'medium' | 'low' => {
      if (criticalIssues > 0) return 'critical';
      if (highIssues > 0) return 'high';
      if (mediumIssues > 0) return 'medium';
      return 'low';
    };

    // Reconstruct the scan result
    const scanResult: HipaaScanResult = {
      targetUrl: scanRecord.target_url,
      timestamp: scanRecord.created_at.toISOString(),
      overallScore: scanRecord.overall_score,
      overallPassed: scanRecord.overall_passed,
      summary: {
        totalChecks: scanRecord.total_checks,
        passedChecks: scanRecord.passed_checks,
        failedChecks: scanRecord.failed_checks,
        criticalIssues: scanRecord.critical_issues,
        highIssues: scanRecord.high_issues,
        mediumIssues: scanRecord.medium_issues,
        lowIssues: scanRecord.low_issues,
        overallScore: scanRecord.overall_score,
        complianceLevel: scanRecord.compliance_level,
        riskLevel: calculateRiskLevel(
          scanRecord.critical_issues,
          scanRecord.high_issues,
          scanRecord.medium_issues,
          scanRecord.low_issues,
        ),
        analysisLevelsUsed: parseJsonSafely(scanRecord.analysis_levels_used, []),
      },
      checks: checkResults,
      recommendations,
      metadata: {
        version: scanRecord.scan_version,
        processingTime: scanRecord.processing_time_ms,
        checksPerformed: scanRecord.total_checks,
        analysisLevelsUsed: parseJsonSafely(scanRecord.analysis_levels_used, []),
        cacheHits: scanRecord.cache_hits,
        errors: parseJsonSafely(scanRecord.errors, []),
        warnings: parseJsonSafely(scanRecord.warnings, []),
        userAgent: scanRecord.user_agent,
        scanOptions: parseJsonSafely(scanRecord.scan_options, {}),
      },
    };

    return scanResult;
  }

  /**
   * Retrieves check results for a scan
   */
  private static async getCheckResults(hipaaScanId: string): Promise<HipaaCheckResult[]> {
    const checkRecords = await knex('hipaa_check_results')
      .where('hipaa_scan_id', hipaaScanId)
      .orderBy('created_at');

    const checkResults: HipaaCheckResult[] = [];

    // Safe JSON parsing helper
    const parseJsonSafely = (jsonString: string | null | undefined, fallback: unknown = []) => {
      if (!jsonString || typeof jsonString !== 'string' || jsonString.trim() === '') {
        return fallback;
      }
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`Failed to parse JSON: ${jsonString}`, error);
        return fallback;
      }
    };

    for (const record of checkRecords) {
      const findings = await this.getFindings(record.id);
      const remediation = await this.getRemediation(record.id);
      const evidence = await this.getEvidence(record.id);
      const levelResults = await this.getLevelResults(record.id);

      const checkResult: HipaaCheckResult = {
        checkId: record.check_id,
        name: record.name,
        category: record.category,
        passed: record.passed,
        severity: record.severity,
        confidence: record.confidence,
        description: record.description,
        details: {
          summary: record.description,
          findings,
          metrics: {
            processingTime: record.processing_time_ms,
            contentLength: 0, // Will be populated from content analysis
            sectionsFound: 0,
            patternsMatched: 0,
            entitiesExtracted: 0,
          },
          context: {
            url: '', // Will be populated from scan context
          },
        },
        remediation,
        evidence,
        metadata: {
          checkVersion: record.check_version,
          processingTime: record.processing_time_ms,
          analysisLevels: parseJsonSafely(record.analysis_levels_executed, []),
          error: record.errors ? parseJsonSafely(record.errors, [])[0] : undefined,
          warnings: record.warnings ? parseJsonSafely(record.warnings, []) : undefined,
        },
        levelResults,
        overallScore: record.overall_score,
      };

      checkResults.push(checkResult);
    }

    return checkResults;
  }

  /**
   * Helper methods for retrieving related data
   */
  private static async getFindings(checkResultId: string): Promise<HipaaFinding[]> {
    const records = await knex('hipaa_findings')
      .where('check_result_id', checkResultId)
      .orderBy('created_at');

    return records.map((record: DatabaseRecord) => ({
      type: record.type as HipaaFinding['type'],
      location: record.location as string,
      content: record.content as string,
      severity: record.severity as HipaaFinding['severity'],
      message: record.message as string,
      suggestion: record.suggestion as string,
      context: record.context as string,
      confidence: record.confidence as number,
    }));
  }

  private static async getRemediation(checkResultId: string): Promise<HipaaRemediation> {
    const record = await knex('hipaa_remediation').where('check_result_id', checkResultId).first();

    if (!record) {
      return {
        priority: 'low',
        effort: 'minimal',
        steps: [],
        resources: [],
        timeline: 'N/A',
      };
    }

    // Safe JSON parsing with fallbacks
    const parseJsonSafely = (jsonString: string | null | undefined, fallback: unknown = []) => {
      if (!jsonString || typeof jsonString !== 'string' || jsonString.trim() === '') {
        return fallback;
      }
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`Failed to parse JSON: ${jsonString}`, error);
        return fallback;
      }
    };

    return {
      priority: record.priority as HipaaRemediation['priority'],
      effort: record.effort as HipaaRemediation['effort'],
      steps: parseJsonSafely(record.steps, []) as string[],
      resources: parseJsonSafely(record.resources, []) as HipaaRemediation['resources'],
      timeline: record.timeline as string,
      estimatedCost: record.estimated_cost as string,
    };
  }

  private static async getEvidence(checkResultId: string): Promise<HipaaEvidence[]> {
    const records = await knex('hipaa_evidence')
      .where('check_result_id', checkResultId)
      .orderBy('relevance', 'desc');

    return records.map((record: DatabaseRecord) => ({
      type: record.type as HipaaEvidence['type'],
      content: record.content as string,
      location: record.location as string,
      timestamp: (record.evidence_timestamp as Date).toISOString(),
      relevance: record.relevance as number,
    }));
  }

  private static async getLevelResults(checkResultId: string): Promise<{
    level1?: Level1Result;
    level2?: Level2Result;
    level3?: Level3Result;
  }> {
    const records = await knex('hipaa_analysis_levels')
      .where('check_result_id', checkResultId)
      .orderBy('level');

    console.log(
      `🔍 [DEBUG] getLevelResults for checkResultId: ${checkResultId}, found ${records.length} records`,
    );

    const levelResults: {
      level1?: Level1Result;
      level2?: Level2Result;
      level3?: Level3Result;
      [key: string]: Level1Result | Level2Result | Level3Result | undefined;
    } = {};

    // Safe JSON parsing helper
    const parseJsonSafely = (jsonString: string | null | undefined, fallback: unknown = []) => {
      if (!jsonString || typeof jsonString !== 'string' || jsonString.trim() === '') {
        return fallback;
      }
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`Failed to parse JSON: ${jsonString}`, error);
        return fallback;
      }
    };

    for (const record of records) {
      const levelKey = `level${record.level}`;

      if (record.level === 3) {
        console.log(`🔍 [DEBUG] Level 3 record raw data:`, {
          id: record.id,
          positive_findings: record.positive_findings ? 'HAS DATA' : 'NULL/EMPTY',
          ai_recommendations: record.ai_recommendations ? 'HAS DATA' : 'NULL/EMPTY',
          risk_factors: record.risk_factors ? 'HAS DATA' : 'NULL/EMPTY',
          compliance_gaps: record.compliance_gaps ? 'HAS DATA' : 'NULL/EMPTY',
        });
      }

      // Create properly typed level results based on the level
      if (record.level === 1) {
        const level1Result: Level1Result = {
          level: 1,
          method: record.method as Level1Result['method'],
          score: record.score as number,
          confidence: record.confidence as number,
          processingTime: record.processing_time_ms as number,
          foundPatterns: record.found_patterns as number,
          totalPatterns: record.total_patterns as number,
          findings: parseJsonSafely(record.level_findings, []) as Level1Result['findings'],
        };
        levelResults[levelKey] = level1Result;
      } else if (record.level === 2) {
        const level2Result: Level2Result = {
          level: 2,
          method: record.method as Level2Result['method'],
          score: record.score as number,
          confidence: record.confidence as number,
          processingTime: record.processing_time_ms as number,
          entities: parseJsonSafely(record.entities, {}) as Level2Result['entities'],
          privacyStatements: parseJsonSafely(
            record.privacy_statements,
            [],
          ) as Level2Result['privacyStatements'],
          rightsStatements: parseJsonSafely(
            record.rights_statements,
            [],
          ) as Level2Result['rightsStatements'],
          findings: parseJsonSafely(record.level_findings, []) as Level2Result['findings'],
        };
        levelResults[levelKey] = level2Result;
      } else if (record.level === 3) {
        const level3Result: Level3Result = {
          level: 3,
          method: record.method as Level3Result['method'],
          score: record.score as number,
          confidence: record.confidence as number,
          processingTime: record.processing_time_ms as number,
          identifiedGaps: parseJsonSafely(
            record.compliance_gaps,
            [],
          ) as Level3Result['identifiedGaps'],
          riskFactors: parseJsonSafely(record.risk_factors, []) as Level3Result['riskFactors'],
          recommendations: parseJsonSafely(
            record.ai_recommendations,
            [],
          ) as Level3Result['recommendations'],
          findings: parseJsonSafely(record.level_findings, []) as Level3Result['findings'],
          positiveFindings: parseJsonSafely(
            record.positive_findings,
            [],
          ) as Level3Result['positiveFindings'],
        };
        levelResults[levelKey] = level3Result;

        console.log(`🔍 [DEBUG] Level 3 parsed data:`, {
          positiveFindings: level3Result.positiveFindings?.length || 0,
          recommendations: level3Result.recommendations?.length || 0,
          riskFactors: level3Result.riskFactors?.length || 0,
          identifiedGaps: level3Result.identifiedGaps?.length || 0,
        });
      }
    }

    console.log(`🔍 [DEBUG] Final levelResults keys:`, Object.keys(levelResults));
    return levelResults;
  }

  private static async getRecommendations(hipaaScanId: string): Promise<HipaaRecommendation[]> {
    const records = await knex('hipaa_recommendations')
      .where('hipaa_scan_id', hipaaScanId)
      .orderBy('priority');

    // Safe JSON parsing helper
    const parseJsonSafely = (jsonString: string | null | undefined, fallback: unknown = []) => {
      if (!jsonString || typeof jsonString !== 'string' || jsonString.trim() === '') {
        return fallback;
      }
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn(`Failed to parse JSON: ${jsonString}`, error);
        return fallback;
      }
    };

    return records.map((record: DatabaseRecord) => ({
      id: record.recommendation_id as string,
      priority: record.priority as HipaaRecommendation['priority'],
      title: record.title as string,
      description: record.description as string,
      category: record.category as HipaaRecommendation['category'],
      effort: record.effort as HipaaRecommendation['effort'],
      impact: record.impact as HipaaRecommendation['impact'],
      timeline: record.timeline as string,
      resources: parseJsonSafely(
        record.resources as string,
        [],
      ) as HipaaRecommendation['resources'],
      relatedChecks: parseJsonSafely(record.related_checks as string, []) as string[],
    }));
  }

  /**
   * Gets scan history for a user
   */
  static async getScanHistory(
    userId: string,
    limit = 50,
    offset = 0,
  ): Promise<Array<{ id: string; targetUrl: string; overallScore: number; createdAt: string }>> {
    const records = await knex('hipaa_scans')
      .join('scans', 'hipaa_scans.scan_id', 'scans.id')
      .where('scans.user_id', userId)
      .select(
        'hipaa_scans.id',
        'hipaa_scans.target_url as targetUrl',
        'hipaa_scans.overall_score as overallScore',
        'hipaa_scans.created_at as createdAt',
      )
      .orderBy('hipaa_scans.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    return records.map((record: DatabaseRecord) => ({
      id: record.id as string,
      targetUrl: record.targetUrl as string,
      overallScore: record.overallScore as number,
      createdAt: (record.createdAt as Date).toISOString(),
    }));
  }

  /**
   * Deletes old scan results (for cleanup)
   */
  static async deleteOldScans(daysOld = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const deletedCount = await knex('hipaa_scans').where('created_at', '<', cutoffDate).del();

    return deletedCount;
  }
}
