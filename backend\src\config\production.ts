// backend/src/config/production.ts

/**
 * Production deployment configuration
 * Optimized for VPS deployment with comprehensive monitoring
 */

import { getPerformanceConfig } from './performance';
import { getSecurityConfig } from './security';

// Health check result interface
interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'error';
  duration?: string;
  timestamp: string;
  error?: string;
}

export interface ProductionConfig {
  // Server configuration
  port: number;
  host: string;
  nodeEnv: string;

  // Database configuration
  database: {
    url: string;
    poolMin: number;
    poolMax: number;
    acquireTimeoutMs: number;
    idleTimeoutMs: number;
    ssl: boolean;
  };

  // Redis configuration (for caching)
  redis: {
    url: string;
    maxRetries: number;
    retryDelayMs: number;
  };

  // Logging configuration
  logging: {
    level: string;
    enableConsole: boolean;
    enableFile: boolean;
    logDirectory: string;
    maxFileSize: string;
    maxFiles: number;
  };

  // Monitoring configuration
  monitoring: {
    enableHealthCheck: boolean;
    enableMetrics: boolean;
    metricsPort: number;
    healthCheckInterval: number;
  };

  // External services
  services: {
    keycloak: {
      url: string;
      realm: string;
      clientId: string;
      clientSecret: string;
    };
  };

  // Feature flags
  features: {
    enableHipaaAnalysis: boolean;
    enableGdprAnalysis: boolean;
    enableWcagAnalysis: boolean;
    enableAdaAnalysis: boolean;
    enableAdvancedAI: boolean;
    enableResultCaching: boolean;
  };
}

/**
 * Load production configuration from environment variables
 */
export function getProductionConfig(): ProductionConfig {
  return {
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || '0.0.0.0',
    nodeEnv: process.env.NODE_ENV || 'production',

    database: {
      url: process.env.DATABASE_URL || 'postgresql://user:pass@localhost:5432/complydb',
      poolMin: parseInt(process.env.DB_POOL_MIN || '2', 10),
      poolMax: parseInt(process.env.DB_POOL_MAX || '10', 10),
      acquireTimeoutMs: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000', 10),
      idleTimeoutMs: parseInt(process.env.DB_IDLE_TIMEOUT || '30000', 10),
      ssl: process.env.DB_SSL === 'true',
    },

    redis: {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
      retryDelayMs: parseInt(process.env.REDIS_RETRY_DELAY || '1000', 10),
    },

    logging: {
      level: process.env.LOG_LEVEL || 'info',
      enableConsole: process.env.LOG_CONSOLE !== 'false',
      enableFile: process.env.LOG_FILE === 'true',
      logDirectory: process.env.LOG_DIRECTORY || './logs',
      maxFileSize: process.env.LOG_MAX_SIZE || '10m',
      maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10),
    },

    monitoring: {
      enableHealthCheck: process.env.ENABLE_HEALTH_CHECK !== 'false',
      enableMetrics: process.env.ENABLE_METRICS !== 'false',
      metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
      healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
    },

    services: {
      keycloak: {
        url: process.env.KEYCLOAK_URL || 'http://localhost:8080',
        realm: process.env.KEYCLOAK_REALM || 'comply-checker',
        clientId: process.env.KEYCLOAK_CLIENT_ID || 'comply-checker-backend',
        clientSecret: process.env.KEYCLOAK_CLIENT_SECRET || '',
      },
    },

    features: {
      enableHipaaAnalysis: process.env.FEATURE_HIPAA !== 'false',
      enableGdprAnalysis: process.env.FEATURE_GDPR !== 'false',
      enableWcagAnalysis: process.env.FEATURE_WCAG !== 'false',
      enableAdaAnalysis: process.env.FEATURE_ADA !== 'false',
      enableAdvancedAI: process.env.FEATURE_ADVANCED_AI === 'true',
      enableResultCaching: process.env.FEATURE_CACHING !== 'false',
    },
  };
}

/**
 * Health check utilities
 */
export class HealthChecker {
  private static checks: Map<string, () => Promise<boolean>> = new Map();

  static registerCheck(name: string, checkFn: () => Promise<boolean>): void {
    this.checks.set(name, checkFn);
  }

  static async runAllChecks(): Promise<{
    status: string;
    checks: Record<string, HealthCheckResult>;
  }> {
    const results: Record<string, HealthCheckResult> = {};
    let overallStatus = 'healthy';

    for (const [name, checkFn] of this.checks) {
      try {
        const startTime = Date.now();
        const isHealthy = await checkFn();
        const duration = Date.now() - startTime;

        results[name] = {
          status: isHealthy ? 'healthy' : 'unhealthy',
          duration: `${duration}ms`,
          timestamp: new Date().toISOString(),
        };

        if (!isHealthy) {
          overallStatus = 'unhealthy';
        }
      } catch (error) {
        results[name] = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        };
        overallStatus = 'unhealthy';
      }
    }

    return { status: overallStatus, checks: results };
  }

  static async checkDatabase(): Promise<boolean> {
    try {
      const db = require('../lib/db').default;
      await db.raw('SELECT 1');
      return true;
    } catch {
      return false;
    }
  }

  static async checkRedis(): Promise<boolean> {
    try {
      // Redis check implementation would go here
      return true;
    } catch {
      return false;
    }
  }

  static async checkMemoryUsage(): Promise<boolean> {
    const usage = process.memoryUsage();
    const heapUsedMB = usage.heapUsed / 1024 / 1024;
    const performanceConfig = getPerformanceConfig();

    return heapUsedMB < performanceConfig.maxMemoryUsageMB * 0.9; // 90% threshold
  }

  static async checkDiskSpace(): Promise<boolean> {
    try {
      // const fs = require('fs');
      // const stats = fs.statSync('.');
      // Basic disk space check - in production, use more sophisticated monitoring
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Graceful shutdown handler
 */
export class GracefulShutdown {
  private static shutdownHandlers: Array<() => Promise<void>> = [];
  private static isShuttingDown = false;

  static addHandler(handler: () => Promise<void>): void {
    this.shutdownHandlers.push(handler);
  }

  static async shutdown(signal: string): Promise<void> {
    if (this.isShuttingDown) {
      console.log('Shutdown already in progress...');
      return;
    }

    this.isShuttingDown = true;
    console.log(`Received ${signal}. Starting graceful shutdown...`);

    const shutdownTimeout = setTimeout(() => {
      console.error('Graceful shutdown timeout. Forcing exit.');
      process.exit(1);
    }, 30000); // 30 second timeout

    try {
      // Run all shutdown handlers
      await Promise.all(
        this.shutdownHandlers.map(async (handler, index) => {
          try {
            await handler();
            console.log(`Shutdown handler ${index + 1} completed`);
          } catch (error) {
            console.error(`Shutdown handler ${index + 1} failed:`, error);
          }
        }),
      );

      console.log('Graceful shutdown completed');
      clearTimeout(shutdownTimeout);
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      clearTimeout(shutdownTimeout);
      process.exit(1);
    }
  }

  static setupSignalHandlers(): void {
    process.on('SIGTERM', () => this.shutdown('SIGTERM'));
    process.on('SIGINT', () => this.shutdown('SIGINT'));
    process.on('SIGUSR2', () => this.shutdown('SIGUSR2')); // Nodemon restart

    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.shutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.shutdown('UNHANDLED_REJECTION');
    });
  }
}

/**
 * Initialize production configuration
 */
export function initializeProduction(): void {
  const config = getProductionConfig();
  const performanceConfig = getPerformanceConfig();
  const securityConfig = getSecurityConfig();

  // Register health checks
  HealthChecker.registerCheck('database', HealthChecker.checkDatabase);
  HealthChecker.registerCheck('memory', HealthChecker.checkMemoryUsage);
  HealthChecker.registerCheck('disk', HealthChecker.checkDiskSpace);

  // Setup graceful shutdown
  GracefulShutdown.setupSignalHandlers();

  // Add database shutdown handler
  GracefulShutdown.addHandler(async () => {
    const db = require('../lib/db').default;
    await db.destroy();
  });

  console.log('Production configuration initialized:', {
    nodeEnv: config.nodeEnv,
    port: config.port,
    features: config.features,
    performance: {
      maxConcurrentAnalyses: performanceConfig.maxConcurrentAnalyses,
      maxMemoryUsageMB: performanceConfig.maxMemoryUsageMB,
    },
    security: {
      rateLimitMaxRequests: securityConfig.rateLimitMaxRequests,
      requireApiKey: securityConfig.requireApiKey,
    },
  });
}

export default {
  getProductionConfig,
  HealthChecker,
  GracefulShutdown,
  initializeProduction,
};
