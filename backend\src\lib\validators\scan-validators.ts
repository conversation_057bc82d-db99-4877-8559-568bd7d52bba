import { z } from 'zod';

// Schema for creating a new scan (POST /scans)
export const CreateScanSchema = z.object({
  url: z.string().url({ message: 'Scan URL must be a valid HTTP/HTTPS URL.' }),
  standards: z.array(z.string()).min(1, { message: 'At least one standard must be selected.' }),
});

/**
 * Represents the validated and typed input for creating a new scan.
 * Inferred from {@link CreateScanSchema}.
 */
export type CreateScanInput = z.infer<typeof CreateScanSchema>;

// Schema for getting a scan by ID (GET /scans/:scanId)
export const GetScanParamsSchema = z.object({
  scanId: z.string().uuid({ message: 'Scan ID must be a valid UUID.' }),
});

/**
 * Represents the validated and typed input for fetching a scan by its ID (route parameters).
 * Inferred from {@link GetScanParamsSchema}.
 */
export type GetScanParamsInput = z.infer<typeof GetScanParamsSchema>;

// You can add more schemas here as needed, for example, for query parameters in GET /scans
