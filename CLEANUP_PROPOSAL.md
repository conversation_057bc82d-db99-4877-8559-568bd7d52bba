# Project Cleanup Proposal

## Overview
This document outlines files and directories that can be safely removed from the Comply Checker project to reduce clutter and improve maintainability. All recommendations are based on analysis of current project structure and functionality.

## Files Recommended for Removal

### 1. Obsolete Test Files (Root Directory)

#### **High Priority - Safe to Remove**
- `test-nuclei-fix.js` - Temporary test file for Nuclei path resolution (issue resolved)
- `test-nuclei-path-resolution.js` - Duplicate Nuclei testing functionality
- `test-nuclei-path.js` - Another Nuclei path test file
- `test-nuclei-simple.js` - Simple Nuclei test (functionality covered elsewhere)
- `test-nuclei.js` - Basic Nuclei test file
- `test-security-improvements.js` - Temporary test for security improvements
- `final-test-security-scan.js` - Final test file (testing complete)
- `verify-fixes.js` - Verification script for completed fixes

**Justification**: These are temporary test files created during development and debugging of the Nuclei security scanner integration. The issues have been resolved and proper tests exist in the test directories.

#### **Backend Test Files**
- `backend/test-nuclei-backend.js` - Backend-specific Nuclei test
- `backend/test-nuclei-fix.js` - Duplicate of root test file
- `backend/test-nuclei-simple.js` - Simple backend Nuclei test
- `backend/test-security-fixes.js` - Security fixes test
- `backend/test-security-scan.js` - Security scan test
- `backend/test-security-without-nuclei.js` - Test without Nuclei
- `backend/test-new-scoring.js` - New scoring system test
- `backend/debug-nuclei.js` - Debug script for Nuclei
- `backend/verify-nuclei-path.js` - Path verification script

**Justification**: These are temporary debugging and testing files. Proper unit tests exist in the Jest test framework.

### 2. Obsolete Documentation Files

#### **Medium Priority - Review Before Removal**
- `NUCLEI_FIXED_SUMMARY.md` - Summary of completed Nuclei fixes
- `NUCLEI_SETUP_SOLUTION.md` - Setup solution (now documented elsewhere)
- `SECURITY_SCAN_FIXES.md` - Security scan fixes documentation
- `SECURITY_SCAN_FIXES_SUMMARY.md` - Summary of security fixes
- `WSL2_NETWORK_FIX_GUIDE.md` - WSL2 network fix guide
- `DOCKER_BUILD_ERROR_FIX.md` - Docker build error fixes
- `DOCKER_DESKTOP_STARTUP_GUIDE.md` - Docker startup guide
- `DOCKER_SETUP_GUIDE.md` - Docker setup guide
- `KeyCloak_Logs.md` - Keycloak logs (temporary)
- `backend_logs.md` - Backend logs (temporary)

**Justification**: These are issue-specific documentation files that were created to track specific problems. The issues have been resolved and the information should be consolidated into proper documentation.

### 3. Temporary Configuration Files

#### **Low Priority - Keep for Now**
- `security-scan-test-results.json` - Test results file (may contain useful data)
- `babel.config.js` - May be needed for Jest configuration

### 4. Obsolete Scripts

#### **High Priority - Safe to Remove**
- `FIX_DOCKER_PERMANENTLY.bat` - Temporary Docker fix script
- Various PowerShell scripts in `/scripts/` that are no longer needed:
  - `fix-docker-desktop-error.bat`
  - `fix-docker-desktop-error.ps1`
  - `fix-docker-setup.bat`
  - `fix-docker-setup.ps1`
  - `fix-wsl2-network-issue.bat`
  - `fix-wsl2-network-issue.ps1`
  - `permanent-wsl2-fix.bat`
  - `setup-auto-prevention.bat`
  - `start-docker-desktop.bat`
  - `start-docker-desktop.ps1`
  - `verify-docker-and-start-services.bat`
  - `verify-permanent-fix.bat`

**Justification**: These are temporary fix scripts for specific Docker/WSL2 issues that have been resolved.

## Files to Keep

### Important Documentation
- `BUGS.md` - Active bug tracking
- `Doc/` directory - Contains important project documentation
- `.windsurfrules` - Project configuration rules
- `HIPAA_PRIVACY_RESTRUCTURING_PLAN.md` - Active planning document
- `LegalBERT_Upgrade_Plan.md` - Future upgrade planning
- `PRODUCTION_DEPLOYMENT_GUIDE.md` - Deployment documentation
- `PROJECT_COMPLETION_SUMMARY.md` - Project status

### Configuration Files
- `.eslintrc.js` - ESLint configuration
- `.husky/` - Git hooks
- `package.json` - Root package configuration
- `tsconfig.base.json` - TypeScript base configuration

### Active Scripts
- `scripts/safe-commit.sh` - Active commit script
- `scripts/deploy-production.sh` - Deployment script
- Scripts that are actively used for development

## Recommended Actions

1. **Phase 1**: Remove all temporary test files (safe removal)
2. **Phase 2**: Archive obsolete documentation to a `docs/archive/` directory
3. **Phase 3**: Remove obsolete scripts after confirming they're no longer needed
4. **Phase 4**: Consolidate remaining documentation into comprehensive guides

## Risk Assessment

- **Low Risk**: Temporary test files and debug scripts
- **Medium Risk**: Documentation files (should be archived first)
- **High Risk**: Configuration files and active scripts (keep these)

## Next Steps

1. Get approval for Phase 1 removals
2. Create archive directory for documentation
3. Execute cleanup in phases
4. Update .gitignore if needed
5. Create comprehensive documentation to replace removed files
