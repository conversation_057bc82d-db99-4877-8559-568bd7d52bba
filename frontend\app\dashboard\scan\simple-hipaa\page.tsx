'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Loader2, ArrowLeft, TestTube } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Badge } from '@/components/ui/Badge';
import { useAuth } from '@/context/AuthContext';
import { submitScan, getScanDetails } from '@/lib/api';
import EnhancedHipaaResultsComponent from '@/components/compliance/EnhancedHipaaResults';
import { EnhancedHipaaResults } from '@/types/hipaa';

/**
 * Simple HIPAA Test Page
 * Uses the actual backend data structure without complex transformations
 */

const SAMPLE_URLS = [
  {
    name: 'Healthcare Provider',
    url: 'https://www.kp.org/',
    description: 'Kaiser Permanente - Large healthcare organization',
  },
  {
    name: 'Telehealth Platform',
    url: 'https://vsee.com/',
    description: 'VSee - Telehealth and telemedicine platform',
  },
  {
    name: 'Hospital System',
    url: 'https://www.nychealthandhospitals.org/',
    description: 'NYC Health + Hospitals - Public hospital system',
  },
];

/**
 * Simple HIPAA Analysis Page
 * Displays HIPAA compliance results in a simplified format
 * @returns {JSX.Element} The simple HIPAA page component
 */
export default function SimpleHipaaPage() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanResult, setScanResult] = useState<EnhancedHipaaResults | null>(null);
  const [scanId, setScanId] = useState<string | null>(null);
  const { authenticated, login } = useAuth();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setScanResult(null);
    setScanId(null);

    if (!authenticated) {
      setError(
        'Please log in to access HIPAA Privacy Policy Analysis. This feature requires authentication to protect your scan data.',
      );
      return;
    }

    if (!url.trim()) {
      setError('Please enter a valid URL');
      return;
    }

    try {
      new URL(url);
    } catch (_) {
      setError('Please enter a valid URL format (e.g., https://example.com)');
      return;
    }

    setIsLoading(true);
    try {
      // Submit scan with enhanced HIPAA analysis
      const response = await submitScan({
        url,
        standards: ['hipaa'],
        options: {
          enableEnhancedAnalysis: true,
          analysisDepth: 'comprehensive',
          includePerformanceMetrics: true,
        },
      });

      setScanId(response.id);

      // Poll for results
      const pollForResults = async (scanId: string, attempts = 0) => {
        if (attempts > 60) {
          setError('Analysis timed out after 60 seconds. Please try again.');
          setIsLoading(false);
          return;
        }

        try {
          const scanData = await getScanDetails(scanId);

          if (scanData.status === 'completed') {
            if (scanData.enhancedHipaaResults) {
              // Use type assertion to handle backend/frontend type differences
              setScanResult(scanData.enhancedHipaaResults as EnhancedHipaaResults);
              setIsLoading(false);
            } else {
              setTimeout(() => pollForResults(scanId, attempts + 1), 2000);
            }
          } else if (scanData.status === 'failed') {
            setError(`Analysis failed: ${scanData.error || 'Unknown error'}`);
            setIsLoading(false);
          } else {
            // Still processing

            setTimeout(() => pollForResults(scanId, attempts + 1), 1000);
          }
        } catch (error) {
          setError(
            `Failed to get analysis results: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          setIsLoading(false);
        }
      };

      // Start polling
      pollForResults(response.id);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Failed to submit scan');
      } else {
        setError('Failed to submit scan');
      }
      setIsLoading(false);
    }
  };

  const handleSampleUrl = (sampleUrl: string) => {
    setUrl(sampleUrl);
    setError(null);
    setScanResult(null);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/scan/new">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Scan
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            Simple HIPAA Privacy Policy Analysis
          </h1>
          <p className="text-muted-foreground">
            3-Level HIPAA privacy policy analysis with pattern matching, NLP, and AI-powered
            recommendations
          </p>
        </div>
      </div>

      {/* URL Input Form */}
      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Enter URL to Analyze</CardTitle>
            <CardDescription>
              Test the 3-level HIPAA privacy policy analysis system with pattern matching, NLP, and
              AI analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="url">Website URL</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>

            {/* Sample URLs */}
            <div className="space-y-2">
              <Label>Quick Test URLs</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                {SAMPLE_URLS.map((sample, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    type="button"
                    onClick={() => handleSampleUrl(sample.url)}
                    disabled={isLoading}
                    className="justify-start h-auto p-3"
                  >
                    <div className="text-left">
                      <div className="font-medium">{sample.name}</div>
                      <div className="text-xs text-muted-foreground">{sample.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>
                  {error}
                  {!authenticated && (
                    <div className="mt-2">
                      <Button onClick={login} size="sm" variant="outline">
                        Log In to Continue
                      </Button>
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {!authenticated && (
              <Alert>
                <AlertDescription>
                  <div className="flex items-center justify-between">
                    <span>
                      Please log in to access HIPAA Privacy Policy Analysis (3-Level System)
                    </span>
                    <Button onClick={login} size="sm">
                      Log In
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {scanId && !scanResult && (
              <Alert>
                <AlertDescription>
                  Scan submitted successfully! Scan ID: {scanId}
                  <br />
                  Running enhanced 3-level analysis...
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing with 3-Level System...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  Run HIPAA Privacy Policy Analysis
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {/* Results Display */}
      {scanResult && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Analysis Complete</Badge>
            <span className="text-sm text-muted-foreground">
              Processed in {scanResult.metadata?.processingTime}ms
            </span>
          </div>
          <EnhancedHipaaResultsComponent enhancedHipaaResults={scanResult} />
        </div>
      )}
    </div>
  );
}
