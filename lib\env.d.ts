declare const finalEnv: {
    DATABASE_URL: string;
    NODE_ENV: "test" | "development" | "production";
    KEYCLOAK_ADMIN_USER: string;
    KEYCLOAK_ADMIN_PASSWORD: string;
    KEYCLOAK_PORT: number;
    KE<PERSON><PERSON>OAK_REALM: string;
    K<PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID_FRONTEND: string;
    K<PERSON><PERSON><PERSON>OAK_CLIENT_ID_BACKEND: string;
    KEYCLOAK_CLIENT_SECRET_BACKEND: string;
    KEYCLOAK_URL: string;
    MAILHOG_SMTP_PORT: number;
    MAILHOG_HTTP_PORT: number;
    SMTP_HOST: string;
    SMTP_PORT: number;
    NEXT_PUBLIC_KEYCLOAK_URL: string | undefined;
    NEXT_PUBLIC_KEYCLOAK_REALM: string | undefined;
    NEXT_PUBLIC_KEYCLOAK_CLIENT_ID: string | undefined;
    NEXT_PUBLIC_BACKEND_API_URL: string | undefined;
    POSTGRES_USER: string;
    POSTGRES_PASSWORD: string;
    POSTGRES_DB: string;
    POSTGRES_HOST: string | undefined;
    POSTGRES_PORT: number;
    BACKEND_PORT: number;
    FRONTEND_URL: string;
    SESSION_SECRET: string;
} | {
    DATABASE_URL: string;
    NODE_ENV: "test" | "development" | "production";
    KEYCLOAK_ADMIN_USER: string;
    KEYCLOAK_ADMIN_PASSWORD: string;
    KEYCLOAK_PORT: number;
    KEYCLOAK_REALM: string;
    KEYCLOAK_CLIENT_ID_FRONTEND: string;
    KEYCLOAK_CLIENT_ID_BACKEND: string;
    KEYCLOAK_CLIENT_SECRET_BACKEND: string;
    KEYCLOAK_URL: string;
    MAILHOG_SMTP_PORT: number;
    MAILHOG_HTTP_PORT: number;
    SMTP_HOST: string;
    SMTP_PORT: number;
    NEXT_PUBLIC_KEYCLOAK_URL: string | undefined;
    NEXT_PUBLIC_KEYCLOAK_REALM: string | undefined;
    NEXT_PUBLIC_KEYCLOAK_CLIENT_ID: string | undefined;
    NEXT_PUBLIC_BACKEND_API_URL: string | undefined;
    POSTGRES_USER: string;
    POSTGRES_PASSWORD: string;
    POSTGRES_DB: string;
    POSTGRES_HOST: string | undefined;
    POSTGRES_PORT: number;
    BACKEND_PORT: number;
    FRONTEND_URL: string;
    SESSION_SECRET: string;
};
export { finalEnv as env };
//# sourceMappingURL=env.d.ts.map