import { NucleiClient } from '../services/nuclei-client';
import { ContentAnalyzer } from '../services/content-analyzer';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export class AuthenticationTest {
  private nucleiClient: NucleiClient;
  private contentAnalyzer: ContentAnalyzer;

  constructor(nucleiClient: NucleiClient, contentAnalyzer: ContentAnalyzer) {
    this.nucleiClient = nucleiClient;
    this.contentAnalyzer = contentAnalyzer;
  }

  async runAuthenticationTests(targetUrl: string): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    // Test 1: Authentication Form Detection
    results.push(await this.testAuthenticationForms(targetUrl));

    // Test 2: Multi-Factor Authentication Indicators
    results.push(await this.testMFAIndicators(targetUrl));

    // Test 3: Password Complexity Requirements
    results.push(await this.testPasswordComplexity(targetUrl));

    return results;
  }

  private async testAuthenticationForms(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'authentication-forms';
    const testName = 'Authentication Form Security';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUTHENTICATION;

    try {
      const authPages = ['/login', '/register', '/reset-password'];
      const formIssues: FailureEvidence[] = [];
      const secureFormsFound: string[] = [];

      for (const page of authPages) {
        try {
          const fullUrl = `${targetUrl}${page}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          if (response.statusCode === 200) {
            const contentAnalysis = this.contentAnalyzer.analyzeContent(
              response.body,
              fullUrl,
              response.responseHeaders,
            );

            // Check each form for security issues
            contentAnalysis.formAnalysis.forEach((form) => {
              if (form.securityIssues.length > 0) {
                formIssues.push({
                  location: fullUrl,
                  elementType: 'form',
                  actualCode: `Form: ${form.formId}, Action: ${form.action}, Method: ${form.method}`,
                  expectedBehavior: 'Forms should have CSRF protection and use HTTPS',
                  context: `Security issues: ${form.securityIssues.join(', ')}`,
                });
              } else {
                secureFormsFound.push(`${page}:${form.formId}`);
              }
            });
          }
        } catch (error) {
          // Page might not exist, which is acceptable
          continue;
        }
      }

      if (formIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify authentication forms implement proper security measures',
          category: 'technical',
          passed: false,
          failureReason: `${formIssues.length} authentication form security issues found`,
          riskLevel: 'high',
          failureEvidence: formIssues,
          recommendedAction:
            'Implement CSRF protection and ensure HTTPS for all authentication forms',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify authentication forms implement proper security measures',
        category: 'technical',
        passed: true,
        evidence: `${secureFormsFound.length} secure authentication forms found`,
        pagesTested: authPages.filter((page) =>
          secureFormsFound.some((form) => form.startsWith(page)),
        ),
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify authentication forms implement proper security measures',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testMFAIndicators(targetUrl: string): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'authentication-mfa';
    const testName = 'Multi-Factor Authentication Indicators';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUTHENTICATION;

    try {
      const loginUrl = `${targetUrl}/login`;
      const response = await this.nucleiClient.fetchUrlContent(loginUrl);

      if (response.statusCode !== 200) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Check for multi-factor authentication implementation indicators',
          category: 'technical',
          passed: false,
          failureReason: 'Login page not accessible for MFA analysis',
          riskLevel: 'medium',
          failureEvidence: [
            {
              location: loginUrl,
              elementType: 'response',
              actualCode: `HTTP ${response.statusCode}`,
              expectedBehavior: 'Login page should be accessible',
              context: 'Cannot analyze MFA indicators without accessible login page',
            },
          ],
          recommendedAction: 'Ensure login page is accessible for security analysis',
          remediationPriority: 3,
          timestamp: new Date(),
        };
      }

      // Look for MFA indicators
      const mfaPatterns = [
        /two.factor|2fa|mfa/i,
        /authenticator|totp/i,
        /sms.code|text.code/i,
        /verification.code/i,
        /security.key|yubikey/i,
      ];

      const mfaIndicators = mfaPatterns.filter((pattern) => pattern.test(response.body));

      if (mfaIndicators.length === 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Check for multi-factor authentication implementation indicators',
          category: 'technical',
          passed: false,
          failureReason: 'No multi-factor authentication indicators found',
          riskLevel: 'high',
          failureEvidence: [
            {
              location: loginUrl,
              elementType: 'html',
              actualCode: response.body.substring(0, 500),
              expectedBehavior:
                'Should include MFA indicators (2FA, authenticator, verification code, etc.)',
              context: 'No MFA implementation indicators detected in login page',
            },
          ],
          recommendedAction: 'Implement multi-factor authentication for enhanced security',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for multi-factor authentication implementation indicators',
        category: 'technical',
        passed: true,
        evidence: `MFA indicators found: ${mfaIndicators.length} patterns detected`,
        pagesTested: ['/login'],
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for multi-factor authentication implementation indicators',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testPasswordComplexity(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'authentication-password-complexity';
    const testName = 'Password Complexity Requirements';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUTHENTICATION;

    try {
      const passwordPages = ['/register', '/reset-password', '/change-password'];
      const complexityIndicators: string[] = [];
      const missingComplexity: FailureEvidence[] = [];

      for (const page of passwordPages) {
        try {
          const fullUrl = `${targetUrl}${page}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          if (response.statusCode === 200) {
            // Look for password complexity indicators
            const complexityPatterns = [
              /password.{0,50}(requirement|rule|policy)/i,
              /minimum.{0,20}(character|length)/i,
              /uppercase.{0,20}lowercase/i,
              /special.{0,20}character/i,
              /\d+.{0,20}character/i,
            ];

            const hasComplexityIndicators = complexityPatterns.some((pattern) =>
              pattern.test(response.body),
            );

            if (hasComplexityIndicators) {
              complexityIndicators.push(page);
            } else {
              missingComplexity.push({
                location: fullUrl,
                elementType: 'html',
                actualCode: response.body.substring(0, 300),
                expectedBehavior: 'Should display password complexity requirements',
                context: 'No password complexity requirements displayed',
              });
            }
          }
        } catch (error) {
          // Page might not exist, continue
          continue;
        }
      }

      if (complexityIndicators.length === 0 && passwordPages.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify password complexity requirements are displayed',
          category: 'technical',
          passed: false,
          failureReason: 'No password complexity requirements found',
          riskLevel: 'medium',
          failureEvidence: missingComplexity,
          recommendedAction: 'Display clear password complexity requirements to users',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify password complexity requirements are displayed',
        category: 'technical',
        passed: true,
        evidence: `Password complexity requirements found on ${complexityIndicators.length} pages`,
        pagesTested: complexityIndicators,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify password complexity requirements are displayed',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }
}
