/**
 * Accessibility Utilities for WCAG AA Compliance
 * Provides helper functions and hooks for accessibility features
 */

import { useEffect, useRef } from 'react';

// ===== ARIA UTILITIES =====

/**
 * Generate unique IDs for ARIA attributes
 */
export function generateAriaId(prefix: string = 'aria'): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Create ARIA label for compliance scores
 */
export function createScoreAriaLabel(score: number, context: string = 'compliance'): string {
  const level = score >= 90 ? 'excellent' : score >= 75 ? 'good' : score >= 60 ? 'fair' : 'poor';
  return `${context} score: ${score} percent, ${level} level`;
}

/**
 * Create ARIA label for risk levels
 */
export function createRiskAriaLabel(riskLevel: string, score?: number): string {
  const scoreText = score ? ` with ${score} percent compliance` : '';
  return `Risk level: ${riskLevel}${scoreText}`;
}

/**
 * Create ARIA label for scan status
 */
export function createScanStatusAriaLabel(status: string, url?: string): string {
  const urlText = url ? ` for ${url}` : '';
  return `Scan status: ${status}${urlText}`;
}

// ===== KEYBOARD NAVIGATION =====

/**
 * Hook for managing focus trap in modals and dialogs
 */
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    function handleTabKey(e: KeyboardEvent) {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    }

    function handleEscapeKey(e: KeyboardEvent) {
      if (e.key === 'Escape') {
        // Let parent component handle escape
        container.dispatchEvent(new CustomEvent('escape-pressed'));
      }
    }

    container.addEventListener('keydown', handleTabKey);
    container.addEventListener('keydown', handleEscapeKey);

    // Focus first element when trap becomes active
    if (firstElement) {
      firstElement.focus();
    }

    return () => {
      container.removeEventListener('keydown', handleTabKey);
      container.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isActive]);

  return containerRef;
}

/**
 * Hook for managing roving tabindex in lists and grids
 */
export function useRovingTabIndex(items: HTMLElement[], activeIndex: number) {
  useEffect(() => {
    items.forEach((item, index) => {
      if (item) {
        item.tabIndex = index === activeIndex ? 0 : -1;
      }
    });
  }, [items, activeIndex]);
}

// ===== SCREEN READER UTILITIES =====

/**
 * Announce message to screen readers
 */
export function announceToScreenReader(
  message: string,
  priority: 'polite' | 'assertive' = 'polite',
) {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Create live region for dynamic content updates
 */
export function createLiveRegion(
  id: string,
  priority: 'polite' | 'assertive' = 'polite',
): HTMLElement {
  let liveRegion = document.getElementById(id);

  if (!liveRegion) {
    liveRegion = document.createElement('div');
    liveRegion.id = id;
    liveRegion.setAttribute('aria-live', priority);
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    document.body.appendChild(liveRegion);
  }

  return liveRegion;
}

/**
 * Update live region content
 */
export function updateLiveRegion(id: string, message: string) {
  const liveRegion = document.getElementById(id);
  if (liveRegion) {
    liveRegion.textContent = message;
  }
}

// ===== COLOR CONTRAST UTILITIES =====

/**
 * Calculate relative luminance for color contrast
 */
function getRelativeLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map((c) => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Calculate contrast ratio between two colors
 */
export function getContrastRatio(color1: string, color2: string): number {
  // Simple implementation for hex colors
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');

  const r1 = parseInt(hex1.substring(0, 2), 16);
  const g1 = parseInt(hex1.substring(2, 4), 16);
  const b1 = parseInt(hex1.substring(4, 6), 16);

  const r2 = parseInt(hex2.substring(0, 2), 16);
  const g2 = parseInt(hex2.substring(2, 4), 16);
  const b2 = parseInt(hex2.substring(4, 6), 16);

  const lum1 = getRelativeLuminance(r1, g1, b1);
  const lum2 = getRelativeLuminance(r2, g2, b2);

  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if color combination meets WCAG AA standards
 */
export function meetsWCAGAA(
  foreground: string,
  background: string,
  isLargeText: boolean = false,
): boolean {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
}

// ===== MOTION UTILITIES =====

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Hook for respecting motion preferences
 */
export function useRespectMotionPreference() {
  const prefersReduced = prefersReducedMotion();

  return {
    shouldAnimate: !prefersReduced,
    duration: prefersReduced ? 0 : undefined,
    transition: prefersReduced ? 'none' : undefined,
  };
}

// ===== FORM ACCESSIBILITY =====

/**
 * Create accessible form field props
 */
export function createFormFieldProps(
  id: string,
  label: string,
  error?: string,
  description?: string,
  required: boolean = false,
) {
  const describedBy = [];

  if (description) {
    describedBy.push(`${id}-description`);
  }

  if (error) {
    describedBy.push(`${id}-error`);
  }

  return {
    id,
    'aria-label': label,
    'aria-required': required,
    'aria-invalid': !!error,
    'aria-describedby': describedBy.length > 0 ? describedBy.join(' ') : undefined,
  };
}

/**
 * Create accessible error message props
 */
export function createErrorMessageProps(fieldId: string) {
  return {
    id: `${fieldId}-error`,
    role: 'alert',
    'aria-live': 'polite' as const,
  };
}

/**
 * Create accessible description props
 */
export function createDescriptionProps(fieldId: string) {
  return {
    id: `${fieldId}-description`,
  };
}

// ===== TABLE ACCESSIBILITY =====

/**
 * Create accessible table props
 */
export function createTableProps(caption: string, summary?: string) {
  return {
    role: 'table',
    'aria-label': caption,
    'aria-describedby': summary ? 'table-summary' : undefined,
  };
}

/**
 * Create accessible table header props
 */
export function createTableHeaderProps(scope: 'col' | 'row' = 'col') {
  return {
    role: 'columnheader',
    scope,
  };
}

/**
 * Create accessible table cell props
 */
export function createTableCellProps(headers?: string[]) {
  return {
    role: 'cell',
    headers: headers?.join(' '),
  };
}

// ===== LANDMARK UTILITIES =====

/**
 * Create accessible landmark props
 */
export function createLandmarkProps(
  role: 'main' | 'navigation' | 'banner' | 'contentinfo' | 'complementary' | 'region',
  label?: string,
) {
  return {
    role,
    'aria-label': label,
  };
}

/**
 * Create accessible heading props with proper level
 */
export function createHeadingProps(level: 1 | 2 | 3 | 4 | 5 | 6, id?: string) {
  return {
    role: 'heading',
    'aria-level': level,
    id,
  };
}

// ===== EXPORT ALL UTILITIES =====
export default {
  generateAriaId,
  createScoreAriaLabel,
  createRiskAriaLabel,
  createScanStatusAriaLabel,
  useFocusTrap,
  useRovingTabIndex,
  announceToScreenReader,
  createLiveRegion,
  updateLiveRegion,
  getContrastRatio,
  meetsWCAGAA,
  prefersReducedMotion,
  useRespectMotionPreference,
  createFormFieldProps,
  createErrorMessageProps,
  createDescriptionProps,
  createTableProps,
  createTableHeaderProps,
  createTableCellProps,
  createLandmarkProps,
  createHeadingProps,
};
