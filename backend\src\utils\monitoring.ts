// backend/src/utils/monitoring.ts

/**
 * Comprehensive monitoring and observability system
 * Provides metrics collection, performance tracking, and alerting
 */

import { PerformanceMetrics, MemoryMonitor } from '../config/performance';
import { Request, Response, NextFunction } from 'express';

// Type aliases for Express
type ExpressRequest = Request;
type ExpressResponse = Response;
type ExpressNext = NextFunction;

export interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  unit?: string;
}

export interface AlertRule {
  name: string;
  metric: string;
  threshold: number;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  duration: number; // milliseconds
  enabled: boolean;
}

/**
 * Metrics collector for application monitoring
 */
export class MetricsCollector {
  private static metrics: MetricData[] = [];
  private static alerts: AlertRule[] = [];
  private static alertStates: Map<string, { triggered: boolean; since: number }> = new Map();

  /**
   * Record a metric value
   */
  static recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>,
    unit?: string,
  ): void {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      unit,
    };

    this.metrics.push(metric);

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Check alerts
    this.checkAlerts(metric);

    // Also record in performance metrics for compatibility
    PerformanceMetrics.recordMetric(name, value);
  }

  /**
   * Get metrics by name and time range
   */
  static getMetrics(name?: string, since?: number): MetricData[] {
    let filtered = this.metrics;

    if (name) {
      filtered = filtered.filter((m) => m.name === name);
    }

    if (since) {
      filtered = filtered.filter((m) => m.timestamp >= since);
    }

    return filtered;
  }

  /**
   * Get aggregated metrics
   */
  static getAggregatedMetrics(
    name: string,
    since?: number,
  ): {
    avg: number;
    min: number;
    max: number;
    count: number;
    sum: number;
  } {
    const metrics = this.getMetrics(name, since);

    if (metrics.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0, sum: 0 };
    }

    const values = metrics.map((m) => m.value);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      avg: sum / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length,
      sum,
    };
  }

  /**
   * Add alert rule
   */
  static addAlert(rule: AlertRule): void {
    this.alerts.push(rule);
    this.alertStates.set(rule.name, { triggered: false, since: 0 });
  }

  /**
   * Check alerts for a metric
   */
  private static checkAlerts(metric: MetricData): void {
    for (const rule of this.alerts) {
      if (!rule.enabled || rule.metric !== metric.name) continue;

      const state = this.alertStates.get(rule.name)!;
      const shouldTrigger = this.evaluateAlertCondition(metric.value, rule);

      if (shouldTrigger && !state.triggered) {
        state.triggered = true;
        state.since = metric.timestamp;
        this.triggerAlert(rule, metric);
      } else if (!shouldTrigger && state.triggered) {
        state.triggered = false;
        this.resolveAlert(rule, metric);
      }
    }
  }

  /**
   * Evaluate alert condition
   */
  private static evaluateAlertCondition(value: number, rule: AlertRule): boolean {
    switch (rule.operator) {
      case 'gt':
        return value > rule.threshold;
      case 'gte':
        return value >= rule.threshold;
      case 'lt':
        return value < rule.threshold;
      case 'lte':
        return value <= rule.threshold;
      case 'eq':
        return value === rule.threshold;
      default:
        return false;
    }
  }

  /**
   * Trigger alert
   */
  private static triggerAlert(rule: AlertRule, metric: MetricData): void {
    console.warn(
      `[ALERT TRIGGERED] ${rule.name}: ${metric.name} = ${metric.value} (threshold: ${rule.threshold})`,
    );

    // In production, this would send notifications (email, Slack, etc.)
    this.recordMetric('alert.triggered', 1, {
      alertName: rule.name,
      metric: rule.metric,
    });
  }

  /**
   * Resolve alert
   */
  private static resolveAlert(rule: AlertRule, metric: MetricData): void {
    console.info(`[ALERT RESOLVED] ${rule.name}: ${metric.name} = ${metric.value}`);

    this.recordMetric('alert.resolved', 1, {
      alertName: rule.name,
      metric: rule.metric,
    });
  }

  /**
   * Get current alert status
   */
  static getAlertStatus(): Array<{ rule: AlertRule; triggered: boolean; since: number }> {
    return this.alerts.map((rule) => ({
      rule,
      triggered: this.alertStates.get(rule.name)?.triggered || false,
      since: this.alertStates.get(rule.name)?.since || 0,
    }));
  }
}

/**
 * Application performance monitor
 */
export class ApplicationMonitor {
  private static startTime = Date.now();
  private static requestCount = 0;
  private static errorCount = 0;

  /**
   * Initialize monitoring with default alerts
   */
  static initialize(): void {
    // Memory usage alerts
    MetricsCollector.addAlert({
      name: 'high_memory_usage',
      metric: 'memory.heap_used_mb',
      threshold: 4000, // 4GB
      operator: 'gt',
      duration: 60000, // 1 minute
      enabled: true,
    });

    // Response time alerts
    MetricsCollector.addAlert({
      name: 'slow_response_time',
      metric: 'http.response_time_ms',
      threshold: 5000, // 5 seconds
      operator: 'gt',
      duration: 30000, // 30 seconds
      enabled: true,
    });

    // Error rate alerts
    MetricsCollector.addAlert({
      name: 'high_error_rate',
      metric: 'http.error_rate',
      threshold: 0.1, // 10%
      operator: 'gt',
      duration: 300000, // 5 minutes
      enabled: true,
    });

    // Start periodic monitoring
    this.startPeriodicMonitoring();

    console.log('Application monitoring initialized');
  }

  /**
   * Start periodic monitoring tasks
   */
  private static startPeriodicMonitoring(): void {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Collect application metrics every minute
    setInterval(() => {
      this.collectApplicationMetrics();
    }, 60000);
  }

  /**
   * Collect system-level metrics
   */
  private static collectSystemMetrics(): void {
    // Memory metrics
    const memUsage = MemoryMonitor.getCurrentMemoryUsage();
    MetricsCollector.recordMetric(
      'memory.heap_used_mb',
      Math.round(memUsage.heapUsed / 1024 / 1024),
      {},
      'MB',
    );
    MetricsCollector.recordMetric(
      'memory.heap_total_mb',
      Math.round(memUsage.heapTotal / 1024 / 1024),
      {},
      'MB',
    );
    MetricsCollector.recordMetric(
      'memory.external_mb',
      Math.round(memUsage.external / 1024 / 1024),
      {},
      'MB',
    );
    MetricsCollector.recordMetric(
      'memory.rss_mb',
      Math.round(memUsage.rss / 1024 / 1024),
      {},
      'MB',
    );

    // CPU metrics (basic)
    const cpuUsage = process.cpuUsage();
    MetricsCollector.recordMetric('cpu.user_time_ms', cpuUsage.user / 1000, {}, 'ms');
    MetricsCollector.recordMetric('cpu.system_time_ms', cpuUsage.system / 1000, {}, 'ms');

    // Process metrics
    MetricsCollector.recordMetric('process.uptime_seconds', process.uptime(), {}, 'seconds');
  }

  /**
   * Collect application-level metrics
   */
  private static collectApplicationMetrics(): void {
    const uptime = Date.now() - this.startTime;
    const errorRate = this.requestCount > 0 ? this.errorCount / this.requestCount : 0;

    MetricsCollector.recordMetric('app.uptime_ms', uptime, {}, 'ms');
    MetricsCollector.recordMetric('app.request_count', this.requestCount);
    MetricsCollector.recordMetric('app.error_count', this.errorCount);
    MetricsCollector.recordMetric('app.error_rate', errorRate);

    // Reset counters for next period
    this.requestCount = 0;
    this.errorCount = 0;
  }

  /**
   * Record HTTP request
   */
  static recordRequest(
    method: string,
    path: string,
    statusCode: number,
    responseTime: number,
  ): void {
    this.requestCount++;

    if (statusCode >= 400) {
      this.errorCount++;
    }

    MetricsCollector.recordMetric(
      'http.response_time_ms',
      responseTime,
      {
        method,
        path: this.sanitizePath(path),
        status: statusCode.toString(),
      },
      'ms',
    );

    MetricsCollector.recordMetric('http.request_count', 1, {
      method,
      path: this.sanitizePath(path),
      status: statusCode.toString(),
    });
  }

  /**
   * Record analysis performance
   */
  static recordAnalysis(type: string, duration: number, success: boolean): void {
    MetricsCollector.recordMetric(
      'analysis.duration_ms',
      duration,
      {
        type,
        success: success.toString(),
      },
      'ms',
    );

    MetricsCollector.recordMetric('analysis.count', 1, {
      type,
      success: success.toString(),
    });
  }

  /**
   * Record database operation
   */
  static recordDatabaseOperation(operation: string, duration: number, success: boolean): void {
    MetricsCollector.recordMetric(
      'db.operation_duration_ms',
      duration,
      {
        operation,
        success: success.toString(),
      },
      'ms',
    );

    MetricsCollector.recordMetric('db.operation_count', 1, {
      operation,
      success: success.toString(),
    });
  }

  /**
   * Sanitize path for metrics (remove IDs, etc.)
   */
  private static sanitizePath(path: string): string {
    return path
      .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id') // UUIDs
      .replace(/\/\d+/g, '/:id') // Numeric IDs
      .replace(/\?.*$/, ''); // Query parameters
  }

  /**
   * Get monitoring dashboard data
   */
  static getDashboardData(): {
    uptime: number;
    memory: NodeJS.MemoryUsage;
    alerts: Array<{ rule: AlertRule; triggered: boolean; since: number }>;
    metrics: {
      responseTime: { avg: number; min: number; max: number; count: number; sum: number };
      requestCount: { avg: number; min: number; max: number; count: number; sum: number };
      errorRate: { avg: number; min: number; max: number; count: number; sum: number };
      memoryUsage: { avg: number; min: number; max: number; count: number; sum: number };
    };
    recentMetrics: MetricData[];
  } {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 hour

    return {
      uptime: process.uptime(),
      memory: MemoryMonitor.getCurrentMemoryUsage(),
      alerts: MetricsCollector.getAlertStatus(),
      metrics: {
        responseTime: MetricsCollector.getAggregatedMetrics('http.response_time_ms', oneHourAgo),
        requestCount: MetricsCollector.getAggregatedMetrics('http.request_count', oneHourAgo),
        errorRate: MetricsCollector.getAggregatedMetrics('app.error_rate', oneHourAgo),
        memoryUsage: MetricsCollector.getAggregatedMetrics('memory.heap_used_mb', oneHourAgo),
      },
      recentMetrics: MetricsCollector.getMetrics(undefined, oneHourAgo),
    };
  }
}

/**
 * Express middleware for request monitoring
 */
export function monitoringMiddleware() {
  return (req: ExpressRequest, res: ExpressResponse, next: ExpressNext) => {
    const startTime = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - startTime;
      ApplicationMonitor.recordRequest(req.method, req.path, res.statusCode, duration);
    });

    next();
  };
}

export default {
  MetricsCollector,
  ApplicationMonitor,
  monitoringMiddleware,
};
