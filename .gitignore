# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Local Environment Variables
.env
.env*.local
!/.env.example
!/.env.example.staging

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Editor directories and files
.idea
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.sublime-workspace

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarnclean

# dotenv environment variables file
.env

# Docker
**/docker-compose.override.yml

# TypeScript cache files
*.tsbuildinfo

# Build artifacts and compiled files
/dist/
/backend/dist/
/frontend/.next/
/coverage/

# Test files and temporary files
*.test.js
*.spec.js
test-*.js
quick-test.js
run-*.sh

# Temporary and backup files
*.tmp
*.bak
*.swp
*.deleted