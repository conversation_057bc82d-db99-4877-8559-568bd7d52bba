# Project Structure Guidelines

## 🏗️ Overview

This document provides detailed guidelines for organizing code, files, and directories within the Comply Checker project. Following these structure guidelines ensures consistency, maintainability, and scalability across the entire codebase.

## 📁 Root Directory Structure

```
comply-checker/                    # Project root
├── frontend/                      # Next.js 14 frontend application
├── backend/                       # Express.js backend API
├── docs/                          # Project documentation
├── scripts/                       # Utility and deployment scripts
├── tools/                         # External tools (Nuclei, etc.)
├── docker/                        # Docker configurations
├── .env                          # Environment variables (not in git)
├── .env.example                  # Environment template
├── .gitignore                    # Git ignore rules
├── .eslintrc.js                  # Root ESLint configuration
├── .prettierrc                   # Prettier configuration
├── docker-compose.yml            # Development services
├── docker-compose.prod.yml       # Production services
├── package.json                  # Root package configuration
├── README.md                     # Project overview
├── BUGS.md                       # Bug tracking
├── CLEANUP_PROPOSAL.md           # File cleanup documentation
├── PROJECT_STATUS_DOCUMENTATION.md  # Project status
├── BACKEND_ARCHITECTURE_DOCUMENTATION.md  # Backend docs
├── FRONTEND_ARCHITECTURE_DOCUMENTATION.md # Frontend docs
├── API_DOCUMENTATION.md          # API documentation
├── DEVELOPER_GUIDELINES.md       # Development guidelines
└── PROJECT_STRUCTURE_GUIDELINES.md  # This file
```

### Root Directory File Purposes

| File/Directory | Purpose | When to Modify |
|----------------|---------|----------------|
| `frontend/` | Next.js application code | Adding frontend features |
| `backend/` | Express.js API server code | Adding backend features |
| `docs/` | Project documentation | Adding documentation |
| `scripts/` | Automation and utility scripts | Adding deployment/utility scripts |
| `tools/` | External tools and binaries | Adding new scanning tools |
| `docker/` | Docker configurations | Modifying containerization |
| `.env` | Environment variables | Local configuration changes |
| `docker-compose.yml` | Development services | Adding new services |
| `package.json` | Root dependencies and scripts | Adding workspace commands |

## 🎨 Frontend Structure (Next.js 14)

### App Router Directory Structure
```
frontend/app/                      # Next.js 14 App Router
├── globals.css                    # Global styles and Tailwind imports
├── layout.tsx                     # Root layout with providers
├── page.tsx                       # Landing/home page
├── loading.tsx                    # Global loading UI
├── error.tsx                      # Global error UI
├── not-found.tsx                  # 404 page
├── dashboard/                     # Protected dashboard section
│   ├── layout.tsx                # Dashboard layout with navigation
│   ├── page.tsx                  # Dashboard home page
│   ├── loading.tsx               # Dashboard loading UI
│   ├── scan/                     # Scan management pages
│   │   ├── new/                  # New scan creation
│   │   │   ├── page.tsx          # New scan form page
│   │   │   └── loading.tsx       # New scan loading
│   │   └── [scanId]/             # Dynamic scan details
│   │       ├── page.tsx          # Scan results page
│   │       ├── loading.tsx       # Results loading
│   │       └── error.tsx         # Results error
│   ├── scans/                    # Scan history and management
│   │   ├── page.tsx              # Scans list page
│   │   ├── loading.tsx           # List loading
│   │   └── [scanId]/             # Individual scan details
│   ├── hipaa/                    # HIPAA-specific pages
│   │   ├── page.tsx              # HIPAA overview
│   │   ├── privacy/              # Privacy policy analysis
│   │   │   ├── page.tsx          # Privacy results
│   │   │   └── [scanId]/         # Specific privacy scan
│   │   └── security/             # Security compliance
│   │       ├── page.tsx          # Security results
│   │       └── [scanId]/         # Specific security scan
│   ├── gdpr/                     # GDPR compliance pages
│   │   ├── page.tsx              # GDPR overview
│   │   └── [scanId]/             # GDPR scan details
│   ├── ada/                      # ADA compliance pages
│   │   ├── page.tsx              # ADA overview
│   │   └── [scanId]/             # ADA scan details
│   └── wcag/                     # WCAG compliance pages
│       ├── page.tsx              # WCAG overview
│       └── [scanId]/             # WCAG scan details
├── auth/                         # Authentication pages
│   ├── login/                    # Login page
│   │   └── page.tsx              # Login form
│   ├── logout/                   # Logout page
│   │   └── page.tsx              # Logout confirmation
│   └── callback/                 # OAuth callback
│       └── page.tsx              # Auth callback handler
└── api/                          # API routes (if needed)
    └── auth/                     # Auth-related API routes
        └── callback/             # OAuth callback API
            └── route.ts          # Callback handler
```

### Component Organization
```
frontend/components/               # Reusable UI components
├── ui/                           # Base UI components (shadcn/ui)
│   ├── button.tsx                # Button component
│   ├── card.tsx                  # Card component
│   ├── badge.tsx                 # Badge component
│   ├── table.tsx                 # Table component
│   ├── dialog.tsx                # Modal dialog
│   ├── form.tsx                  # Form components
│   ├── input.tsx                 # Input component
│   ├── select.tsx                # Select dropdown
│   ├── textarea.tsx              # Textarea component
│   ├── toast.tsx                 # Toast notifications
│   ├── tabs.tsx                  # Tab component
│   ├── accordion.tsx             # Accordion component
│   ├── alert.tsx                 # Alert component
│   ├── progress.tsx              # Progress bar
│   ├── skeleton.tsx              # Loading skeleton
│   └── tooltip.tsx               # Tooltip component
├── layout/                       # Layout components
│   ├── Header.tsx                # Application header
│   ├── Sidebar.tsx               # Navigation sidebar
│   ├── Footer.tsx                # Application footer
│   ├── Navigation.tsx            # Main navigation
│   ├── Breadcrumbs.tsx           # Breadcrumb navigation
│   └── PageHeader.tsx            # Page header component
├── auth/                         # Authentication components
│   ├── LoginForm.tsx             # Login form
│   ├── UserProfile.tsx           # User profile display
│   ├── ProtectedRoute.tsx        # Route protection
│   └── AuthGuard.tsx             # Authentication guard
├── dashboard/                    # Dashboard-specific components
│   ├── DashboardStats.tsx        # Statistics overview
│   ├── RecentScans.tsx           # Recent scans display
│   ├── QuickActions.tsx          # Quick action buttons
│   ├── ComplianceOverview.tsx    # Compliance status overview
│   └── ActivityFeed.tsx          # Recent activity feed
├── scan/                         # Scan-related components
│   ├── ScanForm.tsx              # New scan form
│   ├── ScanResults.tsx           # Scan results display
│   ├── ScanHistory.tsx           # Scan history table
│   ├── ScanStatus.tsx            # Scan status indicator
│   ├── ScanProgress.tsx          # Scan progress indicator
│   └── ScanFilters.tsx           # Scan filtering controls
├── compliance/                   # Compliance-specific components
│   ├── hipaa/                    # HIPAA components
│   │   ├── HipaaPrivacyResults.tsx      # Privacy scan results
│   │   ├── HipaaSecurityResults.tsx     # Security scan results
│   │   ├── HipaaDashboard.tsx           # HIPAA dashboard
│   │   ├── ExecutiveSummary.tsx         # Executive summary
│   │   ├── ComplianceOverview.tsx       # Compliance overview
│   │   ├── FindingDetailsDisplay.tsx    # Finding details
│   │   ├── RiskAssessment.tsx           # Risk assessment
│   │   ├── SecurityCategories.tsx       # Security categories
│   │   └── PrivacyAnalysisLevels.tsx    # Privacy analysis levels
│   ├── gdpr/                     # GDPR components
│   │   ├── GdprResults.tsx       # GDPR scan results
│   │   ├── CookieConsent.tsx     # Cookie consent analysis
│   │   └── DataProcessing.tsx    # Data processing analysis
│   ├── ada/                      # ADA components
│   │   ├── AdaResults.tsx        # ADA scan results
│   │   ├── AccessibilityIssues.tsx # Accessibility issues
│   │   └── WcagCompliance.tsx    # WCAG compliance
│   └── wcag/                     # WCAG components
│       ├── WcagResults.tsx       # WCAG scan results
│       ├── AccessibilityReport.tsx # Accessibility report
│       └── ComplianceChecklist.tsx # Compliance checklist
└── common/                       # Common utility components
    ├── LoadingSpinner.tsx        # Loading indicator
    ├── ErrorBoundary.tsx         # Error boundary
    ├── DataTable.tsx             # Generic data table
    ├── StatusBadge.tsx           # Status badge
    ├── RiskLevelBadge.tsx        # Risk level indicator
    ├── ScoreDisplay.tsx          # Score display component
    ├── DateDisplay.tsx           # Date formatting component
    ├── UrlDisplay.tsx            # URL display component
    └── EmptyState.tsx            # Empty state component
```

### Supporting Directories
```
frontend/
├── context/                      # React Context providers
│   ├── AuthContext.tsx           # Authentication context
│   ├── ThemeContext.tsx          # Theme management
│   ├── ToastContext.tsx          # Toast notifications
│   └── ScanContext.tsx           # Scan state management
├── hooks/                        # Custom React hooks
│   ├── useAuth.ts                # Authentication hook
│   ├── useApi.ts                 # API interaction hook
│   ├── useLocalStorage.ts        # Local storage hook
│   ├── useDebounce.ts            # Debounce hook
│   ├── useScanResults.ts         # Scan results hook
│   └── useComplianceData.ts      # Compliance data hook
├── lib/                          # Utility libraries
│   ├── api.ts                    # API client functions
│   ├── auth.ts                   # Authentication utilities
│   ├── utils.ts                  # General utilities
│   ├── constants.ts              # Application constants
│   ├── validations.ts            # Form validation schemas
│   └── cn.ts                     # Class name utility
├── services/                     # API service layer
│   ├── hipaa-dashboard-api.ts    # HIPAA dashboard API
│   ├── scan-service.ts           # Scan management API
│   ├── auth-service.ts           # Authentication API
│   ├── compliance-api.ts         # General compliance API
│   └── base-api.ts               # Base API client
├── types/                        # TypeScript type definitions
│   ├── auth.ts                   # Authentication types
│   ├── scan.ts                   # Scan-related types
│   ├── hipaa-privacy.ts          # HIPAA privacy types
│   ├── hipaa-security.ts         # HIPAA security types
│   ├── gdpr.ts                   # GDPR types
│   ├── ada.ts                    # ADA types
│   ├── wcag.ts                   # WCAG types
│   └── api.ts                    # API response types
├── utils/                        # Utility functions
│   ├── accessibility.ts          # Accessibility helpers
│   ├── formatting.ts             # Data formatting
│   ├── validation.ts             # Input validation
│   ├── date.ts                   # Date utilities
│   ├── url.ts                    # URL utilities
│   └── score.ts                  # Score calculation utilities
├── styles/                       # Styling files
│   ├── globals.css               # Global CSS and Tailwind
│   └── components.css            # Component-specific styles
├── public/                       # Static assets
│   ├── images/                   # Image assets
│   │   ├── logos/                # Logo files
│   │   ├── icons/                # Icon files
│   │   └── illustrations/        # Illustration files
│   ├── favicon.ico               # Favicon
│   ├── manifest.json             # PWA manifest
│   └── robots.txt                # SEO robots file
└── __tests__/                    # Test files
    ├── components/               # Component tests
    ├── pages/                    # Page tests
    ├── utils/                    # Utility tests
    ├── hooks/                    # Hook tests
    ├── services/                 # Service tests
    └── setup.ts                  # Test setup
```

## 🔧 Backend Structure (Express.js)

### Main Application Structure
```
backend/src/                      # Source code
├── index.ts                      # Application entry point
├── app.ts                        # Express app configuration
├── server.ts                     # Server startup
├── routes/                       # API route definitions
│   ├── index.ts                  # Main router
│   ├── health.ts                 # Health check endpoints
│   ├── auth.ts                   # Authentication routes
│   └── compliance/               # Compliance-specific routes
│       ├── index.ts              # Compliance router
│       ├── hipaa.ts              # HIPAA routes
│       ├── gdpr.ts               # GDPR routes
│       ├── ada.ts                # ADA routes
│       ├── wcag.ts               # WCAG routes
│       └── scan.ts               # General scan routes
├── compliance/                   # Core compliance modules
│   ├── hipaa/                    # HIPAA compliance engine
│   │   ├── index.ts              # HIPAA module exports
│   │   ├── types.ts              # HIPAA type definitions
│   │   ├── privacy/              # Privacy policy analysis
│   │   │   ├── index.ts          # Privacy module exports
│   │   │   ├── orchestrator.ts   # Privacy scan orchestrator
│   │   │   ├── checks/           # Individual privacy checks
│   │   │   │   ├── privacy-policy-presence.ts
│   │   │   │   ├── contact-information.ts
│   │   │   │   ├── hipaa-specific-content.ts
│   │   │   │   ├── patient-rights.ts
│   │   │   │   └── data-usage-disclosure.ts
│   │   │   └── utils/            # Privacy analysis utilities
│   │   │       ├── nlp-analyzer.ts
│   │   │       ├── ai-analyzer.ts
│   │   │       ├── content-analyzer.ts
│   │   │       ├── improved-scoring.ts
│   │   │       └── positive-findings.ts
│   │   └── security/             # Security compliance analysis
│   │       ├── index.ts          # Security module exports
│   │       ├── orchestrator.ts   # Security scan orchestrator
│   │       ├── services/         # Security analysis services
│   │       │   ├── nuclei-client.ts
│   │       │   ├── ssl-analyzer.ts
│   │       │   ├── content-analyzer.ts
│   │       │   ├── scanner-config.ts
│   │       │   └── hipaa-security-scanner.ts
│   │       ├── tests/            # Security test definitions
│   │       │   ├── technical-safeguards.ts
│   │       │   ├── administrative-safeguards.ts
│   │       │   ├── organizational-safeguards.ts
│   │       │   └── physical-safeguards.ts
│   │       ├── types.ts          # Security type definitions
│   │       └── constants.ts      # Security constants
│   ├── gdpr/                     # GDPR compliance engine
│   │   ├── index.ts              # GDPR module exports
│   │   ├── types.ts              # GDPR type definitions
│   │   ├── cookie-consent-check.ts # Cookie consent analysis
│   │   ├── privacy-policy-check.ts # Privacy policy analysis
│   │   └── data-processing-check.ts # Data processing analysis
│   ├── ada/                      # ADA compliance engine
│   │   ├── index.ts              # ADA module exports
│   │   ├── types.ts              # ADA type definitions
│   │   ├── image-alt-text-check.ts # Image alt text check
│   │   ├── form-accessibility-check.ts # Form accessibility
│   │   └── navigation-check.ts   # Navigation accessibility
│   └── wcag/                     # WCAG compliance engine
│       ├── index.ts              # WCAG module exports
│       ├── types.ts              # WCAG type definitions
│       ├── page-title-check.ts   # Page title check
│       ├── heading-structure-check.ts # Heading structure
│       └── color-contrast-check.ts # Color contrast
├── services/                     # Business logic services
│   ├── scan-service.ts           # Main scanning orchestrator
│   ├── db-service.ts             # Database service layer
│   ├── hipaa-dashboard-service.ts # HIPAA dashboard service
│   ├── hipaa-privacy-service.ts  # HIPAA privacy scan service
│   └── hipaa-security-service.ts # HIPAA security scan service
├── middleware/                   # Express middleware
│   ├── auth.ts                   # Authentication middleware
│   ├── validation.ts             # Request validation
│   ├── error-handler.ts          # Error handling
│   ├── rate-limit.ts             # Rate limiting
│   ├── cors.ts                   # CORS configuration
│   └── logging.ts                # Request logging
├── config/                       # Configuration management
│   ├── database.ts               # Database configuration
│   ├── auth.ts                   # Authentication configuration
│   ├── app.ts                    # Application configuration
│   └── environment.ts            # Environment variables
├── lib/                          # Shared utilities
│   ├── db.ts                     # Database connection (Knex)
│   ├── constants.ts              # Application constants
│   ├── keycloak.ts               # Keycloak authentication
│   └── validators/               # Input validation utilities
├── types/                        # TypeScript type definitions
│   ├── express.ts                # Express type extensions
│   ├── auth.ts                   # Authentication types
│   ├── database.ts               # Database types
│   └── api.ts                    # API types
├── utils/                        # Helper functions
│   ├── health-check.ts           # Health check utilities
│   ├── logger.ts                 # Logging utilities
│   └── monitoring.ts             # Monitoring utilities
├── scripts/                      # Utility scripts
│   └── test-hipaa-scan.js        # HIPAA security scan test script
└── simple-server.ts              # Simple server implementation
```

### Database Structure
```
# Root-level database files (managed by Knex)
migrations/                       # TypeScript database migrations
├── 20250604075717_create_initial_tables.ts
├── 20250609061827_add_error_message_to_scans_table.ts
├── 20250610000000_fix_hipaa_score_types.ts
├── 20250613000000_add_positive_findings_column.ts
├── 20250616062046_hipaa_security_tables.ts
└── 20250617000000_enhance_hipaa_analysis_schema.ts

seeds/                            # Database seed data
├── 01_development_users.ts       # Development user data

backend/
├── knexfile.ts                   # Knex configuration
└── preload-env.js                # Environment preloader
```

### Backend Root Structure (Clean & Organized)
```
backend/
├── src/                          # All TypeScript source code
│   ├── compliance/               # Compliance engines (HIPAA, GDPR, WCAG, ADA)
│   ├── routes/                   # API route handlers
│   ├── services/                 # Business logic services
│   ├── config/                   # Configuration management
│   ├── lib/                      # Shared utilities
│   ├── types/                    # TypeScript type definitions
│   ├── utils/                    # Helper functions
│   ├── scripts/                  # Utility scripts
│   ├── index.ts                  # Main application entry point
│   └── simple-server.ts          # Simple server implementation
├── tools/                        # External tools
│   └── nuclei/                   # Nuclei security scanner
├── docker/                       # Docker-related files
├── dist/                         # Compiled JavaScript output
├── node_modules/                 # Dependencies
├── .eslintrc.js                  # ESLint configuration
├── jest.config.js                # Jest test configuration
├── nodemon.json                  # Nodemon configuration
├── package.json                  # Dependencies and scripts
├── tsconfig.json                 # TypeScript configuration
├── knexfile.ts                   # Database configuration
├── preload-env.js                # Environment preloader
├── Dockerfile                    # Docker configuration
├── Dockerfile.production         # Production Docker configuration
├── keycloak.json                 # Keycloak configuration
└── backendlogs.md                # Backend logs documentation
```

### Database Management
- **Migrations**: Located in root `/migrations/` directory (TypeScript files)
- **Seeds**: Located in root `/seeds/` directory (TypeScript files)
- **Management**: Use `npm run migrate:latest` and `npm run seed:run`
- **Configuration**: Defined in `backend/knexfile.ts`

## 📋 File Naming Conventions

### General Rules
- **Directories**: `kebab-case` (e.g., `hipaa-privacy`, `scan-results`)
- **TypeScript Files**: `kebab-case.ts` (e.g., `scan-service.ts`, `hipaa-dashboard-api.ts`)
- **React Components**: `PascalCase.tsx` (e.g., `ScanResults.tsx`, `HipaaDashboard.tsx`)
- **Test Files**: `*.test.ts` or `*.spec.ts` (e.g., `scan-service.test.ts`)
- **Type Definition Files**: `kebab-case.ts` (e.g., `hipaa-privacy.ts`, `api-types.ts`)

### Specific Patterns

#### Component Files
```
# ✅ Good
ScanResults.tsx
HipaaDashboard.tsx
ComplianceOverview.tsx
FindingDetailsDisplay.tsx

# ❌ Bad
scanResults.tsx
hipaa_dashboard.tsx
compliance-overview.tsx
```

#### Service Files
```
# ✅ Good
scan-service.ts
hipaa-dashboard-api.ts
auth-service.ts
compliance-api.ts

# ❌ Bad
ScanService.ts
hipaaAPI.ts
authService.ts
```

#### Type Definition Files
```
# ✅ Good
hipaa-privacy.ts
hipaa-security.ts
scan-types.ts
api-response.ts

# ❌ Bad
HipaaPrivacy.ts
HIPAA_Security.ts
scanTypes.ts
```

## 🎯 Where to Place New Files

### Adding New Compliance Standards
When adding a new compliance standard (e.g., SOX, PCI-DSS):

1. **Backend Structure**:
   ```
   backend/src/compliance/sox/
   ├── index.ts
   ├── types.ts
   ├── orchestrator.ts
   ├── checks/
   │   ├── financial-controls.ts
   │   └── audit-requirements.ts
   └── utils/
       └── sox-analyzer.ts
   ```

2. **Frontend Structure**:
   ```
   frontend/
   ├── app/dashboard/sox/
   │   ├── page.tsx
   │   └── [scanId]/page.tsx
   ├── components/compliance/sox/
   │   ├── SoxResults.tsx
   │   └── SoxDashboard.tsx
   └── types/sox.ts
   ```

3. **Routes**:
   ```
   backend/src/routes/compliance/sox.ts
   ```

### Adding New UI Components
When adding new reusable components:

1. **Base UI Components**: Place in `frontend/components/ui/`
2. **Layout Components**: Place in `frontend/components/layout/`
3. **Feature-Specific Components**: Place in appropriate feature directory
4. **Common Utilities**: Place in `frontend/components/common/`

### Adding New API Endpoints
When adding new API functionality:

1. **Route Definition**: `backend/src/routes/[feature]/[endpoint].ts`
2. **Service Logic**: `backend/src/services/[feature]-service.ts`
3. **Types**: `backend/src/types/[feature].ts`
4. **Frontend Service**: `frontend/services/[feature]-api.ts`

### Adding New Database Tables
When adding new database entities:

1. **Migration**: `backend/migrations/[timestamp]_[description].ts`
2. **Types**: `backend/src/types/database.ts`
3. **Seed Data**: `backend/seeds/[order]_[table].ts`

## 🔄 Architectural Patterns

### Layered Architecture
```
Presentation Layer (Frontend)
├── Pages (Next.js App Router)
├── Components (React Components)
├── Services (API Clients)
└── Types (TypeScript Interfaces)

Business Logic Layer (Backend)
├── Routes (Express Routes)
├── Services (Business Logic)
├── Compliance Modules (Domain Logic)
└── Middleware (Cross-cutting Concerns)

Data Access Layer
├── Database (PostgreSQL)
├── Migrations (Schema Management)
└── Seeds (Test Data)
```

### Module Organization
Each compliance standard follows a consistent module pattern:
```
[standard]/
├── index.ts          # Module exports
├── types.ts          # Type definitions
├── orchestrator.ts   # Main coordination logic
├── checks/           # Individual compliance checks
├── utils/            # Utility functions
└── constants.ts      # Module constants
```

---

*These structure guidelines ensure consistent organization and easy navigation throughout the Comply Checker project. Regular adherence to these patterns maintains code quality and developer productivity.*
