import { Router } from 'express';
import hipaaSecurityRoutes from '../hipaa-security';
import hipaaPrivacyRoutes from '../hipaa-privacy';
import { HipaaDashboardService } from '../../services/hipaa-dashboard-service';

const router = Router();

/**
 * @openapi
 * tags:
 *   name: HIPAA Compliance
 *   description: Routes for HIPAA specific compliance checks and operations.
 */

// Mount HIPAA Security routes under /security
router.use('/security', hipaaSecurityRoutes);

// Mount HIPAA Privacy routes under /privacy
router.use('/privacy', hipaaPrivacyRoutes);

// Dashboard routes
const dashboardService = new HipaaDashboardService();

/**
 * @openapi
 * /api/v1/compliance/hipaa/dashboard:
 *   get:
 *     summary: Get HIPAA dashboard data
 *     description: Returns aggregated data from both privacy and security modules for the dashboard
 *     tags: [HIPAA Compliance]
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       properties:
 *                         overallScore:
 *                           type: number
 *                         riskLevel:
 *                           type: string
 *                           enum: [critical, high, medium, low]
 *                         complianceStatus:
 *                           type: string
 *                           enum: [compliant, partially_compliant, non_compliant]
 *                         lastScanDate:
 *                           type: string
 *                           format: date-time
 *                         totalScans:
 *                           type: number
 *                     privacyModule:
 *                       type: object
 *                     securityModule:
 *                       type: object
 *                     recentActivity:
 *                       type: array
 *       500:
 *         description: Internal server error
 */
router.get('/dashboard', async (req, res) => {
  try {
    const dashboardData = await dashboardService.getDashboardData();
    res.json({
      success: true,
      data: dashboardData,
    });
  } catch (error) {
    console.error('Error fetching HIPAA dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard data',
    });
  }
});

/**
 * @openapi
 * /api/v1/compliance/hipaa/metrics:
 *   get:
 *     summary: Get HIPAA metrics
 *     description: Returns analytics and metrics for HIPAA compliance
 *     tags: [HIPAA Compliance]
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = await dashboardService.getDashboardMetrics();
    res.json({
      success: true,
      data: metrics,
    });
  } catch (error) {
    console.error('Error fetching HIPAA metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch metrics',
    });
  }
});

/**
 * @openapi
 * /api/v1/compliance/hipaa/privacy/scans:
 *   get:
 *     summary: Get privacy scans
 *     description: Returns list of privacy policy scans
 *     tags: [HIPAA Compliance]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of scans to return
 *     responses:
 *       200:
 *         description: Privacy scans retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/privacy/scans', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const scans = await dashboardService.getRecentPrivacyScans(limit);
    res.json({
      success: true,
      data: scans,
    });
  } catch (error) {
    console.error('Error fetching privacy scans:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch privacy scans',
    });
  }
});

/**
 * @openapi
 * /api/v1/compliance/hipaa/security/scans:
 *   get:
 *     summary: Get security scans
 *     description: Returns list of security scans
 *     tags: [HIPAA Compliance]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of scans to return
 *     responses:
 *       200:
 *         description: Security scans retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/security/scans', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const scans = await dashboardService.getRecentSecurityScans(limit);
    res.json({
      success: true,
      data: scans,
    });
  } catch (error) {
    console.error('Error fetching security scans:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security scans',
    });
  }
});

// Legacy HIPAA routes can be added here if needed
// Example:
// router.post('/check-access-controls', (req, res) => { ... });

export default router;
