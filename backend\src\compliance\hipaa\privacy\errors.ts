// backend/src/compliance/hipaa/errors.ts

/**
 * Enhanced Error Handling for HIPAA Compliance Module
 * Provides comprehensive error classes and recovery mechanisms
 */

/**
 * Base class for all HIPAA scan errors
 */
export class HipaaScanError extends Error {
  public readonly code: string;
  public readonly scanId?: string;
  public readonly checkId?: string;
  public readonly timestamp: string;
  public readonly context: Record<string, unknown>;
  public readonly recoverable: boolean;

  constructor(
    message: string,
    code: string,
    options: {
      scanId?: string;
      checkId?: string;
      context?: Record<string, unknown>;
      recoverable?: boolean;
      cause?: Error;
    } = {},
  ) {
    super(message);
    this.name = 'HipaaScanError';
    this.code = code;
    this.scanId = options.scanId;
    this.checkId = options.checkId;
    this.timestamp = new Date().toISOString();
    this.context = options.context || {};
    this.recoverable = options.recoverable ?? true;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }

    // Chain the cause if provided
    if (options.cause) {
      this.stack += `\nCaused by: ${options.cause.stack}`;
    }
  }

  /**
   * Convert error to JSON for logging and API responses
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      scanId: this.scanId,
      checkId: this.checkId,
      timestamp: this.timestamp,
      context: this.context,
      recoverable: this.recoverable,
      stack: this.stack,
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    return this.message;
  }

  /**
   * Get technical details for debugging
   */
  getTechnicalDetails(): Record<string, unknown> {
    return {
      code: this.code,
      context: this.context,
      stack: this.stack,
    };
  }
}

/**
 * Network-related errors (timeouts, connection failures)
 */
export class HipaaNetworkError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      url?: string;
      timeout?: number;
      statusCode?: number;
      scanId?: string;
      checkId?: string;
      cause?: Error;
    } = {},
  ) {
    super(message, 'NETWORK_ERROR', {
      ...options,
      context: {
        url: options.url,
        timeout: options.timeout,
        statusCode: options.statusCode,
      },
      recoverable: true,
    });
    this.name = 'HipaaNetworkError';
  }

  getUserMessage(): string {
    const { url, statusCode } = this.context;
    if (statusCode) {
      return `Unable to access website (HTTP ${statusCode}). Please verify the URL is correct and accessible.`;
    }
    if (url) {
      return `Unable to connect to ${url}. Please check your internet connection and try again.`;
    }
    return 'Network connection failed. Please check your internet connection and try again.';
  }
}

/**
 * Content parsing and analysis errors
 */
export class HipaaContentError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      contentType?: string;
      contentLength?: number;
      analysisLevel?: number;
      scanId?: string;
      checkId?: string;
      cause?: Error;
    } = {},
  ) {
    super(message, 'CONTENT_ERROR', {
      ...options,
      context: {
        contentType: options.contentType,
        contentLength: options.contentLength,
        analysisLevel: options.analysisLevel,
      },
      recoverable: true,
    });
    this.name = 'HipaaContentError';
  }

  getUserMessage(): string {
    const contentType = this.context.contentType as string | undefined;
    const analysisLevel = this.context.analysisLevel as number | undefined;
    if (analysisLevel) {
      return `Content analysis failed at level ${analysisLevel}. The website content may be in an unsupported format.`;
    }
    if (contentType && !contentType.includes('html')) {
      return `Unsupported content type: ${contentType}. Please provide a valid HTML webpage.`;
    }
    return 'Unable to analyze website content. Please ensure the page contains valid HTML.';
  }
}

/**
 * AI/NLP analysis specific errors
 */
export class HipaaAnalysisError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      analysisType?: 'pattern' | 'nlp' | 'ai';
      modelName?: string;
      inputLength?: number;
      scanId?: string;
      checkId?: string;
      cause?: Error;
    } = {},
  ) {
    super(message, 'ANALYSIS_ERROR', {
      ...options,
      context: {
        analysisType: options.analysisType,
        modelName: options.modelName,
        inputLength: options.inputLength,
      },
      recoverable: options.analysisType !== 'ai', // AI errors are less recoverable
    });
    this.name = 'HipaaAnalysisError';
  }

  getUserMessage(): string {
    const { analysisType } = this.context;

    switch (analysisType) {
      case 'pattern':
        return 'Basic pattern analysis failed. The scan will continue with reduced accuracy.';
      case 'nlp':
        return 'Natural language processing failed. The scan will continue with basic pattern matching.';
      case 'ai':
        return 'AI analysis is temporarily unavailable. The scan completed using basic analysis methods.';
      default:
        return 'Content analysis encountered an error but the scan continued with available methods.';
    }
  }
}

/**
 * Database operation errors
 */
export class HipaaDatabaseError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      operation?: 'save' | 'retrieve' | 'update' | 'delete';
      table?: string;
      scanId?: string;
      checkId?: string;
      cause?: Error;
    } = {},
  ) {
    super(message, 'DATABASE_ERROR', {
      ...options,
      context: {
        operation: options.operation,
        table: options.table,
      },
      recoverable: options.operation === 'save', // Save operations can be retried
    });
    this.name = 'HipaaDatabaseError';
  }

  getUserMessage(): string {
    const { operation } = this.context;

    switch (operation) {
      case 'save':
        return 'Unable to save scan results. The scan completed successfully but results may not be stored.';
      case 'retrieve':
        return 'Unable to retrieve previous scan results. This may affect comparison features.';
      default:
        return 'Database operation failed. Some features may be temporarily unavailable.';
    }
  }
}

/**
 * Configuration and validation errors
 */
export class HipaaConfigurationError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      parameter?: string;
      value?: unknown;
      expectedType?: string;
      scanId?: string;
      checkId?: string;
    } = {},
  ) {
    super(message, 'CONFIGURATION_ERROR', {
      ...options,
      context: {
        parameter: options.parameter,
        value: options.value,
        expectedType: options.expectedType,
      },
      recoverable: false, // Configuration errors require manual intervention
    });
    this.name = 'HipaaConfigurationError';
  }

  getUserMessage(): string {
    const { parameter } = this.context;
    if (parameter) {
      return `Invalid configuration for ${parameter}. Please check your settings and try again.`;
    }
    return 'Configuration error detected. Please verify your scan settings.';
  }
}

/**
 * Timeout errors
 */
export class HipaaTimeoutError extends HipaaScanError {
  constructor(
    message: string,
    options: {
      operation?: string;
      timeout?: number;
      scanId?: string;
      checkId?: string;
    } = {},
  ) {
    super(message, 'TIMEOUT_ERROR', {
      ...options,
      context: {
        operation: options.operation,
        timeout: options.timeout,
      },
      recoverable: true,
    });
    this.name = 'HipaaTimeoutError';
  }

  getUserMessage(): string {
    const operation = this.context.operation as string | undefined;
    const timeout = this.context.timeout as number | undefined;
    const timeoutSeconds = timeout ? Math.round(timeout / 1000) : 30;

    if (operation) {
      return `${operation} timed out after ${timeoutSeconds} seconds. The website may be slow to respond.`;
    }
    return `Operation timed out after ${timeoutSeconds} seconds. Please try again or check if the website is accessible.`;
  }
}

/**
 * Error recovery utilities
 */
export class HipaaErrorRecovery {
  /**
   * Attempt to recover from an error
   */
  static async attemptRecovery<T>(
    operation: () => Promise<T>,
    errorHandler: (error: Error) => HipaaScanError,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        const hipaaError = errorHandler(lastError);

        console.warn(
          `HipaaErrorRecovery: Attempt ${attempt}/${maxRetries} failed:`,
          hipaaError.toJSON(),
        );

        // Don't retry if error is not recoverable
        if (!hipaaError.recoverable) {
          throw hipaaError;
        }

        // Don't retry on last attempt
        if (attempt === maxRetries) {
          throw hipaaError;
        }

        // Wait before retry with exponential backoff
        const delay = retryDelay * Math.pow(2, attempt - 1);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // This should never be reached, but TypeScript requires it
    throw errorHandler(lastError!);
  }

  /**
   * Create appropriate error based on the original error
   */
  static createAppropriateError(
    originalError: Error,
    context: {
      scanId?: string;
      checkId?: string;
      operation?: string;
      url?: string;
    } = {},
  ): HipaaScanError {
    const message = originalError.message;

    // Network errors
    if (message.includes('timeout') || message.includes('ECONNABORTED')) {
      return new HipaaTimeoutError(message, {
        operation: context.operation,
        timeout: 30000,
        scanId: context.scanId,
        checkId: context.checkId,
      });
    }

    if (
      message.includes('ECONNREFUSED') ||
      message.includes('ENOTFOUND') ||
      message.includes('network')
    ) {
      return new HipaaNetworkError(message, {
        url: context.url,
        scanId: context.scanId,
        checkId: context.checkId,
        cause: originalError,
      });
    }

    // Content errors
    if (message.includes('parse') || message.includes('invalid') || message.includes('malformed')) {
      return new HipaaContentError(message, {
        scanId: context.scanId,
        checkId: context.checkId,
        cause: originalError,
      });
    }

    // Database errors
    if (message.includes('database') || message.includes('sql') || message.includes('connection')) {
      return new HipaaDatabaseError(message, {
        operation: context.operation as 'save' | 'retrieve' | 'update' | 'delete',
        scanId: context.scanId,
        checkId: context.checkId,
        cause: originalError,
      });
    }

    // Default to generic HIPAA error
    return new HipaaScanError(message, 'UNKNOWN_ERROR', {
      scanId: context.scanId,
      checkId: context.checkId,
      context: { originalError: originalError.name },
      recoverable: true,
      cause: originalError,
    });
  }

  /**
   * Log error with appropriate level
   */
  static logError(error: HipaaScanError, logger: Console = console): void {
    const logData = {
      ...error.toJSON(),
      userMessage: error.getUserMessage(),
      technicalDetails: error.getTechnicalDetails(),
    };

    if (error.recoverable) {
      logger.warn('Recoverable HIPAA scan error:', logData);
    } else {
      logger.error('Non-recoverable HIPAA scan error:', logData);
    }
  }

  /**
   * Get user-friendly error response
   */
  static getUserErrorResponse(error: HipaaScanError): {
    message: string;
    code: string;
    recoverable: boolean;
    suggestions: string[];
  } {
    const suggestions: string[] = [];

    switch (error.code) {
      case 'NETWORK_ERROR':
        suggestions.push('Verify the URL is correct and accessible');
        suggestions.push('Check your internet connection');
        suggestions.push('Try again in a few minutes');
        break;
      case 'TIMEOUT_ERROR':
        suggestions.push('The website may be slow to respond');
        suggestions.push('Try again with a longer timeout');
        suggestions.push('Check if the website is accessible in a browser');
        break;
      case 'CONTENT_ERROR':
        suggestions.push('Ensure the URL points to a valid HTML webpage');
        suggestions.push('Check if the page requires authentication');
        suggestions.push('Verify the page contains privacy policy content');
        break;
      case 'ANALYSIS_ERROR':
        suggestions.push('The scan completed with reduced analysis capabilities');
        suggestions.push('Results may have lower confidence scores');
        suggestions.push('Consider running the scan again later');
        break;
      default:
        suggestions.push('Please try again');
        suggestions.push('Contact support if the problem persists');
    }

    return {
      message: error.getUserMessage(),
      code: error.code,
      recoverable: error.recoverable,
      suggestions,
    };
  }
}
