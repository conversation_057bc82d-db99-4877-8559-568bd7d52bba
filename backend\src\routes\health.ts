import { Router, Request, Response } from 'express';

const router = Router();

/**
 * @openapi
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns the current operational status of the service, including uptime and database connectivity.
 *     tags:
 *       - Health
 *     responses:
 *       200:
 *         description: Service is healthy.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: UP
 *                 uptime:
 *                   type: number
 *                   description: Server uptime in seconds.
 *                   example: 120.5
 *                 database:
 *                   type: boolean
 *                   description: Indicates if the database connection is healthy.
 *                   example: true
 *       503:
 *         description: Service is unavailable (e.g., database connection issue).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: DOWN
 *                 uptime:
 *                   type: number
 *                   example: 120.5
 *                 database:
 *                   type: boolean
 *                   example: false
 */
// Apply keycloak.protect() to all routes in this router, or to specific routes
// To protect all routes in this file:
// router.use(keycloak.protect()); // This would protect all subsequent routes in this router

// To protect only the GET / route:
router.get('/', (req: Request, res: Response) => {
  // Health endpoint should be public for monitoring
  // TODO: Implement actual database health check
  const isDatabaseHealthy = true; // Placeholder

  // Access user info if needed (demonstration)
  // const token = req.kauth?.grant?.access_token;
  // if (token) {
  //   console.log('Access token content:', token.content);
  //   console.log('User:', token.content.preferred_username);
  //   console.log('User Roles:', token.content.realm_access?.roles);
  // }

  if (!isDatabaseHealthy) {
    return res.status(503).json({
      status: 'DOWN',
      uptime: process.uptime(),
      database: false,
    });
  }

  res.status(200).json({
    status: 'UP',
    uptime: process.uptime(),
    database: isDatabaseHealthy,
  });
});

export default router;
