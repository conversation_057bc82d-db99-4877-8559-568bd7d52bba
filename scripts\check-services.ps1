#!/usr/bin/env pwsh
# PowerShell script to check the health of all Comply Checker services

Write-Host "🔍 Checking Comply Checker Services Health..." -ForegroundColor Cyan

# Function to test HTTP endpoint
function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$TimeoutSeconds = 10
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $ServiceName is responding (HTTP $($response.StatusCode))" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $ServiceName responded with HTTP $($response.StatusCode)" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ $ServiceName is not responding: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check Docker container status
function Get-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker inspect --format='{{.State.Status}}' $ContainerName 2>$null
        $health = docker inspect --format='{{.State.Health.Status}}' $ContainerName 2>$null
        
        if ($status -eq "running") {
            if ($health -eq "healthy") {
                Write-Host "✅ $ContainerName: Running & Healthy" -ForegroundColor Green
            } elseif ($health -eq "unhealthy") {
                Write-Host "⚠️ $ContainerName: Running but Unhealthy" -ForegroundColor Yellow
            } else {
                Write-Host "🔄 $ContainerName: Running (no health check)" -ForegroundColor Blue
            }
        } else {
            Write-Host "❌ $ContainerName: $status" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ $ContainerName: Not found" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Docker Container Status:" -ForegroundColor Cyan
Get-ContainerStatus "comply_checker_postgres"
Get-ContainerStatus "comply_checker_keycloak"
Get-ContainerStatus "comply_checker_mailhog"
Get-ContainerStatus "comply_checker_backend"

Write-Host ""
Write-Host "🌐 HTTP Endpoint Tests:" -ForegroundColor Cyan

# Test Backend API
Test-HttpEndpoint "http://localhost:3001/health" "Backend API"

# Test Keycloak
Test-HttpEndpoint "http://localhost:8080/auth/health/ready" "Keycloak"

# Test MailHog
Test-HttpEndpoint "http://localhost:8025" "MailHog Web UI"

Write-Host ""
Write-Host "🔧 Port Usage Check:" -ForegroundColor Cyan

$ports = @(3001, 5432, 8080, 8025, 1025)
foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "✅ Port $port is in use" -ForegroundColor Green
    } else {
        Write-Host "❌ Port $port is not in use" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📋 Quick Commands:" -ForegroundColor Cyan
Write-Host "  View logs:       docker-compose logs -f [service_name]" -ForegroundColor White
Write-Host "  Restart service: docker-compose restart [service_name]" -ForegroundColor White
Write-Host "  Stop all:        docker-compose down" -ForegroundColor White
Write-Host "  Start all:       .\scripts\start-docker-services.ps1" -ForegroundColor White
