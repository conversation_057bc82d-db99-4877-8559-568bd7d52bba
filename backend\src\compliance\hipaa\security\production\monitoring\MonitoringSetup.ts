/**
 * PART 8: Enhanced Monitoring and Observability Setup
 * Comprehensive monitoring based on validation findings
 */

import { EventEmitter } from 'events';

// Performance metric data structure
export interface PerformanceMetricData {
  scanId: string;
  startTime: Date;
  endTime?: Date;
  totalDuration: number;
  performanceScore: number;
  timeouts: string[];
  retryAttempts: number;
  testResults: Record<string, number>;
}

// Alert data structure
export interface AlertData {
  type: 'critical' | 'warning' | 'info';
  message: string;
  category: string;
  threshold: number;
  actualValue: number;
  suggestions: string[];
}

// Circuit breaker event data
export interface CircuitBreakerEventData {
  serviceKey: string;
  state: 'open' | 'closed' | 'half-open';
  failures: number;
  timestamp: Date;
}

// Reliability metrics structure
export interface ReliabilityMetricsData {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  retriedRequests: number;
  circuitBreakerTrips: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  lastIncident: Date | null;
}

// Mock interfaces for advanced components (optional production features)
interface PerformanceMonitor extends EventEmitter {
  getAllMetrics(): PerformanceMetricData[];
  on(event: 'alertCreated', listener: (alert: AlertData) => void): this;
}

interface ReliabilityManager extends EventEmitter {
  getReliabilityMetrics(): ReliabilityMetricsData;
  on(event: 'circuitBreakerOpened', listener: (data: CircuitBreakerEventData) => void): this;
}

export interface MonitoringConfig {
  metrics: {
    enabled: boolean;
    interval: number;
    retention: number;
    exporters: string[];
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    format: 'json' | 'text';
    outputs: string[];
    enableStructuredLogging: boolean;
  };
  tracing: {
    enabled: boolean;
    sampleRate: number;
    exporters: string[];
  };
  alerting: {
    enabled: boolean;
    channels: string[];
    thresholds: AlertThresholds;
  };
  healthChecks: {
    enabled: boolean;
    interval: number;
    timeout: number;
    endpoints: string[];
  };
}

export interface AlertThresholds {
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  scanFailureRate: number;
  timeoutRate: number;
  circuitBreakerTrips: number;
}

export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  labels: Record<string, string>;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
}

export interface LogEntry {
  timestamp: Date;
  level: string;
  message: string;
  service: string;
  scanId?: string;
  userId?: string;
  metadata: Record<string, string | number | boolean>;
  error?: {
    name: string;
    message: string;
    stack: string;
  };
}

export interface Alert {
  id: string;
  severity: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  timestamp: Date;
  service: string;
  metric: string;
  threshold: number;
  actualValue: number;
  resolved: boolean;
  resolvedAt?: Date;
}

export class MonitoringSetup extends EventEmitter {
  private config: MonitoringConfig;
  private performanceMonitor: PerformanceMonitor;
  private reliabilityManager: ReliabilityManager;
  private metrics: Map<string, MetricData[]> = new Map();
  private alerts: Alert[] = [];
  private healthStatus: Map<string, boolean> = new Map();

  constructor(
    config: MonitoringConfig,
    performanceMonitor?: PerformanceMonitor,
    reliabilityManager?: ReliabilityManager,
  ) {
    super();
    this.config = config;

    // Create mock instances if not provided (for optional advanced features)
    if (!performanceMonitor) {
      const mockMonitor = new EventEmitter() as PerformanceMonitor;
      mockMonitor.getAllMetrics = (): PerformanceMetricData[] => [];
      this.performanceMonitor = mockMonitor;
    } else {
      this.performanceMonitor = performanceMonitor;
    }

    if (!reliabilityManager) {
      const mockManager = new EventEmitter() as ReliabilityManager;
      mockManager.getReliabilityMetrics = (): ReliabilityMetricsData => ({
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        retriedRequests: 0,
        circuitBreakerTrips: 0,
        averageResponseTime: 0,
        errorRate: 0,
        uptime: 100,
        lastIncident: null,
      });
      this.reliabilityManager = mockManager;
    } else {
      this.reliabilityManager = reliabilityManager;
    }

    this.setupMonitoring();
  }

  private setupMonitoring(): void {
    if (this.config.metrics.enabled) {
      this.startMetricsCollection();
    }

    if (this.config.healthChecks.enabled) {
      this.startHealthChecks();
    }

    if (this.config.alerting.enabled) {
      this.setupAlerting();
    }

    this.setupEventListeners();
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      this.collectSystemMetrics();
      this.collectApplicationMetrics();
      this.collectBusinessMetrics();
    }, this.config.metrics.interval);
  }

  private collectSystemMetrics(): void {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // Memory metrics
    this.recordMetric({
      name: 'system_memory_heap_used',
      value: memoryUsage.heapUsed,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    this.recordMetric({
      name: 'system_memory_heap_total',
      value: memoryUsage.heapTotal,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    // CPU metrics
    this.recordMetric({
      name: 'system_cpu_user',
      value: cpuUsage.user,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    this.recordMetric({
      name: 'system_cpu_system',
      value: cpuUsage.system,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    // Check thresholds
    this.checkSystemAlerts(memoryUsage, cpuUsage);
  }

  private collectApplicationMetrics(): void {
    const reliabilityMetrics = this.reliabilityManager.getReliabilityMetrics();

    // Request metrics
    this.recordMetric({
      name: 'app_requests_total',
      value: reliabilityMetrics.totalRequests,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    this.recordMetric({
      name: 'app_requests_successful',
      value: reliabilityMetrics.successfulRequests,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    this.recordMetric({
      name: 'app_requests_failed',
      value: reliabilityMetrics.failedRequests,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    // Error rate
    const errorRate =
      reliabilityMetrics.totalRequests > 0
        ? reliabilityMetrics.failedRequests / reliabilityMetrics.totalRequests
        : 0;

    this.recordMetric({
      name: 'app_error_rate',
      value: errorRate,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    // Response time
    this.recordMetric({
      name: 'app_response_time_avg',
      value: reliabilityMetrics.averageResponseTime,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    // Circuit breaker trips
    this.recordMetric({
      name: 'app_circuit_breaker_trips',
      value: reliabilityMetrics.circuitBreakerTrips,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    // Check application alerts
    this.checkApplicationAlerts(reliabilityMetrics);
  }

  private collectBusinessMetrics(): void {
    const allMetrics = this.performanceMonitor.getAllMetrics();

    // Scan metrics
    const totalScans = allMetrics.length;
    const completedScans = allMetrics.filter((m) => m.endTime).length;
    const avgScanDuration =
      allMetrics.reduce((sum, m) => sum + m.totalDuration, 0) / totalScans || 0;
    const avgPerformanceScore =
      allMetrics.reduce((sum, m) => sum + m.performanceScore, 0) / totalScans || 0;

    this.recordMetric({
      name: 'business_scans_total',
      value: totalScans,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    this.recordMetric({
      name: 'business_scans_completed',
      value: completedScans,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    this.recordMetric({
      name: 'business_scan_duration_avg',
      value: avgScanDuration,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    this.recordMetric({
      name: 'business_performance_score_avg',
      value: avgPerformanceScore,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'gauge',
    });

    // Timeout and retry metrics
    const totalTimeouts = allMetrics.reduce((sum, m) => sum + m.timeouts.length, 0);
    const totalRetries = allMetrics.reduce((sum, m) => sum + m.retryAttempts, 0);

    this.recordMetric({
      name: 'business_timeouts_total',
      value: totalTimeouts,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    this.recordMetric({
      name: 'business_retries_total',
      value: totalRetries,
      timestamp: new Date(),
      labels: { service: 'hipaa-security' },
      type: 'counter',
    });

    // Check business alerts
    this.checkBusinessAlerts(totalScans, avgScanDuration, totalTimeouts, totalRetries);
  }

  private startHealthChecks(): void {
    setInterval(() => {
      this.performHealthChecks();
    }, this.config.healthChecks.interval);
  }

  private async performHealthChecks(): Promise<void> {
    const services = ['database', 'zap-proxy', 'redis', 'application'];

    for (const service of services) {
      try {
        const isHealthy = await this.checkServiceHealth(service);
        this.healthStatus.set(service, isHealthy);

        this.recordMetric({
          name: 'health_check_status',
          value: isHealthy ? 1 : 0,
          timestamp: new Date(),
          labels: { service },
          type: 'gauge',
        });

        if (!isHealthy) {
          this.createAlert({
            severity: 'critical',
            title: `Service Health Check Failed`,
            description: `Health check failed for service: ${service}`,
            service,
            metric: 'health_check_status',
            threshold: 1,
            actualValue: 0,
          });
        }
      } catch (error) {
        this.healthStatus.set(service, false);
        this.logError('Health check error', error as Error, { service });
      }
    }
  }

  private async checkServiceHealth(service: string): Promise<boolean> {
    // Mock health checks - in real implementation, these would make actual health check calls
    switch (service) {
      case 'database':
        return true; // Would check database connectivity
      case 'zap-proxy':
        return true; // Would check ZAP proxy status
      case 'redis':
        return true; // Would check Redis connectivity
      case 'application':
        return true; // Would check application endpoints
      default:
        return false;
    }
  }

  private setupAlerting(): void {
    // Set up alert processing
    this.on('alert', (alert: Alert) => {
      this.processAlert(alert);
    });
  }

  private setupEventListeners(): void {
    // Listen to performance monitor events
    this.performanceMonitor.on('alertCreated', (alert: AlertData) => {
      this.createAlert({
        severity: alert.type === 'critical' ? 'critical' : 'warning',
        title: alert.message,
        description: alert.suggestions.join(', '),
        service: 'hipaa-security',
        metric: alert.category,
        threshold: alert.threshold,
        actualValue: alert.actualValue,
      });
    });

    // Listen to reliability manager events
    this.reliabilityManager.on('circuitBreakerOpened', (data: CircuitBreakerEventData) => {
      this.createAlert({
        severity: 'critical',
        title: 'Circuit Breaker Opened',
        description: `Circuit breaker opened for service: ${data.serviceKey}`,
        service: data.serviceKey,
        metric: 'circuit_breaker_state',
        threshold: 0,
        actualValue: 1,
      });
    });
  }

  private checkSystemAlerts(memoryUsage: NodeJS.MemoryUsage, _cpuUsage: NodeJS.CpuUsage): void {
    // Memory usage alert
    const memoryUsagePercent = memoryUsage.heapUsed / memoryUsage.heapTotal;
    if (memoryUsagePercent > this.config.alerting.thresholds.memoryUsage) {
      this.createAlert({
        severity: 'warning',
        title: 'High Memory Usage',
        description: `Memory usage is ${(memoryUsagePercent * 100).toFixed(1)}%`,
        service: 'hipaa-security',
        metric: 'memory_usage',
        threshold: this.config.alerting.thresholds.memoryUsage,
        actualValue: memoryUsagePercent,
      });
    }
  }

  private checkApplicationAlerts(metrics: ReliabilityMetricsData): void {
    // Error rate alert
    if (metrics.errorRate > this.config.alerting.thresholds.errorRate) {
      this.createAlert({
        severity: 'critical',
        title: 'High Error Rate',
        description: `Error rate is ${(metrics.errorRate * 100).toFixed(1)}%`,
        service: 'hipaa-security',
        metric: 'error_rate',
        threshold: this.config.alerting.thresholds.errorRate,
        actualValue: metrics.errorRate,
      });
    }

    // Response time alert
    if (metrics.averageResponseTime > this.config.alerting.thresholds.responseTime) {
      this.createAlert({
        severity: 'warning',
        title: 'High Response Time',
        description: `Average response time is ${metrics.averageResponseTime}ms`,
        service: 'hipaa-security',
        metric: 'response_time',
        threshold: this.config.alerting.thresholds.responseTime,
        actualValue: metrics.averageResponseTime,
      });
    }
  }

  private checkBusinessAlerts(
    totalScans: number,
    avgDuration: number,
    timeouts: number,
    _retries: number,
  ): void {
    // Scan failure rate
    if (totalScans > 0) {
      const timeoutRate = timeouts / totalScans;
      if (timeoutRate > this.config.alerting.thresholds.timeoutRate) {
        this.createAlert({
          severity: 'warning',
          title: 'High Timeout Rate',
          description: `Timeout rate is ${(timeoutRate * 100).toFixed(1)}%`,
          service: 'hipaa-security',
          metric: 'timeout_rate',
          threshold: this.config.alerting.thresholds.timeoutRate,
          actualValue: timeoutRate,
        });
      }
    }
  }

  private recordMetric(metric: MetricData): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metricHistory = this.metrics.get(metric.name)!;
    metricHistory.push(metric);

    // Keep only recent metrics based on retention policy
    const cutoffTime = new Date(Date.now() - this.config.metrics.retention);
    const filteredHistory = metricHistory.filter((m) => m.timestamp > cutoffTime);
    this.metrics.set(metric.name, filteredHistory);

    this.emit('metricRecorded', metric);
  }

  private createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'resolved'>): void {
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      resolved: false,
      ...alertData,
    };

    this.alerts.push(alert);
    this.emit('alert', alert);
  }

  private processAlert(alert: Alert): void {
    // Log the alert
    this.logInfo('Alert created', {
      alertId: alert.id,
      severity: alert.severity,
      title: alert.title,
      service: alert.service,
      metric: alert.metric,
    });

    // Send to configured channels
    this.config.alerting.channels.forEach((channel) => {
      this.sendAlertToChannel(alert, channel);
    });
  }

  private sendAlertToChannel(alert: Alert, channel: string): void {
    // Mock alert sending - in real implementation, this would send to Slack, email, etc.
    console.log(`Sending alert ${alert.id} to ${channel}: ${alert.title}`);
  }

  private logInfo(message: string, metadata: Record<string, string | number | boolean> = {}): void {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level: 'info',
      message,
      service: 'hipaa-security',
      metadata,
    };

    this.emit('log', logEntry);
  }

  private logError(
    message: string,
    error: Error,
    metadata: Record<string, string | number | boolean> = {},
  ): void {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level: 'error',
      message,
      service: 'hipaa-security',
      metadata,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack || '',
      },
    };

    this.emit('log', logEntry);
  }

  // Public API methods
  getMetrics(metricName?: string): MetricData[] | Map<string, MetricData[]> {
    if (metricName) {
      return this.metrics.get(metricName) || [];
    }
    return this.metrics;
  }

  getAlerts(resolved?: boolean): Alert[] {
    if (resolved !== undefined) {
      return this.alerts.filter((alert) => alert.resolved === resolved);
    }
    return this.alerts;
  }

  getHealthStatus(): Map<string, boolean> {
    return new Map(this.healthStatus);
  }

  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find((a) => a.id === alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      this.emit('alertResolved', alert);
      return true;
    }
    return false;
  }

  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }
}
