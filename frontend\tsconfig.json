{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".", // Added to make paths relative to frontend directory
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"],
      "@/backend-types/*": ["../backend/src/types/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    ".eslintrc.js",
    "postcss.config.js",
    "tailwind.config.js"
  ],
  "exclude": ["node_modules", "__tests__/**/*", "**/*.test.ts", "**/*.test.tsx"]
}
