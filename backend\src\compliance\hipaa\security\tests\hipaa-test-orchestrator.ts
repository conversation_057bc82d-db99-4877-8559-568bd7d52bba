import { NucleiClient } from '../services/nuclei-client';
import { SSLAnalyzer } from '../services/ssl-analyzer';
import { ContentAnalyzer } from '../services/content-analyzer';
import { AccessControlTest } from './access-control-test';
import { AuthenticationTest } from './authentication-test';
import { TransmissionSecurityTest } from './transmission-security-test';
import { EPHIDetectionTest } from './ephi-detection-test';
import { AuditControlsTest } from './audit-controls-test';
import {
  HipaaTestDetail,
  HipaaTestFailure,
  CategoryResult,
  RiskLevel,
  HipaaCategory,
} from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export interface HipaaTestSuiteConfig {
  targetUrl: string;
  protectedEndpoints: string[];
  publicEndpoints: string[];
  pagesToScan: string[];
  timeout: number;
  enableAllTests: boolean;
  testCategories: HipaaCategory[];
}

export interface HipaaTestSuiteResult {
  overallScore: number;
  riskLevel: RiskLevel;
  totalTests: number;
  passedTests: number;
  failedTests: number;

  // Test results by category
  technicalSafeguards: CategoryResult;
  administrativeSafeguards: CategoryResult;
  organizationalSafeguards: CategoryResult;
  physicalSafeguards: CategoryResult;

  // All test results
  allTestResults: (HipaaTestDetail | HipaaTestFailure)[];

  // Summary
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;

  // Execution metadata
  executionTime: number;
  timestamp: Date;
}

export class HipaaTestOrchestrator {
  private nucleiClient: NucleiClient;
  private sslAnalyzer: SSLAnalyzer;
  private contentAnalyzer: ContentAnalyzer;

  // Test modules
  private accessControlTest: AccessControlTest;
  private authenticationTest: AuthenticationTest;
  private transmissionSecurityTest: TransmissionSecurityTest;
  private ephiDetectionTest: EPHIDetectionTest;
  private auditControlsTest: AuditControlsTest;

  constructor(
    nucleiClient: NucleiClient,
    sslAnalyzer: SSLAnalyzer,
    contentAnalyzer: ContentAnalyzer,
  ) {
    this.nucleiClient = nucleiClient;
    this.sslAnalyzer = sslAnalyzer;
    this.contentAnalyzer = contentAnalyzer;

    // Initialize test modules
    this.accessControlTest = new AccessControlTest(nucleiClient, contentAnalyzer);
    this.authenticationTest = new AuthenticationTest(nucleiClient, contentAnalyzer);
    this.transmissionSecurityTest = new TransmissionSecurityTest(
      nucleiClient,
      sslAnalyzer,
      contentAnalyzer,
    );
    this.ephiDetectionTest = new EPHIDetectionTest(nucleiClient, contentAnalyzer);
    this.auditControlsTest = new AuditControlsTest(nucleiClient, contentAnalyzer);
  }

  async runHipaaTestSuite(config: HipaaTestSuiteConfig): Promise<HipaaTestSuiteResult> {
    const startTime = Date.now();
    const allTestResults: (HipaaTestDetail | HipaaTestFailure)[] = [];

    console.log(`🔍 Starting HIPAA Security Test Suite for ${config.targetUrl}`);

    try {
      // Run Technical Safeguards Tests
      if (config.testCategories.includes('technical')) {
        console.log('Running Technical Safeguards tests...');

        // Access Control Tests
        const accessControlResults = await this.accessControlTest.runAccessControlTests(
          config.targetUrl,
          {
            protectedEndpoints: config.protectedEndpoints,
            publicEndpoints: config.publicEndpoints,
            timeout: config.timeout,
          },
        );
        allTestResults.push(...accessControlResults);

        // Authentication Tests
        const authenticationResults = await this.authenticationTest.runAuthenticationTests(
          config.targetUrl,
        );
        allTestResults.push(...authenticationResults);

        // Transmission Security Tests
        const transmissionResults =
          await this.transmissionSecurityTest.runTransmissionSecurityTests(config.targetUrl);
        allTestResults.push(...transmissionResults);

        // Audit Controls Tests
        const auditResults = await this.auditControlsTest.runAuditControlsTests(config.targetUrl);
        allTestResults.push(...auditResults);

        // ePHI Detection Tests
        const ephiResults = await this.ephiDetectionTest.runEPHIDetectionTests(
          config.targetUrl,
          config.pagesToScan,
        );
        allTestResults.push(...ephiResults);
      }

      // Calculate results
      const executionTime = Date.now() - startTime;
      const result = this.calculateTestSuiteResults(allTestResults, executionTime);

      console.log(`✅ HIPAA Test Suite completed in ${executionTime}ms`);
      console.log(`📊 Overall Score: ${result.overallScore}% (${result.riskLevel})`);
      console.log(`📈 Tests: ${result.passedTests}/${result.totalTests} passed`);

      return result;
    } catch (error) {
      console.error('❌ HIPAA Test Suite execution failed:', error);
      throw error;
    }
  }

  private calculateTestSuiteResults(
    allTestResults: (HipaaTestDetail | HipaaTestFailure)[],
    executionTime: number,
  ): HipaaTestSuiteResult {
    const totalTests = allTestResults.length;
    const passedTests = allTestResults.filter((test) => test.passed).length;
    const failedTests = totalTests - passedTests;

    // Calculate category results
    const technicalSafeguards = this.calculateCategoryResult('technical', allTestResults);
    const administrativeSafeguards = this.calculateCategoryResult('administrative', allTestResults);
    const organizationalSafeguards = this.calculateCategoryResult('organizational', allTestResults);
    const physicalSafeguards = this.calculateCategoryResult('physical', allTestResults);

    // Count issues by severity
    const failedTestsOnly = allTestResults.filter((test) => !test.passed) as HipaaTestFailure[];
    const criticalIssues = failedTestsOnly.filter((test) => test.riskLevel === 'critical').length;
    const highIssues = failedTestsOnly.filter((test) => test.riskLevel === 'high').length;
    const mediumIssues = failedTestsOnly.filter((test) => test.riskLevel === 'medium').length;
    const lowIssues = failedTestsOnly.filter((test) => test.riskLevel === 'low').length;

    // Calculate overall score
    const overallScore = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

    // Determine overall risk level
    const riskLevel: RiskLevel = this.calculateOverallRiskLevel(
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
    );

    return {
      overallScore,
      riskLevel,
      totalTests,
      passedTests,
      failedTests,
      technicalSafeguards,
      administrativeSafeguards,
      organizationalSafeguards,
      physicalSafeguards,
      allTestResults,
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
      executionTime,
      timestamp: new Date(),
    };
  }

  private calculateCategoryResult(
    category: HipaaCategory,
    allTestResults: (HipaaTestDetail | HipaaTestFailure)[],
  ): CategoryResult {
    const categoryTests = allTestResults.filter((test) => test.category === category);
    const totalTests = categoryTests.length;
    const passedTests = categoryTests.filter((test) => test.passed).length;
    const failedTests = totalTests - passedTests;

    const failedCategoryTests = categoryTests.filter((test) => !test.passed) as HipaaTestFailure[];
    const criticalIssues = failedCategoryTests.filter(
      (test) => test.riskLevel === 'critical',
    ).length;
    const highIssues = failedCategoryTests.filter((test) => test.riskLevel === 'high').length;
    const mediumIssues = failedCategoryTests.filter((test) => test.riskLevel === 'medium').length;
    const lowIssues = failedCategoryTests.filter((test) => test.riskLevel === 'low').length;

    const score = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 100;
    const riskLevel: RiskLevel = this.calculateOverallRiskLevel(
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
    );

    return {
      category,
      totalTests,
      passedTests,
      failedTests,
      score,
      riskLevel,
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
    };
  }

  private calculateOverallRiskLevel(
    criticalIssues: number,
    highIssues: number,
    mediumIssues: number,
    lowIssues: number,
  ): RiskLevel {
    if (criticalIssues > 0) return 'critical';
    if (highIssues > 0) return 'high';
    if (mediumIssues > 0) return 'medium';
    if (lowIssues > 0) return 'low';
    return 'low'; // No issues
  }

  // Helper method to create default test configuration
  static createDefaultConfig(targetUrl: string): HipaaTestSuiteConfig {
    return {
      targetUrl,
      protectedEndpoints: ['/admin', '/dashboard', '/account', '/api/users', '/api/patients'],
      publicEndpoints: ['/', '/about', '/contact', '/login'],
      pagesToScan: ['/', '/login', '/about', '/contact', '/dashboard'],
      timeout: HIPAA_SECURITY_CONSTANTS.TIMEOUTS.TOTAL_SCAN,
      enableAllTests: true,
      testCategories: ['technical', 'administrative', 'organizational'],
    };
  }
}
