import request from 'supertest';
import app from '../../index';

describe('HIPAA Security API', () => {
  describe('POST /api/v1/hipaa-security/scan', () => {
    it('should start a HIPAA security scan', async () => {
      const response = await request(app)
        .post('/api/v1/hipaa-security/scan')
        .send({
          targetUrl: 'https://example.com',
          maxPages: 5,
          scanDepth: 1,
          timeout: 60000,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.scanId).toBeDefined();
      expect(response.body.data.status).toBe('completed');
    }, 120000); // 2 minute timeout

    it('should validate request parameters', async () => {
      const response = await request(app)
        .post('/api/v1/hipaa-security/scan')
        .send({
          targetUrl: 'invalid-url',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });

    it('should reject URLs with invalid protocols', async () => {
      const response = await request(app)
        .post('/api/v1/hipaa-security/scan')
        .send({
          targetUrl: 'ftp://example.com',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate maxPages parameter', async () => {
      const response = await request(app)
        .post('/api/v1/hipaa-security/scan')
        .send({
          targetUrl: 'https://example.com',
          maxPages: 100, // Too high
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('GET /api/v1/hipaa-security/scan/:scanId/result', () => {
    it('should return 404 for non-existent scan', async () => {
      const fakeUuid = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .get(`/api/v1/hipaa-security/scan/${fakeUuid}/result`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Scan result not found');
    });

    it('should validate UUID format', async () => {
      const response = await request(app)
        .get('/api/v1/hipaa-security/scan/invalid-uuid/result')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('GET /api/v1/hipaa-security/scan/:scanId/status', () => {
    it('should return 404 for non-existent scan', async () => {
      const fakeUuid = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .get(`/api/v1/hipaa-security/scan/${fakeUuid}/status`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Scan not found');
    });
  });

  describe('GET /api/v1/hipaa-security/scans', () => {
    it('should return list of scans', async () => {
      const response = await request(app).get('/api/v1/hipaa-security/scans').expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.metadata).toBeDefined();
      expect(response.body.metadata.count).toBeDefined();
      expect(response.body.metadata.limit).toBeDefined();
    });

    it('should respect limit parameter', async () => {
      const response = await request(app).get('/api/v1/hipaa-security/scans?limit=10').expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.metadata.limit).toBe(10);
    });

    it('should validate limit parameter', async () => {
      const response = await request(app)
        .get('/api/v1/hipaa-security/scans?limit=200') // Too high
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('DELETE /api/v1/hipaa-security/scan/:scanId', () => {
    it('should return 404 for non-existent scan', async () => {
      const fakeUuid = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .delete(`/api/v1/hipaa-security/scan/${fakeUuid}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Scan not found');
    });
  });

  describe('GET /api/v1/hipaa-security/scan/:scanId/export', () => {
    it('should return 404 for non-existent scan', async () => {
      const fakeUuid = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .get(`/api/v1/hipaa-security/scan/${fakeUuid}/export`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Scan result not found');
    });

    it('should validate format parameter', async () => {
      const fakeUuid = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .get(`/api/v1/hipaa-security/scan/${fakeUuid}/export?format=invalid`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });
});
