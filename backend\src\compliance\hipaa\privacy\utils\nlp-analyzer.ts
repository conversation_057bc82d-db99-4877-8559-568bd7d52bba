// backend/src/compliance/hipaa/privacy/utils/nlp-analyzer.ts

import nlp from 'compromise';
import {
  Level2Result,
  ExtractedEntities,
  PrivacyStatement,
  RightsStatement,
  Level2Finding,
} from '../types';
import { NLP_ANALYSIS_CONFIG } from '../constants';

// Basic types for Compromise.js to avoid 'any'
interface CompromiseBase {
  // Base for objects that can output text
  text: () => string;
}

interface CompromiseOutput<T> {
  out: (type: 'text' | 'array' | 'json' | 'terms') => T;
}

// For results of .people(), .organizations(), .places(), .phoneNumbers(), .emails(), .match()
interface CompromiseTermCollection extends CompromiseBase, CompromiseOutput<string[]> {
  map: <U>(callback: (item: CompromiseBase, index: number) => U) => U[];
  forEach: (callback: (item: CompromiseBase, index: number) => void) => void;
}

// For results of .sentences()
interface CompromiseSentence extends CompromiseBase {
  has: (keywords: string[] | string) => boolean;
}

interface CompromiseSentenceCollection
  extends CompromiseBase,
    CompromiseOutput<CompromiseSentence[]> {
  map: <U>(callback: (sentence: CompromiseSentence, index: number) => U) => U[];
  forEach: (callback: (sentence: CompromiseSentence, index: number) => void) => void;
}

// The main document object returned by nlp()
interface CompromiseDocument extends CompromiseBase {
  sentences: () => CompromiseSentenceCollection;
  people: () => CompromiseTermCollection;
  organizations: () => CompromiseTermCollection;
  phoneNumbers: () => CompromiseTermCollection;
  emails: () => CompromiseTermCollection;
  places: () => CompromiseTermCollection; // Added
  match: (pattern: string) => CompromiseTermCollection;
}

/**
 * NLP analysis utilities for Level 2 HIPAA analysis
 * Uses Compromise.js for natural language understanding and context analysis
 */
export class NLPAnalyzer {
  /**
   * Performs comprehensive Level 2 NLP analysis using Compromise.js
   * Understands context, entities, and meaning beyond exact phrase matching
   */
  static async analyzeWithCompromise(text: string): Promise<Level2Result> {
    console.log('🧠 [Level 2 Analysis] Starting NLP analysis with Compromise.js');
    console.log('📊 [Level 2 Analysis] Text length:', text.length);
    console.log('📝 [Level 2 Analysis] Text preview:', text.substring(0, 200) + '...');

    const startTime = Date.now();

    try {
      // Ensure minimum processing time for very fast operations
      await new Promise((resolve) => setTimeout(resolve, 5));

      // Parse text with Compromise.js
      console.log('🔍 [Level 2 Analysis] Parsing text with Compromise.js...');
      const doc = nlp(text) as CompromiseDocument;
      console.log('✅ [Level 2 Analysis] Text parsed successfully');

      // Extract entities
      console.log('👥 [Level 2 Analysis] Extracting entities...');
      const entities = this.extractEntities(doc);
      console.log('✅ [Level 2 Analysis] Entity extraction complete:', {
        people: entities.people.length,
        organizations: entities.organizations.length,
        phoneNumbers: entities.phoneNumbers.length,
        emails: entities.emails.length,
      });

      // Find privacy-related statements
      console.log('🔒 [Level 2 Analysis] Finding privacy statements...');
      const privacyStatements = this.findPrivacyStatements(doc);
      console.log('✅ [Level 2 Analysis] Privacy statement analysis complete:', {
        statementsFound: privacyStatements.length,
        privacyScore:
          privacyStatements.reduce((sum, stmt) => sum + stmt.confidence, 0) /
            privacyStatements.length || 0,
      });

      // Find rights-related statements
      console.log('⚖️ [Level 2 Analysis] Finding rights statements...');
      const rightsStatements = this.findRightsStatements(doc);
      console.log('✅ [Level 2 Analysis] Rights statement analysis complete:', {
        statementsFound: rightsStatements.length,
        rightsScore:
          rightsStatements.reduce((sum, stmt) => sum + stmt.confidence, 0) /
            rightsStatements.length || 0,
      });

      // Generate findings
      console.log('📋 [Level 2 Analysis] Generating findings...');
      const findings = this.generateLevel2Findings(
        doc,
        entities,
        privacyStatements,
        rightsStatements,
      );

      console.log('✅ [Level 2 Analysis] Findings generation complete:', {
        findingsCount: findings.length,
        findingTypes: findings.map((f) => f.type),
      });

      // Calculate compliance score
      console.log('📊 [Level 2 Analysis] Calculating compliance score...');
      const score = this.calculateNLPComplianceScore(privacyStatements, rightsStatements, entities);

      // Calculate confidence based on analysis quality
      const confidence = this.calculateNLPConfidence(doc, findings);
      const processingTime = Date.now() - startTime;

      console.log('🎯 [Level 2 Analysis] NLP analysis complete:', {
        score: score,
        confidence: confidence,
        processingTime: processingTime + 'ms',
        entitiesSummary: {
          people: entities.people.length,
          organizations: entities.organizations.length,
          phoneNumbers: entities.phoneNumbers.length,
          emails: entities.emails.length,
        },
        statementsSummary: {
          privacyStatements: privacyStatements.length,
          rightsStatements: rightsStatements.length,
        },
        findingsSummary: {
          total: findings.length,
          types: Array.from(new Set(findings.map((f: Level2Finding) => f.type))),
        },
      });

      return {
        level: 2,
        method: 'NLP with Compromise.js',
        score,
        confidence,
        entities,
        privacyStatements,
        rightsStatements,
        findings,
        processingTime,
      };
    } catch (error) {
      console.error('NLP analysis failed:', error);

      // Return minimal result on error
      return {
        level: 2,
        method: 'NLP with Compromise.js',
        score: 0,
        confidence: 0,
        entities: this.getEmptyEntities(),
        privacyStatements: [],
        rightsStatements: [],
        findings: [
          {
            type: 'context_match',
            content: '',
            context: '',
            confidence: 0,
            interpretation: `NLP analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          },
        ],
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Extracts entities (people, organizations, dates, etc.) from the document
   */
  private static extractEntities(doc: CompromiseDocument): ExtractedEntities {
    const config = NLP_ANALYSIS_CONFIG.ENTITY_EXTRACTION;

    return {
      people: config.extractPeople ? doc.people().out('array') : [],
      organizations: config.extractOrganizations ? doc.organizations().out('array') : [],
      dates: config.extractDates ? this.extractDates(doc) : [],
      places: config.extractPlaces ? doc.places().out('array') : [],
      phoneNumbers: this.extractPhoneNumbers(doc),
      emails: this.extractEmails(doc),
    };
  }

  /**
   * Finds privacy-related statements using NLP understanding
   */
  private static findPrivacyStatements(doc: CompromiseDocument): PrivacyStatement[] {
    const statements: PrivacyStatement[] = [];
    const concepts = NLP_ANALYSIS_CONFIG.PRIVACY_CONCEPTS;

    // Get all sentences and convert to text array
    const sentences = doc.sentences();
    const sentenceTexts: string[] = [];

    // Extract text from each sentence
    sentences.forEach((sentence: CompromiseSentence) => {
      sentenceTexts.push(sentence.text());
    });

    // Find protection statements
    const protectionSentences = sentenceTexts.filter((sentenceText: string) => {
      const text = sentenceText.toLowerCase();
      return concepts.protection.some((word) => text.includes(word));
    });

    protectionSentences.forEach((sentenceText: string) => {
      statements.push({
        text: sentenceText,
        type: 'protection',
        confidence: this.calculateStatementConfidenceFromText(sentenceText, [
          ...concepts.protection,
        ]),
        location: this.findTextPosition(doc.text(), sentenceText),
      });
    });

    // Find disclosure statements
    const disclosureSentences = sentenceTexts.filter((sentenceText: string) => {
      const text = sentenceText.toLowerCase();
      return concepts.disclosure.some((word) => text.includes(word));
    });

    disclosureSentences.forEach((sentenceText: string) => {
      statements.push({
        text: sentenceText,
        type: 'disclosure',
        confidence: this.calculateStatementConfidenceFromText(sentenceText, [
          ...concepts.disclosure,
        ]),
        location: this.findTextPosition(doc.text(), sentenceText),
      });
    });

    // Find usage statements
    const usageSentences = sentenceTexts.filter((sentenceText: string) => {
      const text = sentenceText.toLowerCase();
      return concepts.usage.some((word) => text.includes(word));
    });

    usageSentences.forEach((sentenceText: string) => {
      statements.push({
        text: sentenceText,
        type: 'usage',
        confidence: this.calculateStatementConfidenceFromText(sentenceText, [...concepts.usage]),
        location: this.findTextPosition(doc.text(), sentenceText),
      });
    });

    return statements;
  }

  /**
   * Finds rights-related statements using NLP understanding
   */
  private static findRightsStatements(doc: CompromiseDocument): RightsStatement[] {
    const statements: RightsStatement[] = [];

    // Define right types and their indicators
    const rightTypes = {
      access: ['access', 'inspect', 'view', 'see', 'review'],
      amendment: ['amend', 'correct', 'change', 'modify', 'update'],
      restriction: ['restrict', 'limit', 'control', 'prevent'],
      accounting: ['accounting', 'disclosure', 'list', 'record'],
      copy: ['copy', 'obtain', 'receive', 'get'],
    };

    // Get all sentences and convert to text array
    const sentences = doc.sentences();
    const sentenceTexts: string[] = [];

    // Extract text from each sentence
    sentences.forEach((sentence: CompromiseSentence) => {
      sentenceTexts.push(sentence.text());
    });

    for (const [rightType, indicators] of Object.entries(rightTypes)) {
      const rightSentences = sentenceTexts.filter((sentenceText: string) => {
        const text = sentenceText.toLowerCase();
        return indicators.some((indicator) => text.includes(indicator)) && text.includes('right');
      });

      rightSentences.forEach((sentenceText: string) => {
        statements.push({
          text: sentenceText,
          rightType: rightType as 'access' | 'amendment' | 'restriction' | 'accounting' | 'copy',
          confidence: this.calculateStatementConfidenceFromText(sentenceText, indicators),
          location: this.findTextPosition(doc.text(), sentenceText),
        });
      });
    }

    return statements;
  }

  /**
   * Generates Level 2 findings based on NLP analysis
   */
  private static generateLevel2Findings(
    doc: CompromiseDocument,
    entities: ExtractedEntities,
    privacyStatements: PrivacyStatement[],
    rightsStatements: RightsStatement[],
  ): Level2Finding[] {
    const findings: Level2Finding[] = [];

    // Entity findings
    if (entities.people.length > 0) {
      findings.push({
        type: 'entity_found',
        content: entities.people.join(', '),
        context: 'Contact persons identified',
        confidence: 90,
        interpretation: 'Found contact person names in the policy',
        entities: entities.people,
      });
    }

    if (entities.organizations.length > 0) {
      findings.push({
        type: 'entity_found',
        content: entities.organizations.join(', '),
        context: 'Organizations identified',
        confidence: 85,
        interpretation: 'Found organization names that may be involved in data sharing',
        entities: entities.organizations,
      });
    }

    // Privacy concept findings
    const protectionStatements = privacyStatements.filter((s) => s.type === 'protection');
    if (protectionStatements.length > 0) {
      findings.push({
        type: 'concept_identified',
        content: protectionStatements[0].text,
        context: 'Privacy protection measures',
        confidence: protectionStatements[0].confidence,
        interpretation: 'Policy describes how patient information is protected',
      });
    }

    const disclosureStatements = privacyStatements.filter((s) => s.type === 'disclosure');
    if (disclosureStatements.length > 0) {
      findings.push({
        type: 'concept_identified',
        content: disclosureStatements[0].text,
        context: 'Information disclosure practices',
        confidence: disclosureStatements[0].confidence,
        interpretation: 'Policy explains when and how information may be disclosed',
      });
    }

    // Rights findings
    if (rightsStatements.length > 0) {
      const rightTypes = Array.from(new Set(rightsStatements.map((s) => s.rightType)));
      findings.push({
        type: 'concept_identified',
        content: `Patient rights: ${rightTypes.join(', ')}`,
        context: 'Individual rights under HIPAA',
        confidence: Math.max(...rightsStatements.map((s) => s.confidence)),
        interpretation: `Policy describes ${rightTypes.length} types of patient rights`,
      });
    }

    return findings;
  }

  /**
   * Calculates NLP-based compliance score
   */
  private static calculateNLPComplianceScore(
    privacyStatements: PrivacyStatement[],
    rightsStatements: RightsStatement[],
    entities: ExtractedEntities,
  ): number {
    let score = 0;

    // Points for privacy statements (enhanced scoring)
    const protectionStatements = privacyStatements.filter((s) => s.type === 'protection');
    const disclosureStatements = privacyStatements.filter((s) => s.type === 'disclosure');
    const usageStatements = privacyStatements.filter((s) => s.type === 'usage');

    // Base points for statement types
    if (protectionStatements.length > 0) score += 25;
    if (disclosureStatements.length > 0) score += 25;
    if (usageStatements.length > 0) score += 20;

    // Bonus points for comprehensive content
    const totalStatements =
      protectionStatements.length + disclosureStatements.length + usageStatements.length;
    if (totalStatements >= 5) score += 10; // Comprehensive coverage bonus

    // Points for rights statements (enhanced)
    const rightTypes = Array.from(new Set(rightsStatements.map((s) => s.rightType)));
    score += Math.min(rightTypes.length * 5, 20); // Max 20 points for rights

    // Bonus for comprehensive rights coverage
    if (rightTypes.length >= 4) score += 5;

    // Points for entities (enhanced)
    if (entities.people.length > 0) score += 5; // Contact person
    if (entities.organizations.length > 0) score += 5; // Organizations mentioned
    if (entities.phoneNumbers.length > 0) score += 3; // Contact methods
    if (entities.emails.length > 0) score += 3; // Email contacts

    // Bonus for multiple contact methods
    const contactMethods = entities.phoneNumbers.length + entities.emails.length;
    if (contactMethods >= 2) score += 5;

    return Math.min(score, 100);
  }

  /**
   * Calculates confidence in NLP analysis
   */
  private static calculateNLPConfidence(
    doc: CompromiseDocument,
    findings: Level2Finding[],
  ): number {
    const textLength = doc.text().length;

    // Get sentence count by counting sentences
    const sentences = doc.sentences();
    let sentenceCount = 0;
    sentences.forEach(() => {
      sentenceCount++;
    });

    let confidence = 50; // Base confidence

    // Higher confidence for longer, more structured text
    if (textLength > 1000) confidence += 20;
    if (sentenceCount > 10) confidence += 10;

    // Higher confidence for more findings
    confidence += Math.min(findings.length * 5, 20);

    return Math.min(confidence, 100);
  }

  /**
   * Helper methods
   */
  private static extractPhoneNumbers(doc: CompromiseDocument): string[] {
    // Enhanced phone number regex to match various formats including test patterns
    const phoneRegex = /(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const text = doc.text();
    const matches = [];
    let match;

    // Reset regex lastIndex to ensure proper matching
    phoneRegex.lastIndex = 0;

    while ((match = phoneRegex.exec(text)) !== null) {
      matches.push(match[0].trim());
      // Prevent infinite loop
      if (phoneRegex.lastIndex === match.index) {
        phoneRegex.lastIndex++;
      }
    }

    return matches;
  }

  private static extractEmails(doc: CompromiseDocument): string[] {
    // Enhanced email regex for better matching
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/g;
    const text = doc.text();
    const matches = [];
    let match;

    // Reset regex lastIndex to ensure proper matching
    emailRegex.lastIndex = 0;

    while ((match = emailRegex.exec(text)) !== null) {
      matches.push(match[0].toLowerCase());
      // Prevent infinite loop
      if (emailRegex.lastIndex === match.index) {
        emailRegex.lastIndex++;
      }
    }

    return matches;
  }

  private static extractDates(doc: CompromiseDocument): string[] {
    // Use regex to find date patterns since doc.dates() doesn't exist
    const text = doc.text();
    const datePatterns = [
      /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, // MM/DD/YYYY
      /\b\d{4}-\d{1,2}-\d{1,2}\b/g, // YYYY-MM-DD
      /\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/gi, // Month DD, YYYY
      /\b\d{1,2}\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b/gi, // DD Month YYYY
    ];

    const matches = [];
    for (const pattern of datePatterns) {
      pattern.lastIndex = 0;
      let match;
      while ((match = pattern.exec(text)) !== null) {
        matches.push(match[0]);
        // Prevent infinite loop
        if (pattern.lastIndex === match.index) {
          pattern.lastIndex++;
        }
      }
    }

    return matches;
  }

  private static findTextPosition(fullText: string, searchText: string): number {
    return fullText.indexOf(searchText);
  }

  private static calculateStatementConfidence(
    sentence: CompromiseSentence,
    keywords: string[],
  ): number {
    const text = sentence.text().toLowerCase();
    const matchCount = keywords.filter((keyword) => text.includes(keyword)).length;
    return Math.min(60 + matchCount * 10, 95);
  }

  private static calculateStatementConfidenceFromText(text: string, keywords: string[]): number {
    const lowerText = text.toLowerCase();
    const matchCount = keywords.filter((keyword) => lowerText.includes(keyword)).length;
    return Math.min(60 + matchCount * 10, 95);
  }

  private static getEmptyEntities(): ExtractedEntities {
    return {
      people: [],
      organizations: [],
      dates: [],
      places: [],
      phoneNumbers: [],
      emails: [],
    };
  }
}
