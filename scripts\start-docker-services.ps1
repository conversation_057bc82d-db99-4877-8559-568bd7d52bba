#!/usr/bin/env pwsh
# PowerShell script to start Docker services for Comply Checker

Write-Host "🐳 Starting Comply Checker Docker Services..." -ForegroundColor Cyan

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to wait for service health
function Wait-ForServiceHealth {
    param(
        [string]$ServiceName,
        [int]$MaxWaitSeconds = 120
    )
    
    Write-Host "⏳ Waiting for $ServiceName to be healthy..." -ForegroundColor Yellow
    
    $elapsed = 0
    while ($elapsed -lt $MaxWaitSeconds) {
        $health = docker inspect --format='{{.State.Health.Status}}' "comply_checker_$ServiceName" 2>$null
        
        if ($health -eq "healthy") {
            Write-Host "✅ $ServiceName is healthy!" -ForegroundColor Green
            return $true
        }
        
        Start-Sleep -Seconds 5
        $elapsed += 5
        Write-Host "." -NoNewline -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "⚠️ $ServiceName did not become healthy within $MaxWaitSeconds seconds" -ForegroundColor Red
    return $false
}

# Check if Docker is running
if (-not (Test-DockerRunning)) {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

# Remove any orphaned containers
Write-Host "🧹 Cleaning up orphaned containers..." -ForegroundColor Yellow
docker-compose down --remove-orphans

# Pull latest images
Write-Host "📥 Pulling latest images..." -ForegroundColor Yellow
docker-compose pull

# Start services in order
Write-Host "🚀 Starting core services..." -ForegroundColor Cyan

# Start PostgreSQL first
Write-Host "📊 Starting PostgreSQL..." -ForegroundColor Blue
docker-compose up -d postgres
Wait-ForServiceHealth "postgres" 60

# ZAP removed - using Nuclei for security scanning

# Start Keycloak
Write-Host "🔐 Starting Keycloak..." -ForegroundColor Blue
docker-compose up -d keycloak
Wait-ForServiceHealth "keycloak" 120

# Start MailHog
Write-Host "📧 Starting MailHog..." -ForegroundColor Blue
docker-compose up -d mailhog

# Finally start the backend
Write-Host "🖥️ Starting Backend..." -ForegroundColor Blue
docker-compose up -d backend

# Wait a bit for backend to start
Start-Sleep -Seconds 10

# Show status
Write-Host ""
Write-Host "📋 Service Status:" -ForegroundColor Cyan
docker-compose ps

Write-Host ""
Write-Host "🌐 Service URLs:" -ForegroundColor Cyan
Write-Host "  Backend API:     http://localhost:3001" -ForegroundColor White
Write-Host "  Keycloak:        http://localhost:8080/auth" -ForegroundColor White
Write-Host "  MailHog:         http://localhost:8025" -ForegroundColor White
Write-Host "  PostgreSQL:      localhost:5432" -ForegroundColor White

Write-Host ""
Write-Host "✅ Docker services started successfully!" -ForegroundColor Green
Write-Host "💡 You can now run the frontend separately with: npm run dev" -ForegroundColor Yellow
Write-Host "💡 To stop all services: docker-compose down" -ForegroundColor Yellow
