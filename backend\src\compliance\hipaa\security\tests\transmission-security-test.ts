import { NucleiClient } from '../services/nuclei-client';
import { SSLAnalyzer } from '../services/ssl-analyzer';
import { ContentAnalyzer } from '../services/content-analyzer';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export class TransmissionSecurityTest {
  private nucleiClient: NucleiClient;
  private sslAnalyzer: SSLAnalyzer;
  private contentAnalyzer: ContentAnalyzer;

  constructor(
    nucleiClient: NucleiClient,
    sslAnalyzer: SSLAnalyzer,
    contentAnalyzer: ContentAnalyzer,
  ) {
    this.nucleiClient = nucleiClient;
    this.sslAnalyzer = sslAnalyzer;
    this.contentAnalyzer = contentAnalyzer;
  }

  async runTransmissionSecurityTests(
    targetUrl: string,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    // Test 1: SSL/TLS Configuration
    results.push(await this.testSSLTLSConfiguration(targetUrl));

    // Test 2: HTTPS Enforcement
    results.push(await this.testHTTPSEnforcement(targetUrl));

    // Test 3: Security Headers
    results.push(await this.testSecurityHeaders(targetUrl));

    // Test 4: Encryption in Transit
    results.push(await this.testEncryptionInTransit(targetUrl));

    return results;
  }

  private async testSSLTLSConfiguration(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'transmission-security-ssl-tls';
    const testName = 'SSL/TLS Configuration';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY;

    try {
      const url = new URL(targetUrl);
      const hostname = url.hostname;
      const port = url.port ? parseInt(url.port) : 443;

      const sslAnalysis = await this.sslAnalyzer.analyzeDomain(hostname, port);
      const sslIssues: FailureEvidence[] = [];

      // Check HIPAA compliance
      if (!sslAnalysis.hipaaCompliant) {
        // Check specific issues
        if (!sslAnalysis.isValid) {
          sslIssues.push({
            location: hostname,
            elementType: 'response',
            actualCode: `Certificate Status: Invalid`,
            expectedBehavior: 'SSL certificate should be valid',
            context: 'Invalid SSL certificate detected',
          });
        }

        if (sslAnalysis.daysRemaining < 30) {
          sslIssues.push({
            location: hostname,
            elementType: 'response',
            actualCode: `Certificate expires in ${sslAnalysis.daysRemaining} days`,
            expectedBehavior: 'Certificate should have at least 30 days before expiration',
            context: 'SSL certificate expiring soon',
          });
        }

        if (!sslAnalysis.tlsVersion.includes('1.2') && !sslAnalysis.tlsVersion.includes('1.3')) {
          sslIssues.push({
            location: hostname,
            elementType: 'response',
            actualCode: `TLS Version: ${sslAnalysis.tlsVersion}`,
            expectedBehavior: 'Should use TLS 1.2 or higher',
            context: 'Weak TLS version detected',
          });
        }

        // Add vulnerability issues
        sslAnalysis.vulnerabilities.forEach((vuln) => {
          if (vuln.severity === 'critical' || vuln.severity === 'high') {
            sslIssues.push({
              location: hostname,
              elementType: 'response',
              actualCode: vuln.description,
              expectedBehavior: vuln.remediation,
              context: `SSL Vulnerability: ${vuln.type}`,
            });
          }
        });
      }

      if (sslIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify SSL/TLS configuration meets HIPAA requirements',
          category: 'technical',
          passed: false,
          failureReason: `${sslIssues.length} SSL/TLS configuration issues found`,
          riskLevel: sslAnalysis.vulnerabilities.some((v) => v.severity === 'critical')
            ? 'critical'
            : 'high',
          failureEvidence: sslIssues,
          recommendedAction: 'Update SSL/TLS configuration to meet HIPAA requirements',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify SSL/TLS configuration meets HIPAA requirements',
        category: 'technical',
        passed: true,
        evidence: `SSL/TLS configuration is HIPAA compliant. Grade: ${sslAnalysis.grade}, TLS: ${sslAnalysis.tlsVersion}`,
        pagesTested: [hostname],
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify SSL/TLS configuration meets HIPAA requirements',
        category: 'technical',
        passed: false,
        failureReason: `SSL/TLS analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'high',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'SSL/TLS analysis should complete successfully',
            context: 'SSL/TLS analysis error',
          },
        ],
        recommendedAction: 'Review SSL/TLS configuration and ensure proper certificate setup',
        remediationPriority: 1,
        timestamp: new Date(),
      };
    }
  }

  private async testHTTPSEnforcement(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'transmission-security-https-enforcement';
    const testName = 'HTTPS Enforcement';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY;

    try {
      const url = new URL(targetUrl);
      const httpUrl = `http://${url.hostname}${url.pathname}`;

      const httpsIssues: FailureEvidence[] = [];

      try {
        const response = await this.nucleiClient.fetchUrlContent(httpUrl);

        // Check if HTTP redirects to HTTPS
        const redirectsToHTTPS = response.statusCode === 301 || response.statusCode === 302;
        const locationHeader = response.responseHeaders.location;
        const redirectsToSecure = locationHeader?.startsWith('https://');

        if (!redirectsToHTTPS || !redirectsToSecure) {
          httpsIssues.push({
            location: httpUrl,
            elementType: 'response',
            actualCode: `HTTP ${response.statusCode}, Location: ${locationHeader || 'none'}`,
            expectedBehavior: 'HTTP requests should redirect to HTTPS (301/302 to https://)',
            context: 'HTTP traffic not properly redirected to HTTPS',
          });
        }
      } catch (error) {
        // If HTTP connection fails, it might be properly blocked
        // This is actually good for security
      }

      // Check HSTS header on HTTPS
      const httpsResponse = await this.nucleiClient.fetchUrlContent(targetUrl);
      const hstsHeader = httpsResponse.responseHeaders['strict-transport-security'];

      if (!hstsHeader) {
        httpsIssues.push({
          location: targetUrl,
          elementType: 'header',
          actualCode: 'Missing Strict-Transport-Security header',
          expectedBehavior:
            'Should include HSTS header: Strict-Transport-Security: max-age=31536000',
          context: 'HSTS header missing - browsers may allow HTTP connections',
        });
      } else {
        // Check HSTS configuration
        const maxAge = hstsHeader.match(/max-age=(\d+)/)?.[1];
        if (!maxAge || parseInt(maxAge) < 31536000) {
          httpsIssues.push({
            location: targetUrl,
            elementType: 'header',
            actualCode: `Strict-Transport-Security: ${hstsHeader}`,
            expectedBehavior: 'HSTS max-age should be at least 31536000 (1 year)',
            context: 'HSTS max-age too short',
          });
        }
      }

      if (httpsIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify HTTPS enforcement and HSTS implementation',
          category: 'technical',
          passed: false,
          failureReason: `${httpsIssues.length} HTTPS enforcement issues found`,
          riskLevel: 'high',
          failureEvidence: httpsIssues,
          recommendedAction: 'Implement proper HTTPS redirection and HSTS headers',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify HTTPS enforcement and HSTS implementation',
        category: 'technical',
        passed: true,
        evidence: 'HTTPS properly enforced with HSTS header',
        pagesTested: [targetUrl],
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify HTTPS enforcement and HSTS implementation',
        category: 'technical',
        passed: false,
        failureReason: `HTTPS enforcement test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'HTTPS enforcement test should complete successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review HTTPS configuration and accessibility',
        remediationPriority: 2,
        timestamp: new Date(),
      };
    }
  }

  private async testSecurityHeaders(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'transmission-security-headers';
    const testName = 'Security Headers';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY;

    try {
      const response = await this.nucleiClient.fetchUrlContent(targetUrl);
      const contentAnalysis = this.contentAnalyzer.analyzeContent(
        response.body,
        targetUrl,
        response.responseHeaders,
      );

      const headerIssues: FailureEvidence[] = [];
      const secureHeaders: string[] = [];

      contentAnalysis.securityHeaders.forEach((header) => {
        if (!header.present) {
          headerIssues.push({
            location: targetUrl,
            elementType: 'header',
            actualCode: `Missing header: ${header.header}`,
            expectedBehavior: header.recommendation || `${header.header} header should be present`,
            context: `Security header ${header.header} is missing`,
          });
        } else if (!header.secure) {
          headerIssues.push({
            location: targetUrl,
            elementType: 'header',
            actualCode: `${header.header}: ${header.value}`,
            expectedBehavior:
              header.recommendation || `${header.header} should have secure configuration`,
            context: `Security header ${header.header} has insecure configuration`,
          });
        } else {
          secureHeaders.push(header.header);
        }
      });

      if (headerIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify security headers are properly configured',
          category: 'technical',
          passed: false,
          failureReason: `${headerIssues.length} security header issues found`,
          riskLevel: 'medium',
          failureEvidence: headerIssues,
          recommendedAction: 'Configure missing security headers and fix insecure configurations',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify security headers are properly configured',
        category: 'technical',
        passed: true,
        evidence: `${secureHeaders.length} security headers properly configured`,
        pagesTested: [targetUrl],
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify security headers are properly configured',
        category: 'technical',
        passed: false,
        failureReason: `Security headers test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Security headers test should complete successfully',
            context: 'Security headers test error',
          },
        ],
        recommendedAction: 'Review server configuration and ensure proper header setup',
        remediationPriority: 2,
        timestamp: new Date(),
      };
    }
  }

  private async testEncryptionInTransit(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'transmission-security-encryption';
    const testName = 'Encryption in Transit';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ENCRYPTION;

    try {
      const url = new URL(targetUrl);
      const encryptionIssues: FailureEvidence[] = [];
      const secureConnections: string[] = [];

      // Check if using HTTPS
      if (url.protocol !== 'https:') {
        encryptionIssues.push({
          location: targetUrl,
          elementType: 'response',
          actualCode: `Protocol: ${url.protocol}`,
          expectedBehavior: 'Should use HTTPS protocol',
          context: 'Unencrypted HTTP connection detected',
        });
      } else {
        secureConnections.push('HTTPS');
      }

      // Test common pages for HTTPS enforcement
      const testPages = ['/', '/login', '/api/health'];
      for (const page of testPages) {
        try {
          const httpUrl = targetUrl.replace('https://', 'http://') + page;
          const response = await this.nucleiClient.fetchUrlContent(httpUrl);

          // Check if HTTP redirects to HTTPS
          const redirectsToHttps =
            response.statusCode === 301 ||
            response.statusCode === 302 ||
            response.responseHeaders.location?.startsWith('https://');

          if (!redirectsToHttps && response.statusCode === 200) {
            encryptionIssues.push({
              location: httpUrl,
              elementType: 'response',
              actualCode: `HTTP ${response.statusCode} - Content served over HTTP`,
              expectedBehavior: 'Should redirect to HTTPS or return error',
              context: 'HTTP connection allowed without HTTPS redirect',
            });
          } else {
            secureConnections.push(`${page} (HTTPS enforced)`);
          }
        } catch (error) {
          // HTTP connection refused is good - means HTTPS is enforced
          secureConnections.push(`${page} (HTTP blocked)`);
        }
      }

      if (encryptionIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify encryption in transit is properly implemented',
          category: 'technical',
          passed: false,
          failureReason: `${encryptionIssues.length} encryption in transit issues found`,
          riskLevel: 'high',
          failureEvidence: encryptionIssues,
          recommendedAction: 'Enforce HTTPS for all connections and redirect HTTP to HTTPS',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify encryption in transit is properly implemented',
        category: 'technical',
        passed: true,
        evidence: `Encryption in transit properly configured: ${secureConnections.join(', ')}`,
        pagesTested: testPages,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify encryption in transit is properly implemented',
        category: 'technical',
        passed: false,
        failureReason: `Encryption test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'high',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Encryption test should complete successfully',
            context: 'Encryption test error',
          },
        ],
        recommendedAction: 'Review HTTPS configuration and ensure proper encryption setup',
        remediationPriority: 1,
        timestamp: new Date(),
      };
    }
  }

  private validateHeaderValue(headerName: string, value: string): boolean {
    switch (headerName) {
      case 'strict-transport-security':
        return (
          value.includes('max-age=') &&
          parseInt(value.match(/max-age=(\d+)/)?.[1] || '0') >= 31536000
        );
      case 'content-security-policy':
        return !value.includes("'unsafe-inline'") && !value.includes("'unsafe-eval'");
      case 'x-frame-options':
        return value.toLowerCase() === 'deny' || value.toLowerCase() === 'sameorigin';
      case 'x-content-type-options':
        return value.toLowerCase() === 'nosniff';
      case 'x-xss-protection':
        return value === '1; mode=block';
      default:
        return true;
    }
  }

  private getHeaderRecommendation(headerName: string): string {
    const recommendations: Record<string, string> = {
      'strict-transport-security': 'Strict-Transport-Security: max-age=31536000; includeSubDomains',
      'content-security-policy': "Content-Security-Policy: default-src 'self'",
      'x-frame-options': 'X-Frame-Options: DENY',
      'x-content-type-options': 'X-Content-Type-Options: nosniff',
      'x-xss-protection': 'X-XSS-Protection: 1; mode=block',
    };

    return recommendations[headerName] || `Configure ${headerName} header properly`;
  }
}
