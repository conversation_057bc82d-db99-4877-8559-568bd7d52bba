#!/bin/bash
# <PERSON>ript to install Nuclei vulnerability scanner
# This script downloads and installs Nuclei for Linux/macOS

set -e

echo "🔍 Installing Nuclei Vulnerability Scanner..."

# Create tools directory if it doesn't exist
TOOLS_DIR="./tools"
mkdir -p "$TOOLS_DIR"

# Set Nuclei installation path
NUCLEI_DIR="$TOOLS_DIR/nuclei"
NUCLEI_BIN="$NUCLEI_DIR/nuclei"

# Detect OS and architecture
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

case $ARCH in
    x86_64)
        ARCH="amd64"
        ;;
    arm64|aarch64)
        ARCH="arm64"
        ;;
    *)
        echo "❌ Unsupported architecture: $ARCH"
        exit 1
        ;;
esac

# Check if Nuclei is already installed
if [ -f "$NUCLEI_BIN" ]; then
    echo "✅ Nuclei is already installed at: $NUCLEI_BIN"
    
    # Check version
    if VERSION=$("$NUCLEI_BIN" -version 2>/dev/null); then
        echo "📋 Current version: $VERSION"
    else
        echo "⚠️ Could not determine Nuclei version"
    fi
    
    read -p "Do you want to update to the latest version? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 Using existing Nuclei installation"
        exit 0
    fi
fi

# Create Nuclei directory
mkdir -p "$NUCLEI_DIR"

echo "📥 Downloading latest Nuclei release..."

# Get latest release info from GitHub API
API_URL="https://api.github.com/repos/projectdiscovery/nuclei/releases/latest"
RELEASE_INFO=$(curl -s "$API_URL")

# Extract tag name
TAG_NAME=$(echo "$RELEASE_INFO" | grep '"tag_name":' | sed -E 's/.*"tag_name": "([^"]+)".*/\1/')

if [ -z "$TAG_NAME" ]; then
    echo "❌ Could not determine latest release version"
    exit 1
fi

echo "📋 Found release: $TAG_NAME"

# Construct download URL
ASSET_NAME="nuclei_${TAG_NAME#v}_${OS}_${ARCH}.zip"
DOWNLOAD_URL="https://github.com/projectdiscovery/nuclei/releases/download/$TAG_NAME/$ASSET_NAME"

echo "📋 Asset: $ASSET_NAME"
echo "⬇️ Downloading from: $DOWNLOAD_URL"

# Download the release
ZIP_PATH="$TOOLS_DIR/nuclei.zip"
if ! curl -L -o "$ZIP_PATH" "$DOWNLOAD_URL"; then
    echo "❌ Failed to download Nuclei"
    exit 1
fi

echo "📦 Extracting Nuclei..."

# Extract the zip file
if command -v unzip >/dev/null 2>&1; then
    unzip -o "$ZIP_PATH" -d "$NUCLEI_DIR"
else
    echo "❌ unzip command not found. Please install unzip and try again."
    exit 1
fi

# Clean up zip file
rm "$ZIP_PATH"

# Make binary executable
chmod +x "$NUCLEI_BIN"

# Verify installation
if [ -f "$NUCLEI_BIN" ]; then
    echo "✅ Nuclei installed successfully!"
    
    # Get version
    if VERSION=$("$NUCLEI_BIN" -version 2>/dev/null); then
        echo "📋 Installed version: $VERSION"
    else
        echo "⚠️ Could not determine Nuclei version, but binary exists"
    fi
    
    # Update templates
    echo "📥 Updating Nuclei templates..."
    if "$NUCLEI_BIN" -update-templates >/dev/null 2>&1; then
        echo "✅ Templates updated successfully!"
    else
        echo "⚠️ Could not update templates automatically"
        echo "💡 You can update them later with: nuclei -update-templates"
    fi
    
    echo ""
    echo "🎉 Nuclei installation completed!"
    echo "📋 Binary location: $NUCLEI_BIN"
    echo "💡 Add to PATH or set NUCLEI_PATH environment variable"
    echo ""
    echo "🔧 To use with Comply Checker, set environment variable:"
    echo "   export NUCLEI_PATH=$NUCLEI_BIN"
    echo ""
    
else
    echo "❌ Nuclei binary not found after extraction"
    exit 1
fi
