// Frontend types for HIPAA Security (matching backend types)
export interface HipaaSecurityScanResult {
  scanId: string;
  targetUrl: string;
  scanTimestamp: Date;
  scanDuration: number;
  overallScore: number;
  riskLevel: RiskLevel;

  passedTests: HipaaTestDetail[];
  failedTests: HipaaTestFailure[];

  technicalSafeguards: CategoryResult;
  administrativeSafeguards: CategoryResult;
  organizationalSafeguards: CategoryResult;
  physicalSafeguards: CategoryResult;

  vulnerabilities: VulnerabilityResult[];
  pagesScanned: string[];
  toolsUsed: string[];
  scanStatus: ScanStatus;
  errorMessage?: string;
}

export interface HipaaTestDetail {
  testId: string;
  testName: string;
  hipaaSection: string;
  description: string;
  category: HipaaCategory;
  passed: true;
  evidence: string;
  pagesTested: string[];
  timestamp: Date;
}

export interface HipaaTestFailure {
  testId: string;
  testName: string;
  hipaaSection: string;
  description: string;
  category: HipaaCategory;
  passed: false;
  failureReason: string;
  riskLevel: RiskLevel;
  failureEvidence: FailureEvidence[];
  recommendedAction: string;
  remediationPriority: number;
  timestamp: Date;
}

export interface FailureEvidence {
  location: string;
  elementType: ElementType;
  actualCode: string;
  expectedBehavior: string;
  lineNumber?: number;
  context: string;
}

export interface CategoryResult {
  category: HipaaCategory;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  score: number;
  riskLevel: RiskLevel;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
}

export interface VulnerabilityResult {
  id: string;
  type: string;
  severity: Severity;
  location: string;
  description: string;
  evidence: Record<string, unknown>;
  cweId?: number;
  owaspCategory?: string;
  remediationGuidance: string;
}

export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type HipaaCategory = 'technical' | 'administrative' | 'organizational' | 'physical';
export type ElementType = 'header' | 'html' | 'javascript' | 'response' | 'cookie' | 'form';
export type Severity = 'critical' | 'high' | 'medium' | 'low' | 'info';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// UI-specific types
export interface HipaaSecurityResultsPageProps {
  scanResult: HipaaSecurityScanResult;
  onExportReport?: () => void;
  onStartNewScan?: () => void;
}

export interface TestResultCardProps {
  test: HipaaTestDetail | HipaaTestFailure;
  expanded?: boolean;
  onToggleExpanded?: () => void;
}

export interface FailureEvidenceProps {
  evidence: FailureEvidence[];
  testName: string;
}
