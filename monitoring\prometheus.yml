# Prometheus Configuration for HIPAA Dashboard Monitoring
# Comprehensive monitoring setup for production environment

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'hipaa-dashboard'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Frontend (Next.js) monitoring
  - job_name: 'hipaa-frontend'
    static_configs:
      - targets: ['frontend:3000']
    scrape_interval: 15s
    metrics_path: /api/metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Backend (Node.js API) monitoring
  - job_name: 'hipaa-backend'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true

  # Database monitoring (PostgreSQL)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis monitoring
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx monitoring
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker container monitoring
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Application-specific metrics
  - job_name: 'hipaa-compliance-metrics'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 60s
    metrics_path: /metrics/compliance
    scrape_timeout: 30s

  # Security scan metrics
  - job_name: 'security-scan-metrics'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 300s  # 5 minutes
    metrics_path: /metrics/security
    scrape_timeout: 60s

  # Privacy scan metrics
  - job_name: 'privacy-scan-metrics'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 300s  # 5 minutes
    metrics_path: /metrics/privacy
    scrape_timeout: 60s

  # Blackbox exporter for external monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://yourdomain.com
        - https://yourdomain.com/dashboard/hipaa
        - https://yourdomain.com/api/v1/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # SSL certificate monitoring
  - job_name: 'ssl-exporter'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 300s
    metrics_path: /probe
    params:
      target: ['yourdomain.com:443']

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://cortex:9009/api/prom/push"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "http://cortex:9009/api/prom/read"
    read_recent: true
