export const HIPAA_SECURITY_CONSTANTS = {
  // Test Categories
  CATEGORIES: {
    TECHNICAL: 'technical',
    ADMINISTRATIVE: 'administrative',
    ORGANIZATIONAL: 'organizational',
    PHYSICAL: 'physical',
  } as const,

  // Risk Levels
  RISK_LEVELS: {
    CRITICAL: 'critical',
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low',
  } as const,

  // HIPAA Sections
  HIPAA_SECTIONS: {
    ACCESS_CONTROL: '164.312(a)(1)',
    UNIQUE_USER_ID: '164.312(a)(2)',
    AUTHENTICATION: '164.312(d)',
    AUDIT_CONTROLS: '164.312(b)',
    TRANSMISSION_SECURITY: '164.312(e)(1)',
    ENCRYPTION: '164.312(e)(2)',
    INTEGRITY_CONTROLS: '164.312(c)(1)',
    SECURITY_MANAGEMENT: '164.308(a)(1)',
    SECURITY_RESPONSIBILITY: '164.308(a)(2)',
    WORKFORCE_TRAINING: '164.308(a)(3)',
    INFORMATION_ACCESS: '164.308(a)(4)',
    SECURITY_AWARENESS: '164.308(a)(5)',
    INCIDENT_PROCEDURES: '164.308(a)(6)',
    CONTINGENCY_PLAN: '164.308(a)(7)',
    EVALUATION: '164.308(a)(8)',
    BUSINESS_ASSOCIATES: '164.314(a)(1)',
    GROUP_HEALTH_PLANS: '164.314(a)(2)',
  } as const,

  // Default scan configuration
  DEFAULT_SCAN_CONFIG: {
    maxPages: 15,
    scanDepth: 2,
    timeout: 1800000, // 30 minutes
    enableVulnerabilityScanning: true,
    enableSSLAnalysis: true,
    enableContentAnalysis: true,
    userAgent: 'HIPAA-Security-Scanner/1.0',
  },

  // Page patterns to scan
  SCAN_PAGES: {
    CORE_SECURITY: ['/', '/login', '/admin', '/dashboard', '/account'],
    AUTHENTICATION: ['/register', '/reset-password', '/change-password', '/logout'],
    ADMINISTRATIVE: ['/logs', '/audit', '/admin/logs', '/admin/users'],
    POLICY_COMPLIANCE: ['/about', '/contact', '/security', '/compliance', '/privacy', '/terms'],
    API_ENDPOINTS: ['/api/*', '/upload', '/download'],
    BUSINESS: ['/partners', '/vendors', '/integrations'],
  },

  // ePHI detection patterns
  EPHI_PATTERNS: [
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN
    /\b[A-Z]\d{2}\.\d{3}\b/, // ICD codes
    /\bpatient\s+id\s*:\s*\d+/i,
    /\bmedical\s+record\s+number/i,
    /\bdiagnosis\s*:\s*[A-Z]/i,
    /\bhealth\s+insurance\s+number/i,
    /\bmrn\s*:\s*\d+/i,
  ],

  // Security headers to check
  SECURITY_HEADERS: [
    'strict-transport-security',
    'content-security-policy',
    'x-frame-options',
    'x-content-type-options',
    'x-xss-protection',
    'referrer-policy',
  ],

  // Minimum TLS version
  MIN_TLS_VERSION: 'TLSv1.2',

  // Scan timeouts
  TIMEOUTS: {
    PAGE_LOAD: 30000,
    SSL_CHECK: 60000,
    VULNERABILITY_SCAN: 300000,
    TOTAL_SCAN: 1800000,
  },
} as const;
