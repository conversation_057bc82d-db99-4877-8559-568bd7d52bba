// backend/src/compliance/wcag/types.ts

export interface PageTitleCheckDetails {
  info: string;
  target: string;
  foundTitle: string | null;
}

export interface WcagCheckResult {
  checkId: string; // e.g., WCAG-TITLE-001
  name: string; // e.g., "Page Title Presence"
  passed: boolean;
  description: string; // Detailed description of the finding
  details: string | PageTitleCheckDetails | object; // Specifics for this check, or general object/string
  level?: 'A' | 'AA' | 'AAA'; // WCAG conformance level
  sc?: string; // WCAG Success Criterion, e.g., "2.4.2"
  // Add any WCAG-specific fields if needed in the future
}
