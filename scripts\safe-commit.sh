#!/bin/bash

# Safe commit script to avoid git hook issues
# Usage: ./scripts/safe-commit.sh "commit message"

set -e

echo "🔍 Running pre-commit checks..."

# Check if there are any staged changes
if ! git diff --cached --quiet; then
    echo "✅ Found staged changes"
else
    echo "❌ No staged changes found. Please stage your changes first with 'git add'"
    exit 1
fi

# Run linting
echo "🔧 Running linter..."
npm run lint || {
    echo "❌ Linting failed. Running auto-fix..."
    npm run lint:fix || {
        echo "❌ Auto-fix failed. Please fix lint errors manually."
        exit 1
    }
}

# Run prettier
echo "💅 Running prettier..."
npm run format || {
    echo "❌ Prettier failed."
    exit 1
}

# Add any files that were auto-fixed
git add .

# Commit with the provided message
if [ -n "$1" ]; then
    echo "📝 Committing with message: $1"
    git commit -m "$1"
else
    echo "📝 Committing with interactive editor..."
    git commit
fi

echo "✅ Commit successful!"
