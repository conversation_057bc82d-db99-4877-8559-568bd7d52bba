import { Router } from 'express';
import healthRouter from './health';
import { env } from '@lib/env'; // Import env to check NODE_ENV
// Import other route modules here as they are created
import authRouter from './auth';
import complianceRouter from './compliance';
import hipaaSecurityRouter from './hipaa-security';
import hipaaPrivacyRouter from './hipaa-privacy';

// Import test-auth router for development testing
import testAuthRouter from './test-auth';

const mainRouter = Router();

// Health endpoint always registered - accessible without auth
mainRouter.use('/health', healthRouter);

// Register production auth and compliance routes
mainRouter.use('/auth', authRouter);
mainRouter.use('/compliance', complianceRouter);
mainRouter.use('/hipaa-security', hipaaSecurityRouter);
mainRouter.use('/hipaa-privacy', hipaaPrivacyRouter);

// Only register test-auth routes in development environment
// These routes are useful for testing Keycloak auth flow during development
// but should not be exposed in production
if (env.NODE_ENV === 'development') {
  console.log('[INFO] Registering test-auth routes (development only)');
  mainRouter.use('/test-auth', testAuthRouter);
}

export default mainRouter;
