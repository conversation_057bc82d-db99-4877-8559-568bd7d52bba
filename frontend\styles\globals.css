@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  padding: 0;
}

@layer base {
  :root {
    /* Design System CSS Variables - Standard Theme (Light Mode) */
    /* Primary Colors - WCAG AA Compliant */
    --color-primary: #0055a4; /* Primary Blue */
    --color-primary-light: #1e6bb8; /* Lighter blue for hover states */
    --color-primary-dark: #003d7a; /* Darker blue for active states */

    /* Accent Colors */
    --color-accent: #663399; /* Accent Purple */
    --color-accent-light: #7a4db8; /* Lighter purple for hover states */
    --color-accent-dark: #4d2673; /* Darker purple for active states */

    /* Background Colors */
    --color-background: #f5f5f5; /* Light gray background */
    --color-background-secondary: #ffffff; /* White background for cards */

    /* Text Colors */
    --color-text-primary: #333333; /* Dark gray primary text */
    --color-text-secondary: #666666; /* Medium gray secondary text */
    --color-text-tertiary: #999999; /* Light gray tertiary text */

    /* Border Colors */
    --color-border: #e5e5e5; /* Light gray borders */
    --color-border-light: #f0f0f0; /* Very light borders */

    /* Status Colors */
    --color-success: #22c55e; /* Success green */
    --color-warning: #f59e0b; /* Warning orange */
    --color-error: #ef4444; /* Error red */
    --color-info: #3b82f6; /* Info blue */

    /* Legacy CSS Variables for Compatibility */
    --background: 0 0% 96.1%; /* Maps to #F5F5F5 */
    --foreground: 210 11% 20%; /* Maps to #333333 */
    --card: 0 0% 100%; /* White cards */
    --card-foreground: 210 11% 20%; /* Dark text on cards */
    --popover: 0 0% 100%; /* White popovers */
    --popover-foreground: 210 11% 20%; /* Dark text on popovers */
    --primary: 213 94% 32%; /* Maps to #0055A4 */
    --primary-foreground: 0 0% 100%; /* White text on primary */
    --secondary: 0 0% 96.1%; /* Light gray secondary */
    --secondary-foreground: 210 11% 20%; /* Dark text on secondary */
    --muted: 0 0% 96.1%; /* Muted background */
    --muted-foreground: 210 6% 40%; /* Muted text */
    --accent: 270 50% 40%; /* Maps to #663399 */
    --accent-foreground: 0 0% 100%; /* White text on accent */
    --destructive: 0 84% 60%; /* Error red */
    --destructive-foreground: 0 0% 100%; /* White text on destructive */
    --border: 0 0% 90%; /* Light borders */
    --input: 0 0% 100%; /* White input backgrounds */
    --ring: 213 94% 32%; /* Focus ring color */
    --radius: 0.5rem; /* Border radius */

    /* Chart Colors */
    --chart-1: 213 94% 32%; /* Primary blue */
    --chart-2: 270 50% 40%; /* Accent purple */
    --chart-3: 142 76% 36%; /* Success green */
    --chart-4: 38 92% 50%; /* Warning orange */
    --chart-5: 0 84% 60%; /* Error red */

    /* Status Colors (HSL format for compatibility) */
    --success: 142 76% 96%;
    --success-foreground: 142 76% 20%;
    --warning: 38 92% 96%;
    --warning-foreground: 38 92% 20%;
  }
  /* Dark Theme - Will be implemented in design-system.css */
  .dark {
    /* Dark mode variables will be managed by the design system */
    /* This section is intentionally minimal to avoid conflicts */
    --background: 210 11% 15%; /* Dark background */
    --foreground: 210 6% 93%; /* Light text */
    --card: 210 11% 18%; /* Dark card background */
    --card-foreground: 210 6% 93%; /* Light card text */
    --popover: 210 11% 18%; /* Dark popover background */
    --popover-foreground: 210 6% 93%; /* Light popover text */
    --primary: 213 94% 68%; /* Light blue for dark backgrounds */
    --primary-foreground: 210 11% 15%; /* Dark text on light blue */
    --secondary: 210 11% 25%; /* Dark secondary background */
    --secondary-foreground: 210 6% 93%; /* Light secondary text */
    --muted: 210 11% 22%; /* Dark muted background */
    --muted-foreground: 210 6% 70%; /* Medium light muted text */
    --accent: 270 50% 65%; /* Light purple for dark backgrounds */
    --accent-foreground: 210 11% 15%; /* Dark text on light purple */
    --destructive: 0 84% 65%; /* Light red for dark backgrounds */
    --destructive-foreground: 210 11% 15%; /* Dark text on light red */
    --border: 210 11% 30%; /* Dark border */
    --input: 210 11% 25%; /* Dark input background */
    --ring: 213 94% 68%; /* Light blue focus ring */
    --chart-1: 213 94% 68%; /* Chart colors for dark mode */
    --chart-2: 270 50% 65%;
    --chart-3: 142 76% 60%;
    --chart-4: 38 92% 65%;
    --chart-5: 0 84% 65%;
    --success: 142 76% 60%;
    --success-foreground: 210 11% 15%;
    --warning: 38 92% 65%;
    --warning-foreground: 210 11% 15%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced text visibility for guidance pages */
@layer utilities {
  .text-readable {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .text-readable-muted {
    @apply text-gray-800 dark:text-gray-200 !important;
  }

  .text-readable-light {
    @apply text-gray-700 dark:text-gray-300 !important;
  }

  /* Force high contrast for all text elements */
  .text-high-contrast {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 500;
  }

  .text-medium-contrast {
    @apply text-gray-800 dark:text-gray-200 !important;
    font-weight: 400;
  }

  /* Template and code block styling */
  .template-text {
    @apply text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 !important;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .code-block {
    @apply text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 !important;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Ensure good contrast for guidance content */
  .guidance-content {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .guidance-content h1,
  .guidance-content h2,
  .guidance-content h3,
  .guidance-content h4,
  .guidance-content h5,
  .guidance-content h6 {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .guidance-content p,
  .guidance-content li,
  .guidance-content span,
  .guidance-content div {
    @apply text-gray-800 dark:text-gray-200 !important;
    line-height: 1.6;
  }

  .guidance-content .text-muted {
    @apply text-gray-700 dark:text-gray-300 !important;
  }

  /* Step-by-step guide styling */
  .step-guide {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .step-guide .step-number {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 700;
    font-size: 1.125rem;
  }

  .step-guide .step-description {
    @apply text-gray-800 dark:text-gray-200 !important;
    line-height: 1.6;
  }

  /* Section headers */
  .section-header {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  /* Progress text */
  .progress-text {
    @apply text-gray-800 dark:text-gray-200 !important;
    font-weight: 500;
  }

  /* Card descriptions */
  .card-description {
    @apply text-gray-700 dark:text-gray-300 !important;
    line-height: 1.5;
  }

  /* List items */
  .guidance-list li {
    @apply text-gray-800 dark:text-gray-200 !important;
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  /* Required elements styling */
  .required-elements {
    @apply text-gray-900 dark:text-gray-100 !important;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .dark .required-elements {
    background-color: #1f2937;
    border-color: #374151;
  }

  .required-elements h4 {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #111827 !important;
  }

  .dark .required-elements h4 {
    color: #f9fafb !important;
  }

  .required-elements span {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 500;
    color: #111827 !important;
    line-height: 1.6;
  }

  .dark .required-elements span {
    color: #f9fafb !important;
  }

  .required-elements li {
    margin-bottom: 0.5rem;
  }

  /* Process steps styling */
  .process-steps {
    counter-reset: step-counter;
  }

  .process-step {
    counter-increment: step-counter;
    @apply text-gray-800 dark:text-gray-200 !important;
    position: relative;
    padding-left: 3rem;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .process-step::before {
    content: counter(step-counter);
    position: absolute;
    left: 0;
    top: 0;
    @apply bg-blue-600 text-white !important;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
  }

  /* Card and section improvements */
  .card-description {
    @apply text-gray-800 dark:text-gray-200 !important;
  }

  .section-header {
    @apply text-gray-900 dark:text-gray-100 !important;
  }
}
