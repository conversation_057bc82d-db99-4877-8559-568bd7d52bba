/**
 * Integration Tests for HIPAA Dashboard
 * Tests complete user flows and API integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import HipaaDashboardPage from '@/app/dashboard/hipaa/page';
import { ToastProvider } from '@/components/ui/Toast';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/dashboard/hipaa',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock the dashboard API service
jest.mock('@/services/hipaa-dashboard-api', () => ({
  hipaaDashboardService: {
    getDashboardData: jest.fn(),
    getPrivacyScans: jest.fn(),
    getSecurityScans: jest.fn(),
    getDashboardMetrics: jest.fn(),
    calculateOverallScore: jest.fn(),
    getRiskLevelFromScore: jest.fn(),
    getComplianceStatus: jest.fn(),
  },
}));

// Mock responsive hooks
jest.mock('@/hooks/useResponsive', () => ({
  useResponsiveDashboard: () => ({
    layout: { isMobile: false, isTablet: false, isDesktop: true, stackVertically: false },
    spacing: { section: '1.5rem', container: '2rem' },
    cards: { columns: 3, gap: '2rem', padding: '2rem' },
  }),
  useBreakpoint: () => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    currentBreakpoint: 'lg',
  }),
}));

const mockDashboardData = {
  overview: {
    overallScore: 85,
    riskLevel: 'medium',
    complianceStatus: 'partially_compliant',
    lastScanDate: '2025-06-24T10:30:00Z',
    totalScans: 24,
  },
  privacyModule: {
    latestScore: 82,
    scanCount: 12,
    lastScanDate: '2025-06-24T10:30:00Z',
    status: 'active',
    recentScans: [],
  },
  securityModule: {
    latestScore: 88,
    scanCount: 12,
    lastScanDate: '2025-06-23T14:15:00Z',
    status: 'active',
    recentScans: [],
  },
  recentActivity: [
    {
      id: 'activity-1',
      type: 'privacy',
      url: 'https://example.com',
      timestamp: '2025-06-24T10:30:00Z',
      score: 82,
      status: 'completed',
      riskLevel: 'medium',
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ToastProvider>{component}</ToastProvider>
    </BrowserRouter>,
  );
};

describe('HIPAA Dashboard Integration', () => {
  const { hipaaDashboardService } = require('@/services/hipaa-dashboard-api');

  beforeEach(() => {
    jest.clearAllMocks();
    hipaaDashboardService.getDashboardData.mockResolvedValue(mockDashboardData);
  });

  describe('Page Load Flow', () => {
    it('should load dashboard data on page mount', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(1);
      });

      expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
    });

    it('should show loading state initially', () => {
      hipaaDashboardService.getDashboardData.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(mockDashboardData), 1000)),
      );

      renderWithProviders(<HipaaDashboardPage />);

      expect(screen.getByText('Loading HIPAA dashboard data')).toBeInTheDocument();
    });

    it('should handle API errors gracefully', async () => {
      const errorMessage = 'Failed to fetch dashboard data';
      hipaaDashboardService.getDashboardData.mockRejectedValue(new Error(errorMessage));

      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  describe('Refresh Flow', () => {
    it('should refresh data when refresh button is clicked', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('should show loading state during refresh', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Mock a slow refresh
      hipaaDashboardService.getDashboardData.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(mockDashboardData), 1000)),
      );

      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });
      fireEvent.click(refreshButton);

      expect(screen.getByText('Refreshing...')).toBeInTheDocument();
      expect(refreshButton).toBeDisabled();
    });

    it('should handle refresh errors', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Mock refresh error
      hipaaDashboardService.getDashboardData.mockRejectedValueOnce(new Error('Network error'));

      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation Flow', () => {
    it('should have working navigation links', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Check for navigation links
      expect(
        screen.getByRole('link', { name: /view detailed compliance reports/i }),
      ).toBeInTheDocument();
    });

    it('should navigate to privacy module when privacy card is clicked', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // This would test navigation if we had proper routing setup
      // For now, we just verify the elements exist
      expect(screen.getByText(/privacy/i)).toBeInTheDocument();
    });

    it('should navigate to security module when security card is clicked', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // This would test navigation if we had proper routing setup
      // For now, we just verify the elements exist
      expect(screen.getByText(/security/i)).toBeInTheDocument();
    });
  });

  describe('Data Display Flow', () => {
    it('should display correct compliance scores', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Check that scores are displayed
      expect(screen.getByText('85')).toBeInTheDocument(); // Overall score
      expect(screen.getByText('82')).toBeInTheDocument(); // Privacy score
      expect(screen.getByText('88')).toBeInTheDocument(); // Security score
    });

    it('should display risk levels correctly', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      expect(screen.getByText(/medium/i)).toBeInTheDocument();
    });

    it('should display recent activity', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      expect(screen.getByText('Recent Scan Activity')).toBeInTheDocument();
      expect(screen.getByText('https://example.com')).toBeInTheDocument();
    });
  });

  describe('Error Recovery Flow', () => {
    it('should allow retry after error', async () => {
      hipaaDashboardService.getDashboardData
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockDashboardData);

      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      const retryButton = screen.getByRole('button', { name: /try again/i });
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(2);
    });
  });

  describe('Accessibility Flow', () => {
    it('should be navigable with keyboard', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Test tab navigation
      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });
      refreshButton.focus();
      expect(document.activeElement).toBe(refreshButton);

      // Test Enter key activation
      fireEvent.keyDown(refreshButton, { key: 'Enter' });
      expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(2);
    });

    it('should have proper ARIA labels and roles', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /refresh dashboard data/i })).toBeInTheDocument();
    });
  });

  describe('Performance Flow', () => {
    it('should not make unnecessary API calls', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      // Should only call getDashboardData once on mount
      expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(1);

      // Re-rendering should not trigger additional calls
      renderWithProviders(<HipaaDashboardPage />);
      expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(1);
    });

    it('should handle concurrent refresh requests', async () => {
      renderWithProviders(<HipaaDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('HIPAA Compliance Dashboard')).toBeInTheDocument();
      });

      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });

      // Click refresh multiple times quickly
      fireEvent.click(refreshButton);
      fireEvent.click(refreshButton);
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(hipaaDashboardService.getDashboardData).toHaveBeenCalledTimes(4); // 1 initial + 3 refreshes
      });
    });
  });
});
