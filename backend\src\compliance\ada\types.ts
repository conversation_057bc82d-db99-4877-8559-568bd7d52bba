// backend/src/compliance/ada/types.ts

export interface AdaCheckDetail {
  element: string; // e.g., 'img[src="path/to/image.jpg"]' or a unique selector
  message: string; // Description of the specific finding for this element
  passed: boolean; // Did this specific element pass the check?
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'; // Severity of the issue for this element
  htmlLocation?: string; // Snippet of the HTML element
  imgSrc?: string; // src attribute of the image
  altText?: string | null; // alt attribute value
}

export interface AdaCheckResult {
  checkId: string; // e.g., 'ADA-IMG-001' for image alt text
  name: string; // e.g., "Image Alt Text Presence"
  description: string; // Overall summary of this check's findings for the page
  passed: boolean; // Overall pass/fail status for this check on the page
  details: AdaCheckDetail[]; // Array of specific findings for each relevant element
  // Optional summary fields that can be added by the check function
  totalElements?: number; // e.g., total images found
  nonCompliantElements?: number; // e.g., number of images failing the check
}
