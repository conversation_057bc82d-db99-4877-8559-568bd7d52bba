import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add positive_findings column to hipaa_scan_results table if it doesn't exist
  const hasColumn = await knex.schema.hasColumn('hipaa_scan_results', 'positive_findings');
  if (!hasColumn) {
    await knex.schema.alterTable('hipaa_scan_results', (table) => {
      table.jsonb('positive_findings').defaultTo('[]');
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  // Remove positive_findings column
  const hasColumn = await knex.schema.hasColumn('hipaa_scan_results', 'positive_findings');
  if (hasColumn) {
    await knex.schema.alterTable('hipaa_scan_results', (table) => {
      table.dropColumn('positive_findings');
    });
  }
}
