import React from 'react';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { AlertTriangle, CheckCircle, XCircle, Shield, Clock } from 'lucide-react';

export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';

export interface RiskLevelIndicatorProps {
  riskLevel: RiskLevel;
  score?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'badge' | 'card' | 'inline';
  showIcon?: boolean;
  showProgress?: boolean;
  showDescription?: boolean;
  className?: string;
}

/**
 * Risk Level Indicator Component
 * Visual indicator for risk levels with consistent styling
 */
export const RiskLevelIndicator: React.FC<RiskLevelIndicatorProps> = ({
  riskLevel,
  score,
  size = 'md',
  variant = 'badge',
  showIcon = true,
  showProgress = false,
  showDescription = false,
  className = '',
}) => {
  const getRiskConfig = (level: RiskLevel) => {
    switch (level) {
      case 'critical':
        return {
          label: 'Critical Risk',
          color: 'bg-red-500 text-white',
          badgeVariant: 'destructive' as const,
          icon: <XCircle className="h-4 w-4" />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          description: 'Immediate action required. High probability of compliance violations.',
          progressColor: 'bg-red-500',
        };
      case 'high':
        return {
          label: 'High Risk',
          color: 'bg-orange-500 text-white',
          badgeVariant: 'warning' as const,
          icon: <AlertTriangle className="h-4 w-4" />,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
          description: 'Significant issues found. Action needed within 30 days.',
          progressColor: 'bg-orange-500',
        };
      case 'medium':
        return {
          label: 'Medium Risk',
          color: 'bg-yellow-500 text-white',
          badgeVariant: 'warning' as const,
          icon: <Clock className="h-4 w-4" />,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          description: 'Some issues identified. Review and improve when possible.',
          progressColor: 'bg-yellow-500',
        };
      case 'low':
        return {
          label: 'Low Risk',
          color: 'bg-green-500 text-white',
          badgeVariant: 'success' as const,
          icon: <CheckCircle className="h-4 w-4" />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          description: 'Good compliance status. Minor improvements may be beneficial.',
          progressColor: 'bg-green-500',
        };
      default:
        return {
          label: 'Unknown Risk',
          color: 'bg-gray-500 text-white',
          badgeVariant: 'secondary' as const,
          icon: <Shield className="h-4 w-4" />,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          description: 'Risk level could not be determined.',
          progressColor: 'bg-gray-500',
        };
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          text: 'text-xs',
          padding: 'px-2 py-1',
          icon: 'h-3 w-3',
          gap: 'gap-1',
        };
      case 'lg':
        return {
          text: 'text-lg',
          padding: 'px-4 py-2',
          icon: 'h-6 w-6',
          gap: 'gap-3',
        };
      default: // md
        return {
          text: 'text-sm',
          padding: 'px-3 py-1',
          icon: 'h-4 w-4',
          gap: 'gap-2',
        };
    }
  };

  const config = getRiskConfig(riskLevel);
  const sizeClasses = getSizeClasses(size);

  if (variant === 'badge') {
    return (
      <Badge
        variant={config.badgeVariant}
        className={`${sizeClasses.padding} ${sizeClasses.text} font-semibold flex items-center ${sizeClasses.gap} ${className}`}
      >
        {showIcon && <span className={sizeClasses.icon}>{config.icon}</span>}
        {config.label}
      </Badge>
    );
  }

  if (variant === 'inline') {
    return (
      <div
        className={`inline-flex items-center ${sizeClasses.gap} ${config.textColor} ${className}`}
      >
        {showIcon && <span className={sizeClasses.icon}>{config.icon}</span>}
        <span className={`font-semibold ${sizeClasses.text}`}>{config.label}</span>
        {score !== undefined && (
          <span className={`${sizeClasses.text} opacity-75`}>({score}%)</span>
        )}
      </div>
    );
  }

  // Card variant
  return (
    <div className={`p-4 rounded-lg ${config.bgColor} border ${config.borderColor} ${className}`}>
      <div className={`flex items-center justify-between mb-2`}>
        <div className={`flex items-center ${sizeClasses.gap}`}>
          {showIcon && <div className={config.textColor}>{config.icon}</div>}
          <span className={`font-semibold ${sizeClasses.text} ${config.textColor}`}>
            {config.label}
          </span>
        </div>
        {score !== undefined && (
          <span className={`font-bold text-lg ${config.textColor}`}>{score}%</span>
        )}
      </div>

      {showProgress && score !== undefined && (
        <div className="mb-2">
          <Progress value={score} className="h-2 bg-white bg-opacity-50" />
        </div>
      )}

      {showDescription && (
        <p className={`${sizeClasses.text} ${config.textColor} opacity-75`}>{config.description}</p>
      )}
    </div>
  );
};

// Utility function to determine risk level from score
export const getRiskLevelFromScore = (score: number): RiskLevel => {
  if (score >= 90) return 'low';
  if (score >= 70) return 'medium';
  if (score >= 50) return 'high';
  return 'critical';
};

// Utility function to get risk level color for custom styling
export const getRiskLevelColor = (riskLevel: RiskLevel) => {
  switch (riskLevel) {
    case 'critical':
      return 'red';
    case 'high':
      return 'orange';
    case 'medium':
      return 'yellow';
    case 'low':
      return 'green';
    default:
      return 'gray';
  }
};

// Utility function to get risk level priority (for sorting)
export const getRiskLevelPriority = (riskLevel: RiskLevel): number => {
  switch (riskLevel) {
    case 'critical':
      return 4;
    case 'high':
      return 3;
    case 'medium':
      return 2;
    case 'low':
      return 1;
    default:
      return 0;
  }
};
