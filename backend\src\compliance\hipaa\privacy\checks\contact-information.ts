// backend/src/compliance/hipaa/privacy/checks/contact-information.ts

/**
 * Contact Information Validation Check
 * Validates presence and accessibility of privacy officer contact information
 */

import {
  HipaaCheckResult,
  HipaaCheckCategory,
  HipaaSeverity,
  HipaaFindingType,
  HipaaFinding,
  CheckOptions,
  HipaaRemediation,
  HipaaEvidence,
} from '../types';
import { URLResolver } from '../utils/url-resolver';
import { ContentAnalyzer } from '../utils/content-analyzer';

/**
 * Validates privacy officer contact information and complaint procedures
 * @param targetUrl - The URL to check for contact information
 * @param options - Configuration options for the check
 * @returns Promise<HipaaCheckResult> - Detailed check result
 */
export async function checkContactInformation(
  targetUrl: string,
  options: CheckOptions = {},
): Promise<HipaaCheckResult> {
  const startTime = Date.now();

  const result: HipaaCheckResult = {
    checkId: 'HIPAA-CONTACT-001',
    name: 'Privacy Officer Contact Information',
    category: HipaaCheckCategory.CONTACT_INFO,
    passed: false,
    severity: HipaaSeverity.HIGH,
    confidence: 0,
    description:
      'Validates the presence and accessibility of privacy officer contact information and complaint procedures',
    details: {
      summary: '',
      findings: [],
      metrics: {
        processingTime: 0,
        contentLength: 0,
        contactMethodsFound: 0,
        accessibilityScore: 0,
      },
      context: {
        url: targetUrl,
        pageTitle: '',
        lastModified: '',
        contentType: '',
        language: '',
      },
    },
    remediation: {
      priority: 'high',
      effort: 'minimal',
      steps: [],
      resources: [],
      timeline: '1-2 days',
      estimatedCost: '$200 - $500',
    },
    evidence: [],
    metadata: {
      checkVersion: '2.0',
      processingTime: 0,
      analysisLevels: [1],
      warnings: [],
    },
  };

  try {
    // Step 1: Discover and fetch privacy policy content
    const privacyPolicyUrl = await discoverPrivacyPolicyUrl(targetUrl);
    const htmlContent = await URLResolver.fetchPageContent(privacyPolicyUrl, {
      timeout: options.timeout || 30000,
      userAgent: options.userAgent,
    });

    // Step 2: Extract and analyze content
    const cleanText = ContentAnalyzer.extractText(htmlContent);
    const pageTitle = ContentAnalyzer.extractPageTitle(htmlContent);
    const language = ContentAnalyzer.detectLanguage(htmlContent);

    result.details.context.pageTitle = pageTitle;
    result.details.context.language = language;
    result.details.context.contentType = 'text/html';
    result.details.metrics.contentLength = cleanText.length;

    // Step 3: Find contact information
    const contactInfo = extractContactInformation(cleanText);
    result.details.metrics.contactMethodsFound = contactInfo.methods.length;

    // Step 4: Validate privacy officer information
    const privacyOfficerInfo = findPrivacyOfficerInfo(cleanText);

    // Step 5: Check complaint procedures
    const complaintProcedures = findComplaintProcedures(cleanText);

    // Step 6: Validate accessibility of contact methods
    const accessibilityScore = validateContactAccessibility(contactInfo, htmlContent);
    result.details.metrics.accessibilityScore = accessibilityScore;

    // Step 7: Calculate overall result
    const { passed, confidence, findings } = calculateContactResult(
      contactInfo,
      privacyOfficerInfo,
      complaintProcedures,
      accessibilityScore,
    );

    result.passed = passed;
    result.confidence = confidence;
    result.details.findings = findings;

    // Step 8: Generate remediation guidance
    if (!passed) {
      result.remediation = generateContactRemediation(findings);
      result.severity = determineContactSeverity(findings);
    } else {
      result.severity = HipaaSeverity.INFO;
      result.remediation.priority = 'low';
      result.remediation.steps = ['Contact information validation successful'];
    }

    // Step 9: Collect evidence
    result.evidence = collectContactEvidence(contactInfo, privacyOfficerInfo, complaintProcedures);

    // Step 10: Generate summary
    result.details.summary = generateContactSummary(result);
  } catch (error) {
    // Handle errors gracefully
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorName = error instanceof Error ? error.name : 'Error';

    result.passed = false;
    result.confidence = 0;
    result.severity = HipaaSeverity.HIGH;
    result.details.summary = `Error during contact information check: ${errorMessage}`;
    result.details.findings.push({
      type: HipaaFindingType.ERROR,
      location: 'Check execution',
      content: errorMessage,
      severity: HipaaSeverity.HIGH,
      message: 'Failed to complete contact information check',
      suggestion: 'Verify privacy policy URL accessibility and try again',
      context: `Error: ${errorName}`,
      confidence: 100,
    });

    if (!result.metadata.warnings) {
      result.metadata.warnings = [];
    }
    result.metadata.warnings.push(`Contact check failed: ${errorMessage}`);
  }

  // Final processing
  const processingTime = Date.now() - startTime;
  result.details.metrics.processingTime = processingTime;
  result.metadata.processingTime = processingTime;

  return result;
}

/**
 * Extract contact information from content
 */
function extractContactInformation(content: string): ContactInfo {
  const methods: ContactMethod[] = [];

  // Phone number patterns
  const phoneRegex = /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/g;
  const phoneMatches = content.match(phoneRegex) || [];

  phoneMatches.forEach((phone) => {
    methods.push({
      type: 'phone',
      value: phone.trim(),
      context: extractContext(content, phone),
      accessible: true,
    });
  });

  // Email patterns
  const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
  const emailMatches = content.match(emailRegex) || [];

  emailMatches.forEach((email) => {
    methods.push({
      type: 'email',
      value: email.trim(),
      context: extractContext(content, email),
      accessible: true,
    });
  });

  // Mailing address patterns
  const addressRegex =
    /(\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Court|Ct|Place|Pl))/gi;
  const addressMatches = content.match(addressRegex) || [];

  addressMatches.forEach((address) => {
    methods.push({
      type: 'address',
      value: address.trim(),
      context: extractContext(content, address),
      accessible: true,
    });
  });

  return { methods, hasPrivacyOfficer: false, hasComplaintProcedure: false };
}

/**
 * Find privacy officer information
 */
function findPrivacyOfficerInfo(content: string): PrivacyOfficerInfo {
  const privacyOfficerPatterns = [
    /privacy\s+officer/i,
    /chief\s+privacy\s+officer/i,
    /hipaa\s+officer/i,
    /privacy\s+contact/i,
    /privacy\s+coordinator/i,
  ];

  let found = false;
  let title = '';
  let name = '';
  let contact = '';

  for (const pattern of privacyOfficerPatterns) {
    const match = content.match(pattern);
    if (match) {
      found = true;
      title = match[0];

      // Try to extract name and contact info near the title
      const context = extractContext(content, match[0], 200);

      // Look for names (capitalized words)
      const nameMatch = context.match(/([A-Z][a-z]+\s+[A-Z][a-z]+)/);
      if (nameMatch) {
        name = nameMatch[0];
      }

      // Look for contact info in context
      const emailMatch = context.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      const phoneMatch = context.match(/(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/);

      if (emailMatch) contact = emailMatch[0];
      else if (phoneMatch) contact = phoneMatch[0];

      break;
    }
  }

  return { found, title, name, contact };
}

/**
 * Find complaint procedures
 */
function findComplaintProcedures(content: string): ComplaintProcedures {
  const complaintPatterns = [
    /complaint/i,
    /grievance/i,
    /file\s+a\s+complaint/i,
    /report\s+a\s+violation/i,
    /department\s+of\s+health\s+and\s+human\s+services/i,
    /hhs/i,
  ];

  let found = false;
  const procedures: string[] = [];
  let hasHHSReference = false;

  for (const pattern of complaintPatterns) {
    const matches = content.match(new RegExp(pattern.source, 'gi'));
    if (matches) {
      found = true;

      if (pattern.source.includes('health.*human.*services') || pattern.source === 'hhs') {
        hasHHSReference = true;
      }

      // Extract procedure context
      matches.forEach((match) => {
        const context = extractContext(content, match, 150);
        if (!procedures.includes(context)) {
          procedures.push(context);
        }
      });
    }
  }

  return { found, procedures, hasHHSReference };
}

/**
 * Validate accessibility of contact methods
 */
function validateContactAccessibility(contactInfo: ContactInfo, htmlContent: string): number {
  if (contactInfo.methods.length === 0) return 0;

  let score = 0;
  let totalChecks = 0;

  contactInfo.methods.forEach((method) => {
    totalChecks += 3; // 3 checks per contact method

    // Check 1: Contact method is clearly labeled
    if (isContactMethodLabeled(method, htmlContent)) {
      score += 33;
    }

    // Check 2: Contact method is easily findable
    if (isContactMethodFindable(method, htmlContent)) {
      score += 33;
    }

    // Check 3: Contact method appears functional
    if (isContactMethodFunctional(method)) {
      score += 34;
    }
  });

  return totalChecks > 0 ? Math.round(score / totalChecks) : 0;
}

/**
 * Calculate overall contact result
 */
function calculateContactResult(
  contactInfo: ContactInfo,
  privacyOfficer: PrivacyOfficerInfo,
  complaints: ComplaintProcedures,
  accessibilityScore: number,
): { passed: boolean; confidence: number; findings: HipaaFinding[] } {
  const findings: HipaaFinding[] = [];
  let confidence = 0;
  let passed = false;

  // Check for contact methods
  if (contactInfo.methods.length > 0) {
    confidence += 30;
    findings.push({
      type: HipaaFindingType.EXACT_MATCH,
      location: 'Contact information',
      content: `Found ${contactInfo.methods.length} contact method(s)`,
      severity: HipaaSeverity.INFO,
      message: 'Contact information detected',
      suggestion: 'Ensure contact information is current and accessible',
      context: contactInfo.methods.map((m) => `${m.type}: ${m.value}`).join(', '),
      confidence: 90,
    });
  } else {
    findings.push({
      type: HipaaFindingType.MISSING_CONTENT,
      location: 'Contact information',
      content: '',
      severity: HipaaSeverity.HIGH,
      message: 'No contact information found',
      suggestion:
        'Add privacy officer contact information including phone, email, or mailing address',
      context: 'Required for HIPAA compliance',
      confidence: 100,
    });
  }

  // Check for privacy officer
  if (privacyOfficer.found) {
    confidence += 25;
    passed = true;
    findings.push({
      type: HipaaFindingType.EXACT_MATCH,
      location: 'Privacy officer information',
      content: `${privacyOfficer.title}${privacyOfficer.name ? ': ' + privacyOfficer.name : ''}`,
      severity: HipaaSeverity.INFO,
      message: 'Privacy officer information found',
      suggestion: 'Verify privacy officer contact details are current',
      context: privacyOfficer.contact || 'Contact details may need verification',
      confidence: 85,
    });
  } else {
    findings.push({
      type: HipaaFindingType.MISSING_CONTENT,
      location: 'Privacy officer information',
      content: '',
      severity: HipaaSeverity.HIGH,
      message: 'Privacy officer not clearly identified',
      suggestion: 'Clearly identify the privacy officer and provide contact information',
      context: 'HIPAA requires designated privacy officer contact',
      confidence: 95,
    });
  }

  // Check for complaint procedures
  if (complaints.found) {
    confidence += 20;
    if (complaints.hasHHSReference) {
      confidence += 15;
      findings.push({
        type: HipaaFindingType.EXACT_MATCH,
        location: 'Complaint procedures',
        content: 'HHS complaint reference found',
        severity: HipaaSeverity.INFO,
        message: 'Complaint procedures include HHS reference',
        suggestion: 'Ensure HHS contact information is current',
        context: 'Department of Health and Human Services',
        confidence: 90,
      });
    } else {
      findings.push({
        type: HipaaFindingType.PARTIAL_MATCH,
        location: 'Complaint procedures',
        content: 'Complaint procedures mentioned',
        severity: HipaaSeverity.MEDIUM,
        message: 'Complaint procedures found but missing HHS reference',
        suggestion: 'Include information about filing complaints with HHS',
        context: 'HIPAA requires HHS complaint option',
        confidence: 80,
      });
    }
  } else {
    findings.push({
      type: HipaaFindingType.MISSING_CONTENT,
      location: 'Complaint procedures',
      content: '',
      severity: HipaaSeverity.HIGH,
      message: 'No complaint procedures found',
      suggestion: 'Add information about how patients can file complaints',
      context: 'Must include both internal and HHS complaint options',
      confidence: 100,
    });
  }

  // Accessibility score
  if (accessibilityScore >= 80) {
    confidence += 10;
  }

  // Determine if passed
  if (contactInfo.methods.length > 0 && privacyOfficer.found && complaints.found) {
    passed = true;
  }

  return { passed, confidence: Math.min(confidence, 100), findings };
}

// Helper interfaces
interface ContactMethod {
  type: 'phone' | 'email' | 'address';
  value: string;
  context: string;
  accessible: boolean;
}

interface ContactInfo {
  methods: ContactMethod[];
  hasPrivacyOfficer: boolean;
  hasComplaintProcedure: boolean;
}

interface PrivacyOfficerInfo {
  found: boolean;
  title: string;
  name: string;
  contact: string;
}

interface ComplaintProcedures {
  found: boolean;
  procedures: string[];
  hasHHSReference: boolean;
}

// Helper functions
async function discoverPrivacyPolicyUrl(targetUrl: string): Promise<string> {
  try {
    const privacyUrls = await URLResolver.findPrivacyPolicyUrls(targetUrl);
    return privacyUrls.length > 0 ? privacyUrls[0] : targetUrl;
  } catch (error) {
    return targetUrl;
  }
}

function extractContext(content: string, searchTerm: string, length: number = 100): string {
  const index = content.toLowerCase().indexOf(searchTerm.toLowerCase());
  if (index === -1) return '';

  const start = Math.max(0, index - length / 2);
  const end = Math.min(content.length, index + searchTerm.length + length / 2);

  return content.substring(start, end).trim();
}

function isContactMethodLabeled(method: ContactMethod, _htmlContent: string): boolean {
  const labels = ['phone', 'email', 'contact', 'address', 'call', 'write'];
  return labels.some((label) => method.context.toLowerCase().includes(label));
}

function isContactMethodFindable(method: ContactMethod, htmlContent: string): boolean {
  // Check if contact method appears in a prominent location
  const prominentSections = ['header', 'footer', 'contact', 'privacy'];
  return prominentSections.some(
    (section) =>
      htmlContent.toLowerCase().includes(`<${section}`) &&
      htmlContent.toLowerCase().includes(method.value.toLowerCase()),
  );
}

function isContactMethodFunctional(method: ContactMethod): boolean {
  if (method.type === 'email') {
    return method.value.includes('@') && method.value.includes('.');
  }
  if (method.type === 'phone') {
    return /\d{10,}/.test(method.value.replace(/\D/g, ''));
  }
  if (method.type === 'address') {
    return method.value.length > 10;
  }
  return true;
}

function determineContactSeverity(findings: HipaaFinding[]): HipaaSeverity {
  const hasCritical = findings.some((f) => f.severity === HipaaSeverity.CRITICAL);
  const hasHigh = findings.some((f) => f.severity === HipaaSeverity.HIGH);

  if (hasCritical) return HipaaSeverity.CRITICAL;
  if (hasHigh) return HipaaSeverity.HIGH;
  return HipaaSeverity.MEDIUM;
}

function generateContactRemediation(findings: HipaaFinding[]): HipaaRemediation {
  const steps: string[] = [];
  const resources: HipaaRemediation['resources'] = [];

  if (findings.some((f) => f.message.includes('No contact information'))) {
    steps.push('Add privacy officer contact information including phone number and email address');
    steps.push('Include mailing address for written communications');
  }

  if (findings.some((f) => f.message.includes('Privacy officer not clearly identified'))) {
    steps.push('Clearly identify the designated privacy officer by name and title');
    steps.push('Provide direct contact information for the privacy officer');
  }

  if (findings.some((f) => f.message.includes('No complaint procedures'))) {
    steps.push('Add section explaining how patients can file complaints');
    steps.push('Include information about filing complaints with HHS');
    steps.push('Provide HHS contact information for complaints');
  }

  resources.push({
    title: 'HIPAA Privacy Officer Requirements',
    url: 'https://www.hhs.gov/hipaa/for-professionals/privacy/guidance/privacy-officer/index.html',
    type: 'regulation' as const,
    description: 'HHS guidance on privacy officer designation and responsibilities',
  });

  return {
    priority: 'high' as const,
    effort: 'minimal' as const,
    steps: steps.length > 0 ? steps : ['Review and update contact information'],
    resources,
    timeline: '1-2 days',
    estimatedCost: '$200 - $500',
  };
}

function collectContactEvidence(
  contactInfo: ContactInfo,
  privacyOfficer: PrivacyOfficerInfo,
  complaints: ComplaintProcedures,
): HipaaEvidence[] {
  const evidence: HipaaEvidence[] = [];

  contactInfo.methods.forEach((method, index) => {
    evidence.push({
      type: 'text_excerpt' as const,
      content: `${method.type}: ${method.value}`,
      location: `Contact method ${index + 1}`,
      timestamp: new Date().toISOString(),
      relevance: 90,
    });
  });

  if (privacyOfficer.found) {
    evidence.push({
      type: 'text_excerpt' as const,
      content: `${privacyOfficer.title}${privacyOfficer.name ? ': ' + privacyOfficer.name : ''}`,
      location: 'Privacy officer information',
      timestamp: new Date().toISOString(),
      relevance: 95,
    });
  }

  if (complaints.found) {
    evidence.push({
      type: 'text_excerpt' as const,
      content: complaints.procedures.join('; '),
      location: 'Complaint procedures section',
      timestamp: new Date().toISOString(),
      relevance: 85,
    });
  }

  return evidence;
}

function generateContactSummary(result: HipaaCheckResult): string {
  const { passed, confidence, details } = result;
  const methodsFound = details.metrics.contactMethodsFound;
  const accessibilityScore = details.metrics.accessibilityScore;

  if (passed) {
    return `Contact information validation successful. Found ${methodsFound} contact method(s) with ${accessibilityScore}% accessibility score. Privacy officer and complaint procedures identified. Confidence: ${confidence}%`;
  } else {
    return `Contact information validation failed. ${methodsFound === 0 ? 'No contact methods found.' : `Found ${methodsFound} contact method(s) but missing required elements.`} Confidence: ${confidence}%`;
  }
}
