import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { TrendingUp, Shield, BarChart3, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock } from 'lucide-react';

export interface HipaaOverviewCardProps {
  overallScore: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  lastScanDate: string;
  totalScans: number;
  complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
  loading?: boolean;
}

/**
 * HIPAA Overview Card Component
 * Displays overall HIPAA compliance score and key metrics
 */
export const HipaaOverviewCard: React.FC<HipaaOverviewCardProps> = ({
  overallScore,
  riskLevel,
  lastScanDate,
  totalScans,
  complianceStatus,
  loading = false,
}) => {
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'text-white';
      case 'high':
        return 'text-white';
      case 'medium':
        return 'text-white';
      case 'low':
        return 'text-white';
      default:
        return 'text-white';
    }
  };

  const getRiskLevelBgColor = (level: string) => {
    switch (level) {
      case 'critical':
        return '#DC2626'; // Critical Risk Red from .projectrules
      case 'high':
        return '#EA580C'; // High Risk Orange from .projectrules
      case 'medium':
        return '#D97706'; // Medium Risk Yellow from .projectrules
      case 'low':
        return '#059669'; // Low Risk Green from .projectrules
      default:
        return '#6B7280'; // Gray
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'success';
      case 'partially_compliant':
        return 'warning';
      case 'non_compliant':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getComplianceStatusText = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'Compliant';
      case 'partially_compliant':
        return 'Partially Compliant';
      case 'non_compliant':
        return 'Non-Compliant';
      default:
        return 'Unknown';
    }
  };

  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-5 w-5" />;
      case 'medium':
        return <Shield className="h-5 w-5" />;
      case 'low':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Shield className="h-5 w-5" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <Card className="border-2 border-blue-200 animate-pulse">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="h-5 w-5 bg-gray-200 rounded"></div>
            <div className="h-5 bg-gray-200 rounded w-32"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-6 bg-gray-200 rounded w-20"></div>
            </div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className="border-2 bg-gradient-to-br to-white"
      style={{ borderColor: '#0055A4', backgroundColor: '#F5F5F5' }}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2" style={{ color: '#0055A4' }}>
          <TrendingUp className="h-5 w-5" />
          Overall HIPAA Compliance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Main Score Display */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-4xl font-bold" style={{ color: '#333333' }}>
                {overallScore}%
              </div>
              <div className="flex flex-col gap-1">
                <Badge
                  className={`${getRiskLevelColor(riskLevel)} font-semibold px-3 py-1`}
                  style={{ backgroundColor: getRiskLevelBgColor(riskLevel) }}
                >
                  {getRiskIcon(riskLevel)}
                  <span className="ml-1">{riskLevel.toUpperCase()} RISK</span>
                </Badge>
                <Badge
                  variant={
                    getComplianceStatusColor(complianceStatus) as
                      | 'success'
                      | 'warning'
                      | 'destructive'
                      | 'secondary'
                  }
                >
                  {getComplianceStatusText(complianceStatus)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress value={overallScore} className="h-4 bg-blue-100" />
            <div className="text-sm text-center" style={{ color: '#666666' }}>
              Compliance Score
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-blue-100">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium text-gray-900">{totalScans}</div>
                <div className="text-xs text-gray-600">Total Scans</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" style={{ color: '#0055A4' }} />
              <div>
                <div className="text-sm font-medium" style={{ color: '#333333' }}>
                  {formatDate(lastScanDate)}
                </div>
                <div className="text-xs" style={{ color: '#666666' }}>
                  Last Scan
                </div>
              </div>
            </div>
          </div>

          {/* Compliance Breakdown */}
          <div className="pt-4 border-t" style={{ borderColor: '#E5E7EB' }}>
            <div className="text-sm font-medium mb-2" style={{ color: '#333333' }}>
              Compliance Breakdown
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">Privacy Policy:</span>
                <span className="font-medium">82%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Security:</span>
                <span className="font-medium">88%</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
