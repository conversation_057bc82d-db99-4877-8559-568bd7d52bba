import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Enable pgcrypto extension for UUID generation
  await knex.raw('CREATE EXTENSION IF NOT EXISTS "pgcrypto"');

  // Create Users table
  await knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('keycloak_id').unique().notNullable();
    table.string('email').unique().notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
  });

  // Create Scans table
  await knex.schema.createTable('scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE').notNullable();
    table.string('url').notNullable();
    table.string('status').defaultTo('pending').notNullable(); // e.g., pending, running, completed, failed
    table.jsonb('standards_scanned'); // e.g., ['gdpr', 'hipaa']
    table.jsonb('summary_report');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
    table.timestamp('completed_at');
  });

  // Create ComplianceFindings table
  await knex.schema.createTable('compliance_findings', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').references('id').inTable('scans').onDelete('CASCADE').notNullable();
    table.string('standard').notNullable(); // e.g., 'gdpr', 'hipaa', 'wcag'
    table.string('check_id').notNullable(); // Identifier for the specific check performed
    table.text('description').notNullable();
    table.boolean('passed').notNullable();
    table.string('severity'); // e.g., 'critical', 'high', 'medium', 'low'
    table.jsonb('details'); // Additional details about the finding
    table.text('remediation_suggestion');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  // Drop tables in reverse order of creation due to foreign key constraints
  await knex.schema.dropTableIfExists('compliance_findings');
  await knex.schema.dropTableIfExists('scans');
  await knex.schema.dropTableIfExists('users');
  // Optionally, drop the pgcrypto extension if it's not needed by other parts of the DB
  // await knex.raw('DROP EXTENSION IF EXISTS "pgcrypto"');
}
