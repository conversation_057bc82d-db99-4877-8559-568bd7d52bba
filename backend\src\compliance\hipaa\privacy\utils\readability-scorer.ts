// backend/src/compliance/hipaa/privacy/utils/readability-scorer.ts

import { READABILITY_STANDARDS } from '../constants';

/**
 * Complex term with suggested alternative
 */
export interface ComplexTerm {
  term: string;
  suggestion: string;
  location: number;
  context: string;
}

/**
 * Plain language assessment result
 */
export interface PlainLanguageScore {
  score: number; // 0-100
  fleschScore: number;
  averageWordsPerSentence: number;
  averageSyllablesPerWord: number;
  complexTermsCount: number;
  readingLevel: string;
  recommendations: string[];
}

/**
 * Readability analysis utilities for HIPAA plain language requirements
 * Ensures privacy policies are written in language patients can understand
 */
export class ReadabilityScorer {
  /**
   * Calculates Flesch Reading Ease score
   * Higher scores indicate easier readability
   */
  static calculateFleschScore(text: string): number {
    const sentences = this.countSentences(text);
    const words = this.countWords(text);
    const syllables = this.countSyllables(text);

    if (sentences === 0 || words === 0) return 0;

    const averageSentenceLength = words / sentences;
    const averageSyllablesPerWord = syllables / words;

    // Flesch Reading Ease formula
    const score = 206.835 - 1.015 * averageSentenceLength - 84.6 * averageSyllablesPerWord;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Assesses overall plain language compliance
   */
  static assessPlainLanguage(text: string): PlainLanguageScore {
    const fleschScore = this.calculateFleschScore(text);
    const sentences = this.countSentences(text);
    const words = this.countWords(text);
    const syllables = this.countSyllables(text);
    const complexTerms = this.identifyComplexTerms(text);

    const averageWordsPerSentence = sentences > 0 ? words / sentences : 0;
    const averageSyllablesPerWord = words > 0 ? syllables / words : 0;

    // Calculate overall plain language score
    let score = fleschScore;

    // Adjust score based on sentence length
    if (averageWordsPerSentence > READABILITY_STANDARDS.SENTENCE_LENGTH.MAX_AVERAGE) {
      score -= 10;
    }

    // Adjust score based on complex terms
    if (complexTerms.length > READABILITY_STANDARDS.COMPLEX_TERMS.MAX_ALLOWED) {
      score -= Math.min(
        20,
        (complexTerms.length - READABILITY_STANDARDS.COMPLEX_TERMS.MAX_ALLOWED) * 2,
      );
    }

    const readingLevel = this.getReadingLevel(fleschScore);
    const recommendations = this.generateRecommendations(
      fleschScore,
      averageWordsPerSentence,
      complexTerms,
    );

    return {
      score: Math.max(0, Math.round(score)),
      fleschScore,
      averageWordsPerSentence,
      averageSyllablesPerWord,
      complexTermsCount: complexTerms.length,
      readingLevel,
      recommendations,
    };
  }

  /**
   * Identifies complex terms that should be simplified
   */
  static identifyComplexTerms(text: string): ComplexTerm[] {
    const complexTerms: ComplexTerm[] = [];
    const alternatives = READABILITY_STANDARDS.COMPLEX_TERMS.ALTERNATIVES;

    // Check for predefined complex terms
    for (const [complexTerm, simpleTerm] of Object.entries(alternatives)) {
      const regex = new RegExp(`\\b${complexTerm}\\b`, 'gi');
      let match;

      while ((match = regex.exec(text)) !== null) {
        const context = this.extractContext(text, match.index, 50);

        complexTerms.push({
          term: match[0],
          suggestion: simpleTerm,
          location: match.index,
          context,
        });
      }
    }

    // Check for other complex patterns
    const complexPatterns = [
      {
        pattern: /\b\w{12,}\b/g, // Very long words (12+ characters)
        getSuggestion: (_term: string) => 'Consider using simpler language',
      },
      {
        pattern: /\b(?:utilize|implement|facilitate|demonstrate|accommodate)\b/gi,
        getSuggestion: (term: string) => {
          const suggestions: Record<string, string> = {
            utilize: 'use',
            implement: 'put in place',
            facilitate: 'help',
            demonstrate: 'show',
            accommodate: 'help with',
          };
          return suggestions[term.toLowerCase()] || 'use simpler language';
        },
      },
    ];

    for (const pattern of complexPatterns) {
      let match;
      while ((match = pattern.pattern.exec(text)) !== null) {
        const context = this.extractContext(text, match.index, 50);

        complexTerms.push({
          term: match[0],
          suggestion: pattern.getSuggestion(match[0]),
          location: match.index,
          context,
        });
      }
    }

    // Remove duplicates and sort by location
    const uniqueTerms = complexTerms.filter(
      (term, index, array) => array.findIndex((t) => t.location === term.location) === index,
    );

    return uniqueTerms.sort((a, b) => a.location - b.location);
  }

  /**
   * Gets simpler alternatives for complex terms
   */
  static getSimplerAlternatives(complexTerm: string): string[] {
    const alternatives = READABILITY_STANDARDS.COMPLEX_TERMS.ALTERNATIVES;
    const lowerTerm = complexTerm.toLowerCase();

    // Check predefined alternatives
    if (lowerTerm in alternatives) {
      return [alternatives[lowerTerm as keyof typeof alternatives]];
    }

    // Generate suggestions for common complex patterns
    const suggestions: string[] = [];

    if (lowerTerm.endsWith('tion')) {
      const root = lowerTerm.slice(0, -4);
      suggestions.push(`${root}ing`, `${root}`, 'process');
    }

    if (lowerTerm.endsWith('ment')) {
      const root = lowerTerm.slice(0, -4);
      suggestions.push(`${root}ing`, `${root}`, 'action');
    }

    if (lowerTerm.startsWith('pre')) {
      suggestions.push('before', 'early');
    }

    if (lowerTerm.startsWith('post')) {
      suggestions.push('after', 'later');
    }

    return suggestions.length > 0 ? suggestions : ['use simpler language'];
  }

  /**
   * Counts sentences in text
   */
  private static countSentences(text: string): number {
    // Split by sentence-ending punctuation
    const sentences = text
      .split(/[.!?]+/)
      .filter((sentence) => sentence.trim().length > 0 && /\w/.test(sentence));
    return sentences.length;
  }

  /**
   * Counts words in text
   */
  private static countWords(text: string): number {
    const words = text.split(/\s+/).filter((word) => word.trim().length > 0 && /\w/.test(word));
    return words.length;
  }

  /**
   * Counts syllables in text
   */
  private static countSyllables(text: string): number {
    const words = text.split(/\s+/).filter((word) => /\w/.test(word));
    let totalSyllables = 0;

    for (const word of words) {
      totalSyllables += this.countSyllablesInWord(word.toLowerCase());
    }

    return totalSyllables;
  }

  /**
   * Counts syllables in a single word
   */
  private static countSyllablesInWord(word: string): number {
    // Remove non-alphabetic characters
    word = word.replace(/[^a-z]/g, '');

    if (word.length === 0) return 0;
    if (word.length <= 3) return 1;

    // Count vowel groups
    let syllables = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = /[aeiouy]/.test(word[i]);

      if (isVowel && !previousWasVowel) {
        syllables++;
      }

      previousWasVowel = isVowel;
    }

    // Handle silent 'e'
    if (word.endsWith('e') && syllables > 1) {
      syllables--;
    }

    // Ensure at least 1 syllable
    return Math.max(1, syllables);
  }

  /**
   * Determines reading level based on Flesch score
   */
  private static getReadingLevel(fleschScore: number): string {
    if (fleschScore >= 90) return '5th grade';
    if (fleschScore >= 80) return '6th grade';
    if (fleschScore >= 70) return '7th grade';
    if (fleschScore >= 60) return '8th-9th grade';
    if (fleschScore >= 50) return '10th-12th grade';
    if (fleschScore >= 30) return 'College level';
    return 'Graduate level';
  }

  /**
   * Generates recommendations for improving readability
   */
  private static generateRecommendations(
    fleschScore: number,
    averageWordsPerSentence: number,
    complexTerms: ComplexTerm[],
  ): string[] {
    const recommendations: string[] = [];

    // Flesch score recommendations
    if (fleschScore < READABILITY_STANDARDS.FLESCH_SCORE.MINIMUM) {
      recommendations.push('Simplify language to reach at least 8th grade reading level');
    }

    // Sentence length recommendations
    if (averageWordsPerSentence > READABILITY_STANDARDS.SENTENCE_LENGTH.MAX_AVERAGE) {
      recommendations.push(
        `Shorten sentences (current average: ${averageWordsPerSentence.toFixed(1)} words, target: ${READABILITY_STANDARDS.SENTENCE_LENGTH.MAX_AVERAGE} words)`,
      );
    }

    // Complex terms recommendations
    if (complexTerms.length > READABILITY_STANDARDS.COMPLEX_TERMS.MAX_ALLOWED) {
      recommendations.push(
        `Replace ${complexTerms.length} complex terms with simpler alternatives`,
      );
    }

    // Specific improvement suggestions
    if (fleschScore < 50) {
      recommendations.push('Use active voice instead of passive voice');
      recommendations.push('Break up long paragraphs into shorter ones');
      recommendations.push('Use bullet points for lists and procedures');
    }

    if (recommendations.length === 0) {
      recommendations.push('Text meets plain language requirements');
    }

    return recommendations;
  }

  /**
   * Extracts context around a specific location in text
   */
  private static extractContext(text: string, position: number, contextLength: number): string {
    const start = Math.max(0, position - contextLength);
    const end = Math.min(text.length, position + contextLength);

    let context = text.substring(start, end);

    // Add ellipsis if truncated
    if (start > 0) context = '...' + context;
    if (end < text.length) context = context + '...';

    return context.trim();
  }
}
