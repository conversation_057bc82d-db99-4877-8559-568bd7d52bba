'use client';

import { useTheme } from 'next-themes';
import { getThemeColors, type ThemeName, type ThemeColors } from '@/lib/theme-config';
import { useMemo } from 'react';

/**
 * Hook to get current theme colors based on the active theme
 * Provides easy access to design system colors that automatically adapt to theme changes
 */
export function useThemeColors(): ThemeColors & {
  themeName: ThemeName;
  isDark: boolean;
  isStandard: boolean;
  isSystem: boolean;
} {
  const { theme, resolvedTheme } = useTheme();

  const themeColors = useMemo(() => {
    const currentTheme = (resolvedTheme || theme || 'standard') as ThemeName;
    return getThemeColors(currentTheme);
  }, [theme, resolvedTheme]);

  const themeName = (resolvedTheme || theme || 'standard') as ThemeName;
  const isDark = resolvedTheme === 'dark';
  const isStandard = resolvedTheme === 'standard' || (!resolvedTheme && theme === 'standard');
  const isSystem = theme === 'system';

  return {
    ...themeColors,
    themeName,
    isDark,
    isStandard,
    isSystem,
  };
}

/**
 * Hook to get CSS custom properties for the current theme
 * Returns an object with CSS variable names and their current values
 */
export function useThemeCSSVariables(): Record<string, string> {
  const colors = useThemeColors();

  return useMemo(
    () => ({
      '--color-primary': colors.primary,
      '--color-primary-light': colors.primaryLight,
      '--color-primary-dark': colors.primaryDark,
      '--color-primary-foreground': colors.primaryForeground,
      '--color-accent': colors.accent,
      '--color-accent-light': colors.accentLight,
      '--color-accent-dark': colors.accentDark,
      '--color-accent-foreground': colors.accentForeground,
      '--color-background': colors.background,
      '--color-background-secondary': colors.backgroundSecondary,
      '--color-background-tertiary': colors.backgroundTertiary,
      '--color-text-primary': colors.textPrimary,
      '--color-text-secondary': colors.textSecondary,
      '--color-text-tertiary': colors.textTertiary,
      '--color-border': colors.border,
      '--color-border-light': colors.borderLight,
      '--color-border-dark': colors.borderDark,
      '--color-success': colors.success,
      '--color-success-foreground': colors.successForeground,
      '--color-warning': colors.warning,
      '--color-warning-foreground': colors.warningForeground,
      '--color-error': colors.error,
      '--color-error-foreground': colors.errorForeground,
      '--color-info': colors.info,
      '--color-info-foreground': colors.infoForeground,
      '--color-card': colors.card,
      '--color-card-foreground': colors.cardForeground,
      '--color-input': colors.input,
      '--color-input-foreground': colors.inputForeground,
      '--color-muted': colors.muted,
      '--color-muted-foreground': colors.mutedForeground,
      '--color-hover': colors.hover,
      '--color-focus': colors.focus,
      '--color-active': colors.active,
    }),
    [colors],
  );
}

/**
 * Hook to get status colors for the current theme
 * Useful for badges, alerts, and status indicators
 */
export function useStatusColors() {
  const colors = useThemeColors();

  return useMemo(
    () => ({
      success: {
        background: colors.success,
        foreground: colors.successForeground,
        className: 'bg-green-500 text-white',
      },
      warning: {
        background: colors.warning,
        foreground: colors.warningForeground,
        className: 'bg-orange-500 text-white',
      },
      error: {
        background: colors.error,
        foreground: colors.errorForeground,
        className: 'bg-red-500 text-white',
      },
      info: {
        background: colors.info,
        foreground: colors.infoForeground,
        className: 'bg-blue-500 text-white',
      },
      // Risk level colors for HIPAA compliance - Project Rules Compliant
      critical: {
        background: '#DC2626', // Critical Risk Red (Project Rules)
        foreground: '#FFFFFF',
        className: 'bg-red-600 text-white',
      },
      high: {
        background: '#EA580C', // High Risk Orange (Project Rules)
        foreground: '#FFFFFF',
        className: 'bg-orange-600 text-white',
      },
      medium: {
        background: '#D97706', // Medium Risk Yellow (Project Rules)
        foreground: '#FFFFFF',
        className: 'bg-yellow-600 text-white',
      },
      low: {
        background: '#059669', // Low Risk Green (Project Rules)
        foreground: '#FFFFFF',
        className: 'bg-green-600 text-white',
      },
    }),
    [colors],
  );
}

/**
 * Hook to get button styles for the current theme
 * Provides consistent button styling across the application
 */
export function useButtonStyles() {
  const colors = useThemeColors();

  return useMemo(
    () => ({
      primary: {
        style: {
          backgroundColor: colors.primary,
          color: colors.primaryForeground,
          border: 'none',
        },
        className: 'hover:opacity-90 transition-all duration-200 shadow-md',
        hoverStyle: {
          backgroundColor: colors.primaryLight,
        },
      },
      secondary: {
        style: {
          backgroundColor: 'transparent',
          color: colors.primary,
          border: `1px solid ${colors.primary}`,
        },
        className: 'hover:bg-opacity-10 transition-all duration-200',
        hoverStyle: {
          backgroundColor: colors.hover,
        },
      },
      accent: {
        style: {
          backgroundColor: colors.accent,
          color: colors.accentForeground,
          border: 'none',
        },
        className: 'hover:opacity-90 transition-all duration-200 shadow-md',
        hoverStyle: {
          backgroundColor: colors.accentLight,
        },
      },
      ghost: {
        style: {
          backgroundColor: 'transparent',
          color: colors.textPrimary,
          border: 'none',
        },
        className: 'hover:bg-opacity-10 transition-all duration-200',
        hoverStyle: {
          backgroundColor: colors.hover,
        },
      },
    }),
    [colors],
  );
}

/**
 * Hook to get card styles for the current theme
 * Provides consistent card styling across the application
 */
export function useCardStyles() {
  const colors = useThemeColors();

  return useMemo(
    () => ({
      default: {
        style: {
          backgroundColor: colors.card,
          color: colors.cardForeground,
          border: `1px solid ${colors.border}`,
          borderRadius: '0.5rem',
        },
        className: 'shadow-sm hover:shadow-md transition-shadow duration-200',
      },
      elevated: {
        style: {
          backgroundColor: colors.card,
          color: colors.cardForeground,
          border: `1px solid ${colors.border}`,
          borderRadius: '0.5rem',
        },
        className: 'shadow-md hover:shadow-lg transition-shadow duration-200',
      },
      muted: {
        style: {
          backgroundColor: colors.muted,
          color: colors.mutedForeground,
          border: `1px solid ${colors.borderLight}`,
          borderRadius: '0.5rem',
        },
        className: 'shadow-sm',
      },
    }),
    [colors],
  );
}

/**
 * Hook to get text styles for the current theme
 * Provides consistent typography styling across the application
 */
export function useTextStyles() {
  const colors = useThemeColors();

  return useMemo(
    () => ({
      primary: {
        style: { color: colors.textPrimary },
        className: 'font-medium',
      },
      secondary: {
        style: { color: colors.textSecondary },
        className: 'font-normal',
      },
      tertiary: {
        style: { color: colors.textTertiary },
        className: 'font-normal text-sm',
      },
      heading: {
        style: { color: colors.textPrimary },
        className: 'font-semibold',
      },
      muted: {
        style: { color: colors.mutedForeground },
        className: 'font-normal text-sm',
      },
    }),
    [colors],
  );
}

/**
 * Utility function to get risk level color
 * Maintains backward compatibility while using theme colors
 */
export function getRiskLevelColor(riskLevel: 'critical' | 'high' | 'medium' | 'low'): string {
  const statusColors = useStatusColors();
  return statusColors[riskLevel]?.className || 'bg-gray-500 text-white';
}
