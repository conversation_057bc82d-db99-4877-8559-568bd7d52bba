// backend/src/routes/compliance/scan.ts
import express, { Router, Request, Response } from 'express';
import { Scan, ComplianceFinding, ScanWithFindings } from '../../types'; // Import relevant types
import { HipaaScanResult, HipaaCheckResult } from '../../compliance/hipaa/privacy/types';
import { keycloak } from '../../lib/keycloak';
import { CreateScanSchema, GetScanParamsSchema } from '../../lib/validators/scan-validators';
import { ERROR_MESSAGES } from '../../lib/constants';
import ScanService, { UserNotFoundError, ScanProcessingError } from '../../services/scan-service';
import db from '../../lib/db';
import logger from '../../utils/logger';
import { HipaaDatabase } from '../../compliance/hipaa/privacy/database/hipaa-privacy-db';
// We use Zod for validation but don't need ZodError directly

const router: Router = express.Router();

// Type definition for level results
interface LevelResult {
  level: number;
  method: string;
  score: string | number;
  confidence: number;
  processingTime: number;
  findings?: unknown[];
  foundPatterns?: number;
  totalPatterns?: number;
  entities?: unknown;
  privacyStatements?: unknown[];
  rightsStatements?: unknown[];
  identifiedGaps?: unknown[];
  riskFactors?: unknown[];
  recommendations?: unknown[];
  positiveFindings?: unknown[];
}

// Define interfaces for Keycloak token and authenticated request
// TODO: Consider moving these to a shared types file (e.g., src/types/auth-types.ts)
interface KeycloakTokenContent {
  sub: string;
  email?: string;
  // Add other fields you expect from your Keycloak token
}

interface AuthenticatedRequest extends Request {
  kauth?: {
    grant?: {
      access_token?: {
        content: KeycloakTokenContent;
      };
    };
  };
}

/**
 * @openapi
 * /compliance/scans:
 *   post:
 *     summary: Submit a new compliance scan
 *     description: Accepts a URL and a list of standards to scan against. Creates a new scan record and initiates the scanning process.
 *     tags: [Scans]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 format: url
 *                 example: "https://example.com"
 *               standards:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["hipaa", "gdpr"]
 *                 minItems: 1
 *     responses:
 *       201:
 *         description: Scan submitted successfully, processing initiated.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScanWithFindings'
 *       400:
 *         description: Invalid input (e.g., invalid URL, no standards selected).
 *       401:
 *         description: Unauthorized (e.g., missing or invalid token).
 *       404:
 *         description: User not found.
 *       500:
 *         description: Internal server error or scan processing error.
 */
router.post('/', keycloak.protect(), async (req: AuthenticatedRequest, res: Response) => {
  // req.kauth.grant.access_token.content is typed via AuthenticatedRequest
  const userKeycloakId = req.kauth?.grant?.access_token?.content?.sub;

  if (!userKeycloakId) {
    return res.status(401).json({ message: ERROR_MESSAGES.UNAUTHORIZED });
  }

  const validationResult = CreateScanSchema.safeParse(req.body);
  if (!validationResult.success) {
    return res.status(400).json({
      message: ERROR_MESSAGES.INVALID_INPUT,
      errors: validationResult.error.format(),
    });
  }
  const { url, standards } = validationResult.data;

  try {
    const newScan = await ScanService.initiateNewScan(userKeycloakId, url, standards);
    return res.status(201).json(newScan);
  } catch (error: unknown) {
    if (error instanceof UserNotFoundError) {
      logger.warn('User not found during scan creation attempt.', {
        keycloakId: userKeycloakId,
        errorMessage: error.message,
      });
      return res.status(404).json({ message: error.message });
    }
    if (error instanceof ScanProcessingError) {
      logger.error('Scan processing error during scan creation.', {
        message: error.message,
        scanId: error.scanId,
        keycloakId: userKeycloakId,
      });
      return res.status(500).json({ message: error.message, scanId: error.scanId });
    }
    // Generic error logging for unexpected errors
    const errorDetails: Record<string, unknown> = {
      keycloakId: userKeycloakId,
      requestBody: req.body,
    };
    if (error instanceof Error) {
      errorDetails.originalErrorName = error.name;
      errorDetails.originalErrorMessage = error.message;
      // errorDetails.stack = error.stack; // Server-side only, if needed
    } else {
      errorDetails.unknownError = String(error); // Convert unknown error to string for logging
    }
    logger.error('Unexpected error initiating scan.', errorDetails);
    // For the client, send a generic error message unless it's a ScanProcessingError (handled above)
    return res.status(500).json({ message: ERROR_MESSAGES.SCAN_CREATION_FAILED });
  }
});

/**
 * @openapi
 * /compliance/scans:
 *   get:
 *     summary: Retrieve all compliance scans for the authenticated user
 *     description: Returns a list of all scans initiated by the user, including their current status and summary.
 *     tags: [Scans]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of scans.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Scan'
 *       401:
 *         description: Unauthorized.
 *       404:
 *         description: User not found.
 *       500:
 *         description: Internal server error.
 */
router.get('/', keycloak.protect(), async (req: AuthenticatedRequest, res: Response) => {
  // req.kauth.grant.access_token.content is typed via AuthenticatedRequest
  const userKeycloakId = req.kauth?.grant?.access_token?.content?.sub;

  if (!userKeycloakId) {
    return res.status(401).json({ message: ERROR_MESSAGES.UNAUTHORIZED });
  }

  try {
    // Using initiateNewScan as a temporary replacement for getAllScans which doesn't exist in ScanService
    // TODO: Implement getAllScans in ScanService
    const scans = await db<Scan>('scans')
      .join('users', 'scans.user_id', '=', 'users.id')
      .where('users.keycloak_id', userKeycloakId)
      .select('scans.*') // Select all columns from the scans table
      .orderBy('scans.created_at', 'desc');
    return res.status(200).json(scans);
  } catch (error: unknown) {
    if (error instanceof UserNotFoundError) {
      logger.warn('User not found when fetching all scans.', {
        keycloakId: userKeycloakId,
        errorMessage: error.message,
      });
      return res.status(404).json({ message: error.message });
    }
    // Generic error logging for other unexpected errors
    const errorDetails: Record<string, unknown> = { keycloakId: userKeycloakId };
    if (error instanceof Error) {
      errorDetails.originalErrorName = error.name;
      errorDetails.originalErrorMessage = error.message;
    } else {
      errorDetails.unknownError = String(error);
    }
    logger.error('Error fetching all scans for user.', errorDetails);
    return res.status(500).json({ message: ERROR_MESSAGES.FETCH_SCANS_FAILED });
  }
});

/**
 * @openapi
 * /compliance/scans/{scanId}:
 *   get:
 *     summary: Retrieve a specific compliance scan by ID
 *     description: Returns detailed information about a single scan, including findings, if the user is authorized.
 *     tags: [Scans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid # Assuming scan IDs are UUIDs
 *         description: The ID of the scan to retrieve.
 *     responses:
 *       200:
 *         description: Detailed information about the scan.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScanWithFindings'
 *       400:
 *         description: Invalid scan ID format.
 *       401:
 *         description: Unauthorized.
 *       404:
 *         description: User not found or Scan not found for this user.
 *       500:
 *         description: Internal server error.
 */
router.get('/:scanId', keycloak.protect(), async (req: AuthenticatedRequest, res: Response) => {
  // req.kauth.grant.access_token.content is typed via AuthenticatedRequest
  const userKeycloakId = req.kauth?.grant?.access_token?.content?.sub;

  if (!userKeycloakId) {
    return res.status(401).json({ message: ERROR_MESSAGES.UNAUTHORIZED });
  }

  const paramsValidationResult = GetScanParamsSchema.safeParse(req.params);
  if (!paramsValidationResult.success) {
    return res.status(400).json({
      message: ERROR_MESSAGES.INVALID_SCAN_ID_FORMAT,
      errors: paramsValidationResult.error.format(),
    });
  }
  const { scanId } = paramsValidationResult.data;

  try {
    // Using direct database query as a temporary replacement for getScanById which doesn't exist in ScanService
    // TODO: Implement getScanById in ScanService
    const scan = await db<Scan>('scans')
      .join('users', 'scans.user_id', '=', 'users.id')
      .where({ 'scans.id': scanId, 'users.keycloak_id': userKeycloakId })
      .select('scans.*') // Select all columns from the scans table
      .first();
    let scanWithFindings = null;
    if (scan) {
      const findings = await db<ComplianceFinding>('compliance_findings')
        .where({ scan_id: scanId })
        .select('*');

      // Try to get enhanced HIPAA results if this is a HIPAA scan
      let enhancedHipaaResults = null;
      try {
        // DIRECT FIX: Always try to retrieve HIPAA results regardless of standards parsing
        console.log(
          `🔧 [DIRECT FIX] Always attempting to retrieve enhanced HIPAA results for scan: ${scanId}`,
        );

        // Get the HIPAA scan record to find the hipaa_scan_id
        const hipaaScanRecord = await db('hipaa_scans').where({ scan_id: scanId }).first();

        console.log(`🔧 [DIRECT FIX] HIPAA scan record query result:`, {
          found: !!hipaaScanRecord,
          scanId: scanId,
          hipaaScanRecord: hipaaScanRecord
            ? {
                id: hipaaScanRecord.id,
                overall_score: hipaaScanRecord.overall_score,
                compliance_level: hipaaScanRecord.compliance_level,
                total_checks: hipaaScanRecord.total_checks,
                created_at: hipaaScanRecord.created_at,
              }
            : null,
        });

        if (hipaaScanRecord) {
          console.log(`🔧 [DIRECT FIX] Found HIPAA scan record, retrieving enhanced results...`);

          try {
            enhancedHipaaResults = await HipaaDatabase.getScanResult(hipaaScanRecord.id);

            if (enhancedHipaaResults) {
              console.log(`🎯 [DIRECT FIX] SUCCESS! Enhanced HIPAA results retrieved:`, {
                overallScore: enhancedHipaaResults.overallScore,
                checksCount: enhancedHipaaResults.checks?.length || 0,
                recommendationsCount: enhancedHipaaResults.recommendations?.length || 0,
                complianceLevel: enhancedHipaaResults.summary?.complianceLevel,
              });
            } else {
              console.log(`❌ [DIRECT FIX] HipaaDatabase.getScanResult returned null`);
            }
          } catch (dbError) {
            console.error(`❌ [DIRECT FIX] Error retrieving enhanced results:`, {
              error: dbError instanceof Error ? dbError.message : String(dbError),
              hipaaScanId: hipaaScanRecord.id,
              stack: dbError instanceof Error ? dbError.stack : undefined,
            });
          }
        } else {
          console.log(`⚠️ [DIRECT FIX] No HIPAA scan record found for scan: ${scanId}`);

          // Debug: Check if any HIPAA scans exist at all
          const allHipaaScans = await db('hipaa_scans').select('*').limit(3);
          console.log(`🔧 [DIRECT FIX] Total HIPAA scans in database: ${allHipaaScans.length}`);
          if (allHipaaScans.length > 0) {
            console.log(
              `🔧 [DIRECT FIX] Sample HIPAA scan records:`,
              allHipaaScans.map((s) => ({
                id: s.id,
                scan_id: s.scan_id,
                target_url: s.target_url,
                created_at: s.created_at,
              })),
            );
          }
        }

        // LEGACY CODE (for reference, but not used anymore)
        /*
        // Handle both JSON array and string formats for standards_scanned
        let standards: string[] = [];
        console.log(`🔍 [Standards Debug] Raw standards_scanned value:`, scan.standards_scanned);
        console.log(`🔍 [Standards Debug] Type of standards_scanned:`, typeof scan.standards_scanned);

        try {
          standards = JSON.parse(scan.standards_scanned || '[]');
          console.log(`🔍 [Standards Debug] Parsed standards array:`, standards);
        } catch (parseError) {
          // If JSON parsing fails, treat as a single standard string
          console.log(`🔍 [Standards Debug] JSON parse failed, treating as string:`, parseError);
          if (scan.standards_scanned) {
            standards = [scan.standards_scanned];
          }
          console.log(`🔍 [Standards Debug] Standards as string array:`, standards);
        }

        console.log(`🔍 [Standards Debug] Final standards array:`, standards);
        console.log(`🔍 [Standards Debug] Checking if includes 'hipaa':`, standards.includes('hipaa'));

        if (standards.includes('hipaa')) {
        */
      } catch (error) {
        console.error(`❌ [Enhanced HIPAA Retrieval] Top-level error:`, error);
        logger.warn('Failed to retrieve enhanced HIPAA results:', {
          error: error instanceof Error ? error.message : String(error),
        });
      }

      scanWithFindings = {
        ...scan,
        findings,
        enhancedHipaaResults,
      } as ScanWithFindings & { enhancedHipaaResults?: HipaaScanResult };

      // Comprehensive debug logging for frontend result generation
      console.log('🌐 [Frontend API Response] Preparing scan response for frontend:', {
        scanId,
        url: scan.url,
        status: scan.status,
        hasEnhancedResults: !!enhancedHipaaResults,
        enhancedResultsStructure: enhancedHipaaResults
          ? {
              overallScore: enhancedHipaaResults.overallScore,
              overallPassed: enhancedHipaaResults.overallPassed,
              checksCount: enhancedHipaaResults.checks?.length || 0,
              recommendationsCount: enhancedHipaaResults.recommendations?.length || 0,
              complianceLevel: enhancedHipaaResults.summary?.complianceLevel,
              analysisLevelsUsed: enhancedHipaaResults.summary?.analysisLevelsUsed || [1, 2, 3],
              processingTime: enhancedHipaaResults.metadata?.processingTime,
            }
          : null,
        basicFindingsCount: findings.length,
        scanTimestamp: scan.created_at,
      });

      // Additional logging for enhanced results content
      if (enhancedHipaaResults) {
        console.log('📊 [Frontend API Response] Enhanced HIPAA results details:', {
          targetUrl: enhancedHipaaResults.targetUrl,
          overallScore: enhancedHipaaResults.overallScore,
          overallPassed: enhancedHipaaResults.overallPassed,
          summary: {
            totalChecks: enhancedHipaaResults.summary?.totalChecks,
            passedChecks: enhancedHipaaResults.summary?.passedChecks,
            failedChecks: enhancedHipaaResults.summary?.failedChecks,
            complianceLevel: enhancedHipaaResults.summary?.complianceLevel,
            analysisLevelsUsed: enhancedHipaaResults.summary?.analysisLevelsUsed || [1, 2, 3],
          },
          checksBreakdown:
            enhancedHipaaResults.checks?.map((check: HipaaCheckResult) => {
              console.log(`🔍 [API Debug] Processing check: ${check.checkId}`);
              console.log(`🔍 [API Debug] Check levelResults:`, check.levelResults);
              console.log(`🔍 [API Debug] Check levelResults type:`, typeof check.levelResults);
              console.log(
                `🔍 [API Debug] Check levelResults keys:`,
                check.levelResults ? Object.keys(check.levelResults) : 'null',
              );
              console.log(
                `🔍 [API Debug] Check levelResults JSON:`,
                JSON.stringify(check.levelResults, null, 2),
              );

              // FIXED: Properly handle levelResults structure from database
              const analysisLevels = check.levelResults
                ? (Object.values(check.levelResults) as LevelResult[])
                    .filter((level) => level && level.level) // Ensure valid level data
                    .map((level) => {
                      console.log(`🔍 [API Debug] Processing level ${level.level}:`, level);
                      console.log(
                        `🔍 [API Debug] Level score type:`,
                        typeof level.score,
                        'value:',
                        level.score,
                      );

                      return {
                        level: level.level,
                        method: level.method,
                        score: level.score, // Keep as string - frontend will parse
                        confidence: level.confidence,
                        processingTime: level.processingTime,
                        findings: level.findings || [],
                        ...(level.level === 1 && {
                          foundPatterns: level.foundPatterns,
                          totalPatterns: level.totalPatterns,
                        }),
                        ...(level.level === 2 && {
                          entities: level.entities,
                          privacyStatements: level.privacyStatements,
                          rightsStatements: level.rightsStatements,
                        }),
                        ...(level.level === 3 && {
                          identifiedGaps: level.identifiedGaps,
                          riskFactors: level.riskFactors,
                          recommendations: level.recommendations,
                          positiveFindings: level.positiveFindings,
                        }),
                      };
                    })
                : [];

              console.log(
                `🔍 [API Debug] Final analysisLevels for ${check.checkId}:`,
                analysisLevels,
              );
              console.log(
                `🔍 [API Debug] Final analysisLevels JSON:`,
                JSON.stringify(analysisLevels, null, 2),
              );

              return {
                checkId: check.checkId,
                name: check.name,
                passed: check.passed,
                score: check.overallScore,
                confidence: check.confidence,
                analysisLevels,
              };
            }) || [],
          recommendationsCount: enhancedHipaaResults.recommendations?.length || 0,
          metadata: {
            processingTime: enhancedHipaaResults.metadata?.processingTime,
            version: enhancedHipaaResults.metadata?.version,
            analysisLevelsUsed: enhancedHipaaResults.metadata?.analysisLevelsUsed || [1, 2, 3],
          },
        });
      } else {
        console.log('⚠️ [Frontend API Response] No enhanced HIPAA results available for this scan');
      }

      // FINAL DEBUG: Log the complete response structure being sent to frontend
      if (enhancedHipaaResults) {
        console.log('🚀 [FINAL RESPONSE] Complete enhanced HIPAA response being sent to frontend:');
        console.log(
          JSON.stringify(
            {
              enhancedHipaaResults: {
                targetUrl: enhancedHipaaResults.targetUrl,
                overallScore: enhancedHipaaResults.overallScore,
                checksBreakdown:
                  enhancedHipaaResults.checks?.map((check: HipaaCheckResult) => ({
                    checkId: check.checkId,
                    name: check.name,
                    analysisLevels: check.levelResults ? Object.values(check.levelResults) : [],
                  })) || [],
              },
            },
            null,
            2,
          ),
        );
      }

      // Legacy debug logging
      logger.info('Scan retrieval debug info:', {
        scanId,
        hasEnhancedResults: !!enhancedHipaaResults,
        enhancedResultsKeys: enhancedHipaaResults ? Object.keys(enhancedHipaaResults) : [],
        scanStatus: scan.status,
        findingsCount: findings.length,
      });
    }

    if (!scanWithFindings) {
      // This covers both scan not found and scan not belonging to user,
      // as per ScanService.getScanByIdForUser logic.
      logger.info('Scan not found or does not belong to user.', {
        scanId,
        keycloakId: userKeycloakId,
      });
      return res.status(404).json({ message: ERROR_MESSAGES.SCAN_NOT_FOUND });
    }
    return res.status(200).json(scanWithFindings);
  } catch (error: unknown) {
    if (error instanceof UserNotFoundError) {
      logger.warn('User not found when fetching specific scan.', {
        scanId,
        keycloakId: userKeycloakId,
        errorMessage: error.message,
      });
      return res.status(404).json({ message: error.message });
    }
    // Other errors (e.g., database connection issues within the service)
    const errorDetails: Record<string, unknown> = { scanId, keycloakId: userKeycloakId };
    if (error instanceof Error) {
      errorDetails.originalErrorName = error.name;
      errorDetails.originalErrorMessage = error.message;
    } else {
      errorDetails.unknownError = String(error);
    }
    logger.error(`Error fetching scan ${scanId}.`, errorDetails);
    return res.status(500).json({ message: ERROR_MESSAGES.FETCH_SCAN_FAILED });
  }
});

export default router;
