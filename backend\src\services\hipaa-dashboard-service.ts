import { HipaaPrivacyService } from './hipaa-privacy-service';
import { HipaaSecurityService } from './hipaa-security-service';

// Dashboard-specific types
export interface ScanResult {
  id?: string;
  scanId?: string;
  targetUrl: string;
  score?: number;
  overallScore?: number;
  status: string;
  timestamp: string;
  riskLevel?: string;
  summary?: {
    overallScore: number;
  };
}

export interface HipaaDashboardData {
  overview: {
    overallScore: number;
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
    complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
    lastScanDate: string;
    totalScans: number;
  };
  privacyModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: unknown[];
  };
  securityModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: unknown[];
  };
  recentActivity: ScanActivity[];
}

export interface ScanActivity {
  id: string;
  type: 'privacy' | 'security';
  url: string;
  timestamp: string;
  score: number;
  status: 'completed' | 'failed' | 'running';
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
}

export interface DashboardMetrics {
  totalScans: number;
  averageScore: number;
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  complianceRate: number;
  trendsData: {
    period: string;
    score: number;
    scans: number;
  }[];
}

/**
 * HIPAA Dashboard Service
 * Aggregates data from privacy and security services for dashboard display
 */
export class HipaaDashboardService {
  private privacyService: HipaaPrivacyService;
  private securityService: HipaaSecurityService;

  constructor() {
    this.privacyService = new HipaaPrivacyService();
    this.securityService = new HipaaSecurityService();
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<HipaaDashboardData> {
    try {
      // Fetch data from both services in parallel
      const [privacyScans, securityScans] = await Promise.all([
        this.getRecentPrivacyScans(10),
        this.getRecentSecurityScans(10),
      ]);

      // Calculate aggregated metrics
      const overview = this.calculateOverview(privacyScans, securityScans);
      const privacyModule = this.calculateModuleData(privacyScans, 'privacy');
      const securityModule = this.calculateModuleData(securityScans, 'security');
      const recentActivity = this.generateRecentActivity(privacyScans, securityScans);

      return {
        overview,
        privacyModule: {
          ...privacyModule,
          recentScans: privacyScans,
        },
        securityModule: {
          ...securityModule,
          recentScans: securityScans,
        },
        recentActivity,
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw new Error('Failed to load dashboard data');
    }
  }

  /**
   * Get recent privacy scans
   */
  async getRecentPrivacyScans(limit: number = 50): Promise<unknown[]> {
    try {
      return await this.privacyService.getRecentScans(limit);
    } catch (error) {
      console.error('Error fetching privacy scans:', error);
      return [];
    }
  }

  /**
   * Get recent security scans
   */
  async getRecentSecurityScans(limit: number = 50): Promise<unknown[]> {
    try {
      return await this.securityService.getRecentScans(limit);
    } catch (error) {
      console.error('Error fetching security scans:', error);
      return [];
    }
  }

  /**
   * Calculate overall HIPAA compliance score
   */
  calculateOverallScore(privacyScans: unknown[], securityScans: unknown[]): number {
    const privacyWeight = 0.4; // 40% weight for privacy
    const securityWeight = 0.6; // 60% weight for security

    const latestPrivacy = privacyScans[0] as Record<string, unknown>;
    const latestSecurity = securityScans[0] as Record<string, unknown>;

    const privacyScore =
      ((latestPrivacy?.summary as Record<string, unknown>)?.overallScore as number) || 0;
    const securityScore = (latestSecurity?.overallScore as number) || 0;

    return Math.round(privacyScore * privacyWeight + securityScore * securityWeight);
  }

  /**
   * Determine risk level from score
   */
  getRiskLevelFromScore(score: number): 'critical' | 'high' | 'medium' | 'low' {
    if (score >= 90) return 'low';
    if (score >= 75) return 'medium';
    if (score >= 60) return 'high';
    return 'critical';
  }

  /**
   * Determine compliance status
   */
  getComplianceStatus(score: number): 'compliant' | 'partially_compliant' | 'non_compliant' {
    if (score >= 85) return 'compliant';
    if (score >= 60) return 'partially_compliant';
    return 'non_compliant';
  }

  /**
   * Calculate overview metrics
   */
  private calculateOverview(privacyScans: unknown[], securityScans: unknown[]) {
    const overallScore = this.calculateOverallScore(privacyScans, securityScans);
    const riskLevel = this.getRiskLevelFromScore(overallScore);
    const complianceStatus = this.getComplianceStatus(overallScore);

    const allScans = [...privacyScans, ...securityScans];
    const lastScanDate =
      allScans.length > 0
        ? allScans.sort((a, b) => {
            const aRecord = a as Record<string, unknown>;
            const bRecord = b as Record<string, unknown>;
            const aTime = new Date(
              (aRecord.timestamp || aRecord.scanTimestamp) as string,
            ).getTime();
            const bTime = new Date(
              (bRecord.timestamp || bRecord.scanTimestamp) as string,
            ).getTime();
            return bTime - aTime;
          })[0]
        : null;

    return {
      overallScore,
      riskLevel,
      complianceStatus,
      lastScanDate: lastScanDate
        ? ((lastScanDate as Record<string, unknown>).timestamp as string) ||
          new Date((lastScanDate as Record<string, unknown>).scanTimestamp as string).toISOString()
        : new Date().toISOString(),
      totalScans: allScans.length,
    };
  }

  /**
   * Calculate module-specific data
   */
  private calculateModuleData(scans: unknown[], type: 'privacy' | 'security') {
    if (scans.length === 0) {
      return {
        scanCount: 0,
        status: 'not_scanned' as const,
      };
    }

    const latestScan = scans[0] as Record<string, unknown>;
    const latestScore =
      type === 'privacy'
        ? ((latestScan.summary as Record<string, unknown>)?.overallScore as number)
        : (latestScan.overallScore as number);
    const lastScanDate =
      type === 'privacy'
        ? (latestScan.timestamp as string)
        : new Date(latestScan.scanTimestamp as string).toISOString();

    // Determine status based on latest scan
    let status: 'active' | 'needs_attention' | 'not_scanned' = 'active';
    if (latestScore && latestScore < 70) {
      status = 'needs_attention';
    }

    return {
      latestScore,
      scanCount: scans.length,
      lastScanDate,
      status,
    };
  }

  /**
   * Generate recent activity timeline
   */
  private generateRecentActivity(
    privacyScans: unknown[],
    securityScans: unknown[],
  ): ScanActivity[] {
    const activities: ScanActivity[] = [];

    // Convert privacy scans to activities
    privacyScans.slice(0, 5).forEach((scan, index) => {
      const scanRecord = scan as Record<string, unknown>;
      const summary = scanRecord.summary as Record<string, unknown>;
      const overallScore = (summary?.overallScore as number) || 0;

      activities.push({
        id: `privacy-${Date.parse(scanRecord.timestamp as string)}-${index}`,
        type: 'privacy',
        url: scanRecord.targetUrl as string,
        timestamp: scanRecord.timestamp as string,
        score: overallScore,
        status: 'completed',
        riskLevel: this.getRiskLevelFromScore(overallScore),
      });
    });

    // Convert security scans to activities
    securityScans.slice(0, 5).forEach((scan) => {
      const scanRecord = scan as Record<string, unknown>;
      const overallScore = (scanRecord.overallScore as number) || 0;

      activities.push({
        id: scanRecord.scanId as string,
        type: 'security',
        url: scanRecord.targetUrl as string,
        timestamp: new Date(scanRecord.scanTimestamp as string).toISOString(),
        score: overallScore,
        status: 'completed',
        riskLevel: this.getRiskLevelFromScore(overallScore),
      });
    });

    // Sort by timestamp (most recent first) and limit to 10
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);
  }

  /**
   * Get dashboard metrics for analytics
   */
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const [privacyScans, securityScans] = await Promise.all([
      this.getRecentPrivacyScans(),
      this.getRecentSecurityScans(),
    ]);

    const allScans = [...privacyScans, ...securityScans];
    const totalScans = allScans.length;

    if (totalScans === 0) {
      return {
        totalScans: 0,
        averageScore: 0,
        riskDistribution: { critical: 0, high: 0, medium: 0, low: 0 },
        complianceRate: 0,
        trendsData: [],
      };
    }

    const scores = allScans.map((scan) => {
      const scanRecord = scan as Record<string, unknown>;
      return (
        ((scanRecord.summary as Record<string, unknown>)?.overallScore as number) ||
        (scanRecord.overallScore as number) ||
        0
      );
    });
    const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

    // Calculate risk distribution
    const riskDistribution = { critical: 0, high: 0, medium: 0, low: 0 };
    scores.forEach((score) => {
      const risk = this.getRiskLevelFromScore(score);
      riskDistribution[risk]++;
    });

    // Calculate compliance rate (percentage of scans >= 85%)
    const compliantScans = scores.filter((score) => score >= 85).length;
    const complianceRate = Math.round((compliantScans / totalScans) * 100);

    return {
      totalScans,
      averageScore,
      riskDistribution,
      complianceRate,
      trendsData: [], // TODO: Implement trends calculation
    };
  }
}
