import express, { Router } from 'express';
import scanRoutes from './scan';
import adaRoutes from './ada'; // Import ADA routes
import hipaaRoutes from './hipaa'; // Import HIPAA routes
import gdprRoutes from './gdpr'; // Import GDPR routes

const router: Router = express.Router();

router.use('/scans', scanRoutes);
router.use('/ada', adaRoutes); // Mount ADA routes
router.use('/hipaa', hipaaRoutes); // Mount HIPAA routes
router.use('/gdpr', gdprRoutes); // Mount GDPR routes

export default router;
