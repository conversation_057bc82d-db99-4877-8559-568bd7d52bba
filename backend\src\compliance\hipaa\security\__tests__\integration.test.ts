import { HipaaSecurityOrchestrator } from '../hipaa-security-orchestrator';
import { HipaaSecurityScanConfig } from '../types';

describe('HIPAA Security Integration Tests', () => {
  let orchestrator: HipaaSecurityOrchestrator;

  beforeAll(() => {
    orchestrator = new HipaaSecurityOrchestrator();
  });

  afterAll(async () => {
    await orchestrator.cleanup();
  });

  describe('Full Scan Integration', () => {
    it('should perform a complete HIPAA security scan', async () => {
      const config: Partial<HipaaSecurityScanConfig> = {
        targetUrl: 'https://example.com',
        maxPages: 5,
        scanDepth: 1,
        timeout: 60000,
        enableVulnerabilityScanning: true,
        enableSSLAnalysis: true,
        enableContentAnalysis: true,
      };

      const result = await orchestrator.performComprehensiveScan('https://example.com', config);

      expect(result).toBeDefined();
      expect(result.scanId).toBeDefined();
      expect(result.targetUrl).toBe('https://example.com');
      expect(result.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.overallScore).toBeLessThanOrEqual(100);
      expect(result.riskLevel).toMatch(/^(critical|high|medium|low)$/);
      expect(Array.isArray(result.passedTests)).toBe(true);
      expect(Array.isArray(result.failedTests)).toBe(true);
      expect(result.technicalSafeguards).toBeDefined();
      expect(result.administrativeSafeguards).toBeDefined();
      expect(result.organizationalSafeguards).toBeDefined();
      expect(result.physicalSafeguards).toBeDefined();
    }, 120000); // 2 minute timeout for integration test
  });

  describe('Error Handling', () => {
    it('should handle invalid URLs gracefully', async () => {
      await expect(orchestrator.performComprehensiveScan('invalid-url', {})).rejects.toThrow();
    });

    it('should handle network timeouts', async () => {
      const config: Partial<HipaaSecurityScanConfig> = {
        timeout: 1000, // Very short timeout
      };

      await expect(
        orchestrator.performComprehensiveScan('https://httpstat.us/200?sleep=5000', config),
      ).rejects.toThrow();
    });
  });

  describe('Database Operations', () => {
    it('should store and retrieve scan results', async () => {
      const config: Partial<HipaaSecurityScanConfig> = {
        targetUrl: 'https://example.com',
        maxPages: 3,
        scanDepth: 1,
        timeout: 30000,
      };

      const result = await orchestrator.performComprehensiveScan('https://example.com', config);
      expect(result.scanId).toBeDefined();

      // Retrieve the stored result
      const storedResult = await orchestrator.getScanResult(result.scanId);
      expect(storedResult).toBeDefined();
      expect(storedResult?.scanId).toBe(result.scanId);
      expect(storedResult?.targetUrl).toBe('https://example.com');
    }, 60000);

    it('should list all scans', async () => {
      const scans = await orchestrator.getAllScans(10);
      expect(Array.isArray(scans)).toBe(true);
    });
  });

  describe('Scan Management', () => {
    it('should delete scan results', async () => {
      const config: Partial<HipaaSecurityScanConfig> = {
        targetUrl: 'https://example.com',
        maxPages: 2,
        scanDepth: 1,
        timeout: 30000,
      };

      const result = await orchestrator.performComprehensiveScan('https://example.com', config);
      expect(result.scanId).toBeDefined();

      // Delete the scan
      const deleted = await orchestrator.deleteScan(result.scanId);
      expect(deleted).toBe(true);

      // Verify it's deleted
      const retrievedResult = await orchestrator.getScanResult(result.scanId);
      expect(retrievedResult).toBeNull();
    }, 60000);
  });
});
