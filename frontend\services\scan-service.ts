/**
 * Scan Service for initiating and managing compliance scans
 * Provides a unified interface for starting HIPAA, GDPR, ADA, and WCAG scans
 */

import { HipaaPrivacyScanResult } from '@/types/hipaa-privacy';
import { HipaaSecurityScanResult } from '@/types/hipaa-security';

export interface ScanOptions {
  timeout?: number;
  maxPages?: number;
  scanDepth?: number;
}

export interface HipaaPrivacyScanOptions extends ScanOptions {
  enableLevel1?: boolean;
  enableLevel2?: boolean;
  enableLevel3?: boolean;
}

export interface HipaaSecurityScanOptions extends ScanOptions {
  enableVulnerabilityScanning?: boolean;
  enableSSLAnalysis?: boolean;
  enableContentAnalysis?: boolean;
}

export interface ScanProgress {
  scanId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number; // 0-100
  currentStep?: string;
  estimatedTimeRemaining?: number;
  message?: string;
}

class ScanService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api/v1';
  }

  /**
   * Start a HIPAA privacy policy scan
   */
  async startHipaaPrivacyScan(
    targetUrl: string,
    options: HipaaPrivacyScanOptions = {},
  ): Promise<HipaaPrivacyScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUrl,
          enableLevel1: options.enableLevel1 ?? true,
          enableLevel2: options.enableLevel2 ?? true,
          enableLevel3: options.enableLevel3 ?? false,
          timeout: options.timeout ?? 300000,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data;
      }

      throw new Error(data.error || 'Failed to start privacy scan');
    } catch (error) {
      console.error('Error starting HIPAA privacy scan:', error);
      throw error;
    }
  }

  /**
   * Start a HIPAA security compliance scan
   */
  async startHipaaSecurityScan(
    targetUrl: string,
    options: HipaaSecurityScanOptions = {},
  ): Promise<HipaaSecurityScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/hipaa-security/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUrl,
          maxPages: options.maxPages ?? 15,
          scanDepth: options.scanDepth ?? 2,
          timeout: options.timeout ?? 1800000,
          enableVulnerabilityScanning: options.enableVulnerabilityScanning ?? true,
          enableSSLAnalysis: options.enableSSLAnalysis ?? true,
          enableContentAnalysis: options.enableContentAnalysis ?? true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data.result || data.data;
      }

      throw new Error(data.error || 'Failed to start security scan');
    } catch (error) {
      console.error('Error starting HIPAA security scan:', error);
      throw error;
    }
  }

  /**
   * Get scan progress for a running scan
   */
  async getScanProgress(scanId: string): Promise<ScanProgress> {
    try {
      const response = await fetch(`${this.baseUrl}/scans/${scanId}/progress`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data;
      }

      throw new Error(data.error || 'Failed to get scan progress');
    } catch (error) {
      console.error('Error getting scan progress:', error);
      throw error;
    }
  }

  /**
   * Get scan results by scan ID
   */
  async getScanResults(scanId: string): Promise<HipaaPrivacyScanResult | HipaaSecurityScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/scans/${scanId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data;
      }

      throw new Error(data.error || 'Failed to get scan results');
    } catch (error) {
      console.error('Error getting scan results:', error);
      throw error;
    }
  }

  /**
   * Cancel a running scan
   */
  async cancelScan(scanId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/scans/${scanId}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to cancel scan');
      }
    } catch (error) {
      console.error('Error canceling scan:', error);
      throw error;
    }
  }

  /**
   * Get all scans for the current user
   */
  async getAllScans(
    limit: number = 50,
    offset: number = 0,
    standard?: 'hipaa' | 'gdpr' | 'ada' | 'wcag',
  ): Promise<{
    scans: Array<HipaaPrivacyScanResult | HipaaSecurityScanResult>;
    total: number;
    hasMore: boolean;
  }> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (standard) {
        params.append('standard', standard);
      }

      const response = await fetch(`${this.baseUrl}/scans?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return {
          scans: data.data.scans || [],
          total: data.data.total || 0,
          hasMore: data.data.hasMore || false,
        };
      }

      throw new Error(data.error || 'Failed to get scans');
    } catch (error) {
      console.error('Error getting scans:', error);
      throw error;
    }
  }

  /**
   * Delete a scan
   */
  async deleteScan(scanId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/scans/${scanId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to delete scan');
      }
    } catch (error) {
      console.error('Error deleting scan:', error);
      throw error;
    }
  }

  /**
   * Validate a URL before scanning
   */
  validateUrl(url: string): { isValid: boolean; error?: string } {
    try {
      const urlObj = new URL(url);

      // Check if it's HTTP or HTTPS
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          isValid: false,
          error: 'URL must use HTTP or HTTPS protocol',
        };
      }

      // Check if hostname is valid
      if (!urlObj.hostname) {
        return {
          isValid: false,
          error: 'URL must have a valid hostname',
        };
      }

      // Check for localhost or private IPs in production
      if (process.env.NODE_ENV === 'production') {
        const hostname = urlObj.hostname.toLowerCase();
        if (
          hostname === 'localhost' ||
          hostname.startsWith('127.') ||
          hostname.startsWith('192.168.') ||
          hostname.startsWith('10.') ||
          hostname.match(/^172\.(1[6-9]|2[0-9]|3[0-1])\./)
        ) {
          return {
            isValid: false,
            error: 'Cannot scan localhost or private IP addresses',
          };
        }
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: 'Invalid URL format',
      };
    }
  }
}

// Export singleton instance
export const scanService = new ScanService();
export default scanService;
