// backend/src/compliance/wcag/page-title-check.ts
import { WcagCheckResult } from './types';

/**
 * Checks for the presence and non-emptiness of a <title> tag in the HTML of a given URL.
 * This check is relevant to WCAG 2.1 Success Criterion 2.4.2 Page Titled.
 *
 * @param {string} targetUrl - The URL of the page to be scanned.
 * @returns {Promise<WcagCheckResult>} A promise that resolves to a WcagCheckResult, indicating whether the page has a valid title.
 */
export async function checkPageTitlePresence(targetUrl: string): Promise<WcagCheckResult> {
  let pageContent = '';
  let fetchError = false;
  let errorMessage = '';
  let actualTitle: string | null = null;

  try {
    const response = await fetch(targetUrl, { headers: { 'User-Agent': 'ComplyChecker/1.0' } });
    if (!response.ok) {
      throw new Error(
        `Failed to fetch ${targetUrl}. Status: ${response.status} ${response.statusText}`,
      );
    }
    pageContent = await response.text();
  } catch (error: unknown) {
    console.error(`Error fetching page content for ${targetUrl}:`, error);
    fetchError = true;
    if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = 'Unknown error during page fetch.';
    }
  }

  if (fetchError) {
    return {
      checkId: 'WCAG-TITLE-001',
      name: 'Page Title Presence',
      passed: false,
      description: `Failed to retrieve page content from ${targetUrl}. Error: ${errorMessage}`,
      details: {
        info: `Attempted to fetch ${targetUrl} to check for page title. Error: ${errorMessage}`,
        target: targetUrl,
        foundTitle: null,
      },
      level: 'A',
      sc: '2.4.2',
    };
  }

  const titleMatch = pageContent.match(/<title\b[^>]*>([\s\S]*?)<\/title>/i);
  let passed = false;
  let description = '';

  if (titleMatch) {
    actualTitle = titleMatch[1] !== undefined ? titleMatch[1].trim() : '';
    if (actualTitle.length > 0) {
      passed = true;
      description = `Page has a title: "${actualTitle}".`;
    } else {
      passed = false;
      description = 'Page has an empty <title> tag.';
    }
  } else {
    passed = false;
    description = 'Page is missing a <title> tag.';
    actualTitle = null;
  }

  return {
    checkId: 'WCAG-TITLE-001',
    name: 'Page Title Presence',
    passed,
    description,
    details: {
      info: description,
      target: targetUrl,
      foundTitle: actualTitle,
    },
    level: 'A',
    sc: '2.4.2',
  };
}
