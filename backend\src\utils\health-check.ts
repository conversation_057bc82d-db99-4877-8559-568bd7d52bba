// backend/src/utils/health-check.ts

/**
 * Health check utilities for production monitoring
 * Provides comprehensive health and readiness checks
 */

// import { HealthChecker } from '../config/production';
import { MemoryMonitor } from '../config/performance';
import { ApplicationMonitor } from './monitoring';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: Record<string, CheckResult>;
  metrics?: {
    memory: NodeJS.MemoryUsage;
    performance: Record<string, unknown>;
  };
}

export interface CheckResult {
  status: 'pass' | 'fail' | 'warn';
  duration: number;
  message?: string;
  details?: Record<string, unknown> | string;
}

/**
 * Comprehensive health check implementation
 */
export class HealthCheckService {
  private static version = process.env.npm_package_version || '1.0.0';
  private static environment = process.env.NODE_ENV || 'development';
  private static startTime = Date.now();

  /**
   * Perform basic health check
   */
  static async basicHealthCheck(): Promise<HealthStatus> {
    const checks: Record<string, CheckResult> = {};
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    // Basic application check
    checks.application = await this.checkApplication();

    // Memory check
    checks.memory = await this.checkMemory();

    // Determine overall status
    const failedChecks = Object.values(checks).filter((check) => check.status === 'fail');
    const warnChecks = Object.values(checks).filter((check) => check.status === 'warn');

    if (failedChecks.length > 0) {
      overallStatus = 'unhealthy';
    } else if (warnChecks.length > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      version: this.version,
      environment: this.environment,
      checks,
    };
  }

  /**
   * Perform comprehensive health check with all dependencies
   */
  static async comprehensiveHealthCheck(): Promise<HealthStatus> {
    const checks: Record<string, CheckResult> = {};
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    // Run all checks in parallel for better performance
    const checkPromises = [
      this.checkApplication().then((result) => ({ name: 'application', result })),
      this.checkMemory().then((result) => ({ name: 'memory', result })),
      this.checkDatabase().then((result) => ({ name: 'database', result })),
      this.checkRedis().then((result) => ({ name: 'redis', result })),
      this.checkDiskSpace().then((result) => ({ name: 'disk', result })),
      this.checkExternalServices().then((result) => ({ name: 'external_services', result })),
    ];

    const results = await Promise.allSettled(checkPromises);

    // Process results
    for (const result of results) {
      if (result.status === 'fulfilled') {
        checks[result.value.name] = result.value.result;
      } else {
        checks.unknown = {
          status: 'fail',
          duration: 0,
          message: 'Health check failed to execute',
          details: result.reason,
        };
      }
    }

    // Determine overall status
    const failedChecks = Object.values(checks).filter((check) => check.status === 'fail');
    const warnChecks = Object.values(checks).filter((check) => check.status === 'warn');

    if (failedChecks.length > 0) {
      overallStatus = 'unhealthy';
    } else if (warnChecks.length > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      version: this.version,
      environment: this.environment,
      checks,
      metrics: {
        memory: MemoryMonitor.getCurrentMemoryUsage(),
        performance: ApplicationMonitor.getDashboardData(),
      },
    };
  }

  /**
   * Check application status
   */
  private static async checkApplication(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      // Basic application checks
      const uptime = process.uptime();
      const memoryUsage = MemoryMonitor.getMemoryUsageMB();

      if (uptime < 10) {
        return {
          status: 'warn',
          duration: Date.now() - startTime,
          message: 'Application recently started',
          details: { uptime },
        };
      }

      if (memoryUsage > 4000) {
        // 4GB threshold
        return {
          status: 'warn',
          duration: Date.now() - startTime,
          message: 'High memory usage detected',
          details: { memoryUsageMB: memoryUsage },
        };
      }

      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'Application is running normally',
        details: { uptime, memoryUsageMB: memoryUsage },
      };
    } catch (error) {
      return {
        status: 'fail',
        duration: Date.now() - startTime,
        message: 'Application check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check memory usage
   */
  private static async checkMemory(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      const usage = MemoryMonitor.getCurrentMemoryUsage();
      const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);
      const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024);
      const rssMB = Math.round(usage.rss / 1024 / 1024);

      // Warning thresholds
      const heapWarningThreshold = 3000; // 3GB
      const rssWarningThreshold = 4000; // 4GB

      if (heapUsedMB > heapWarningThreshold || rssMB > rssWarningThreshold) {
        return {
          status: 'warn',
          duration: Date.now() - startTime,
          message: 'Memory usage is high',
          details: { heapUsedMB, heapTotalMB, rssMB },
        };
      }

      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'Memory usage is normal',
        details: { heapUsedMB, heapTotalMB, rssMB },
      };
    } catch (error) {
      return {
        status: 'fail',
        duration: Date.now() - startTime,
        message: 'Memory check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check database connectivity
   */
  private static async checkDatabase(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      const { default: db } = await import('../lib/db');
      await db.raw('SELECT 1 as health_check');

      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'Database connection is healthy',
      };
    } catch (error) {
      return {
        status: 'fail',
        duration: Date.now() - startTime,
        message: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check Redis connectivity
   */
  private static async checkRedis(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      // Redis check would go here
      // For now, return a basic check
      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'Redis connection is healthy',
      };
    } catch (error) {
      return {
        status: 'fail',
        duration: Date.now() - startTime,
        message: 'Redis connection failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check disk space
   */
  private static async checkDiskSpace(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      // const { default: fs } = await import('fs'); // fs is not used
      // const _stats = fs.statSync('.');

      // Basic disk space check
      // In production, implement proper disk space monitoring
      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'Disk space is adequate',
      };
    } catch (error) {
      return {
        status: 'warn',
        duration: Date.now() - startTime,
        message: 'Could not check disk space',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check external services
   */
  private static async checkExternalServices(): Promise<CheckResult> {
    const startTime = Date.now();

    try {
      // Check if we can reach external services
      // This is a placeholder - implement actual service checks
      return {
        status: 'pass',
        duration: Date.now() - startTime,
        message: 'External services are reachable',
      };
    } catch (error) {
      return {
        status: 'warn',
        duration: Date.now() - startTime,
        message: 'Some external services may be unreachable',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get readiness status (for Kubernetes-style readiness probes)
   */
  static async getReadinessStatus(): Promise<{ ready: boolean; message: string }> {
    try {
      const health = await this.basicHealthCheck();
      const isReady = health.status === 'healthy' || health.status === 'degraded';

      return {
        ready: isReady,
        message: isReady ? 'Service is ready' : 'Service is not ready',
      };
    } catch (error) {
      return {
        ready: false,
        message: 'Readiness check failed',
      };
    }
  }

  /**
   * Get liveness status (for Kubernetes-style liveness probes)
   */
  static async getLivenessStatus(): Promise<{ alive: boolean; message: string }> {
    try {
      // Basic liveness check - just verify the process is responsive
      const uptime = process.uptime();

      return {
        alive: uptime > 0,
        message: uptime > 0 ? 'Service is alive' : 'Service is not responding',
      };
    } catch (error) {
      return {
        alive: false,
        message: 'Liveness check failed',
      };
    }
  }
}

/**
 * CLI health check for Docker health checks
 */
export async function cliHealthCheck(): Promise<void> {
  try {
    const health = await HealthCheckService.basicHealthCheck();

    if (health.status === 'healthy') {
      console.log('Health check passed');
      process.exit(0);
    } else {
      console.error('Health check failed:', health.status);
      process.exit(1);
    }
  } catch (error) {
    console.error('Health check error:', error);
    process.exit(1);
  }
}

// If this file is run directly (for Docker health checks)
if (require.main === module) {
  cliHealthCheck();
}

export default HealthCheckService;
