// backend/src/compliance/hipaa/privacy/legacy/privacy-policy-check.ts

/**
 * DEPRECATED: Legacy privacy policy check function
 * This file is maintained for backward compatibility only.
 * New implementations should use the enhanced check functions in the checks/ directory.
 */

import { Hip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ult, HipaaCheckCategory, HipaaSeverity } from '../types';

/**
 * @deprecated Use checks/privacy-policy-presence.ts instead
 * Fetches page content and checks for the presence of a privacy policy link.
 * @param {string} targetUrl - The URL of the page to check.
 * @returns {Promise<HipaaCheckResult>} A Promise resolving to a HipaaCheckResult object indicating if a privacy policy link was found.
 */
export async function checkPrivacyPolicyPresence(targetUrl: string): Promise<HipaaCheckResult> {
  console.warn(
    'DEPRECATED: checkPrivacyPolicyPresence is deprecated. Use checks/privacy-policy-presence.ts instead.',
  );

  let pageContent = '';
  let fetchError = false;
  let errorMessage = '';

  try {
    const response = await fetch(targetUrl, { headers: { 'User-Agent': 'ComplyChecker/1.0' } });
    if (!response.ok) {
      // This error will be caught by the catch block
      throw new Error(
        `Failed to fetch ${targetUrl}. Status: ${response.status} ${response.statusText}`,
      );
    }
    pageContent = await response.text();
  } catch (error: unknown) {
    console.error(`Error fetching page content for ${targetUrl}:`, error);
    fetchError = true;
    if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = 'Unknown error during page fetch.';
    }
    if (!errorMessage) {
      // Ensure fallback if error.message was empty
      errorMessage = 'Unknown error during page fetch.';
    }
  }

  if (fetchError) {
    return {
      checkId: 'HIPAA-PP-001',
      name: 'Privacy Policy Presence', // Corrected name
      category: HipaaCheckCategory.PRESENCE,
      passed: false,
      severity: HipaaSeverity.HIGH,
      confidence: 0, // No confidence due to error
      // Added description field for error cases
      description: `Failed to retrieve page content from ${targetUrl}. Error: ${errorMessage}`,
      details: {
        summary: `Failed to fetch ${targetUrl}`,
        findings: [],
        metrics: {
          processingTime: 0,
          contentLength: 0,
          sectionsFound: 0,
          patternsMatched: 0,
          entitiesExtracted: 0,
        },
        context: {
          url: targetUrl,
        },
        // Legacy compatibility
        info: `Attempted to fetch ${targetUrl} to check for privacy policy presence, but the fetch failed.`,
        error: errorMessage,
        target: targetUrl,
      },
      remediation: {
        priority: 'high',
        effort: 'minimal',
        steps: ['Fix website accessibility issues', 'Ensure privacy policy page is accessible'],
        resources: [],
        timeline: 'Immediate',
      },
      metadata: {
        checkVersion: '2.0',
        processingTime: 0,
        analysisLevels: [],
        error: errorMessage,
      },
    };
  }

  const hasPrivacyPolicy = pageContent.toLowerCase().includes('privacy policy');

  return {
    checkId: 'HIPAA-PP-001',
    name: 'Privacy Policy Presence', // Corrected name
    category: HipaaCheckCategory.PRESENCE,
    passed: hasPrivacyPolicy,
    severity: hasPrivacyPolicy ? HipaaSeverity.INFO : HipaaSeverity.HIGH,
    confidence: 95, // High confidence for exact phrase matching
    // Updated description and details for consistency and clarity
    description: hasPrivacyPolicy
      ? `A mention of "privacy policy" was found on ${targetUrl}.`
      : `No mention of "privacy policy" was found on the page content of ${targetUrl}.`,
    details: {
      summary: hasPrivacyPolicy
        ? `Privacy policy found on ${targetUrl}`
        : `No privacy policy found on ${targetUrl}`,
      findings: [],
      metrics: {
        processingTime: 0,
        contentLength: pageContent.length,
        sectionsFound: 0,
        patternsMatched: hasPrivacyPolicy ? 1 : 0,
        entitiesExtracted: 0,
      },
      context: {
        url: targetUrl,
        contentType: 'text/html',
      },
      // Legacy compatibility
      info: hasPrivacyPolicy
        ? `The phrase "privacy policy" (case-insensitive) was detected in the content of ${targetUrl}.`
        : `The page content of ${targetUrl} was successfully fetched but did not contain the phrase "privacy policy" (case-insensitive).`,
      target: targetUrl,
      foundPrivacyPolicy: hasPrivacyPolicy,
    },
    remediation: {
      priority: hasPrivacyPolicy ? 'low' : 'high',
      effort: hasPrivacyPolicy ? 'minimal' : 'moderate',
      steps: hasPrivacyPolicy
        ? ['Privacy policy found - no action needed']
        : [
            'Create a comprehensive privacy policy',
            'Add prominent link to privacy policy on website',
          ],
      resources: [],
      timeline: hasPrivacyPolicy ? 'N/A' : '1-2 weeks',
    },
    metadata: {
      checkVersion: '2.0',
      processingTime: 0,
      analysisLevels: [1], // Basic phrase matching only
    },
  };
}
