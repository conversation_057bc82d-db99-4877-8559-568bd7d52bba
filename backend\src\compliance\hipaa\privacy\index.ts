// backend/src/compliance/hipaa/privacy/index.ts

// Core types and interfaces
export * from './types';

// Legacy compatibility (deprecated)
export * from './legacy/privacy-policy-check';

// Enhanced orchestrator and checks
export { HipaaPrivacyPolicyOrchestrator } from './services/privacy-orchestrator';

// Individual check functions
export { checkPrivacyPolicyPresence as checkPrivacyPolicyPresenceEnhanced } from './checks/privacy-policy-presence';
export { checkHipaaSpecificContent } from './checks/hipaa-specific-content';
export { checkContactInformation } from './checks/contact-information';

// Utility functions
export { PatternMatcher } from './utils/pattern-matcher';
export { NLPAnalyzer } from './utils/nlp-analyzer';
export { AIAnalyzer } from './utils/ai-analyzer';
export { ContentAnalyzer } from './utils/content-analyzer';
export { URLResolver } from './utils/url-resolver';
export { ReadabilityScorer } from './utils/readability-scorer';

// Database functions
export { HipaaDatabase } from './database/hipaa-privacy-db';
export { LegacyHipaaAdapter } from './database/legacy-adapter';

// Constants
export * from './constants';
