# Comply Checker API Documentation

## 📋 Overview

The Comply Checker API provides comprehensive compliance scanning and analysis capabilities for HIPAA, GDPR, ADA, and WCAG standards. Built with Express.js and TypeScript, the API follows RESTful principles with consistent response formats and robust error handling.

**Base URL**: `http://localhost:5000/api/v1` (Development)  
**Production URL**: `https://your-domain.com/api/v1`

## 🔐 Authentication

### Keycloak SSO Integration
All protected endpoints require a valid JWT token obtained through Keycloak authentication.

**Authentication Header**:
```http
Authorization: Bearer <jwt_token>
```

**Token Endpoint**:
```http
POST /auth/token
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}
```

## 📊 Standard Response Format

All API responses follow a consistent format:

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    processingTime: number;
  };
}
```

**Success Response Example**:
```json
{
  "success": true,
  "data": {
    "id": "scan-123",
    "status": "completed"
  },
  "metadata": {
    "timestamp": "2025-06-26T10:30:00Z",
    "requestId": "req-abc123",
    "processingTime": 1250
  }
}
```

**Error Response Example**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid URL format",
    "details": {
      "field": "targetUrl",
      "value": "invalid-url"
    }
  },
  "metadata": {
    "timestamp": "2025-06-26T10:30:00Z",
    "requestId": "req-abc123",
    "processingTime": 45
  }
}
```

## 🏥 HIPAA Compliance Endpoints

### Privacy Policy Analysis

#### Start Privacy Policy Scan
```http
POST /compliance/hipaa/privacy/scan
Content-Type: application/json
Authorization: Bearer <token>

{
  "targetUrl": "https://example-healthcare.com",
  "options": {
    "enableLevel1": true,
    "enableLevel2": true,
    "enableLevel3": true,
    "maxPages": 10,
    "timeout": 300000
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "scanId": "hipaa-privacy-001",
    "targetUrl": "https://example-healthcare.com",
    "scanTimestamp": "2025-06-26T10:30:00Z",
    "overallScore": 85,
    "riskLevel": "low",
    "analysisLevels": {
      "level1": {
        "score": 80,
        "findings": [
          {
            "checkId": "privacy_policy_presence",
            "name": "Privacy Policy Presence",
            "passed": true,
            "severity": "high",
            "description": "Privacy policy found and accessible",
            "details": {
              "privacyPolicyUrl": "https://example-healthcare.com/privacy",
              "accessible": true,
              "wordCount": 2500
            }
          }
        ]
      },
      "level2": {
        "score": 88,
        "findings": [
          {
            "checkId": "hipaa_specific_content",
            "name": "HIPAA-Specific Content",
            "passed": true,
            "severity": "critical",
            "description": "HIPAA-specific language and requirements found",
            "details": {
              "hipaaReferences": 15,
              "phiMentions": 8,
              "securityMeasures": 12
            }
          }
        ]
      },
      "level3": {
        "score": 87,
        "confidence": 0.92,
        "findings": [
          {
            "checkId": "ai_compliance_analysis",
            "name": "AI-Powered Compliance Analysis",
            "passed": true,
            "severity": "critical",
            "description": "Advanced AI analysis indicates strong HIPAA compliance",
            "details": {
              "complianceScore": 0.87,
              "confidenceLevel": 0.92,
              "keyFindings": [
                "Strong privacy protection language",
                "Clear patient rights statements",
                "Comprehensive security measures"
              ]
            }
          }
        ]
      }
    },
    "metadata": {
      "scanVersion": "1.0.0",
      "toolsUsed": ["DistilBERT", "NLP Analyzer"],
      "analysisLevels": ["Level 1", "Level 2", "Level 3"],
      "pagesAnalyzed": [
        "https://example-healthcare.com",
        "https://example-healthcare.com/privacy"
      ],
      "totalContentLength": 45000,
      "processingTime": 12000
    }
  }
}
```

#### Get Privacy Policy Scans
```http
GET /compliance/hipaa/privacy/scans?limit=50&offset=0
Authorization: Bearer <token>
```

**Response**:
```json
{
  "success": true,
  "data": {
    "scans": [
      {
        "scanId": "hipaa-privacy-001",
        "targetUrl": "https://example-healthcare.com",
        "scanTimestamp": "2025-06-26T10:30:00Z",
        "overallScore": 85,
        "riskLevel": "low",
        "status": "completed"
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 50,
      "offset": 0,
      "hasMore": false
    }
  }
}
```

### Security Compliance Analysis

#### Start Security Compliance Scan
```http
POST /compliance/hipaa/security/scan
Content-Type: application/json
Authorization: Bearer <token>

{
  "targetUrl": "https://example-healthcare.com",
  "options": {
    "includeSubdomains": false,
    "timeout": 600000,
    "enableNuclei": true,
    "categories": [
      "technical",
      "administrative", 
      "organizational",
      "physical"
    ]
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "scanId": "hipaa-security-001",
    "targetUrl": "https://example-healthcare.com",
    "scanTimestamp": "2025-06-26T10:30:00Z",
    "scanDuration": 45000,
    "overallScore": 78,
    "riskLevel": "medium",
    "technicalSafeguards": {
      "category": "technical",
      "totalTests": 48,
      "passedTests": 36,
      "failedTests": 12,
      "score": 76,
      "riskLevel": "medium",
      "criticalIssues": 1,
      "highIssues": 3,
      "mediumIssues": 5,
      "lowIssues": 3
    },
    "administrativeSafeguards": {
      "category": "administrative",
      "totalTests": 12,
      "passedTests": 9,
      "failedTests": 3,
      "score": 75,
      "riskLevel": "medium",
      "criticalIssues": 0,
      "highIssues": 1,
      "mediumIssues": 2,
      "lowIssues": 0
    },
    "organizationalSafeguards": {
      "category": "organizational",
      "totalTests": 9,
      "passedTests": 7,
      "failedTests": 2,
      "score": 78,
      "riskLevel": "medium",
      "criticalIssues": 0,
      "highIssues": 0,
      "mediumIssues": 2,
      "lowIssues": 0
    },
    "physicalSafeguards": {
      "category": "physical",
      "totalTests": 8,
      "passedTests": 6,
      "failedTests": 2,
      "score": 75,
      "riskLevel": "medium",
      "criticalIssues": 0,
      "highIssues": 1,
      "mediumIssues": 1,
      "lowIssues": 0
    },
    "vulnerabilities": [
      {
        "id": "ssl-weak-cipher",
        "severity": "high",
        "category": "technical",
        "title": "Weak SSL Cipher Suite",
        "description": "Server supports weak SSL cipher suites",
        "impact": "Potential for man-in-the-middle attacks",
        "recommendation": "Disable weak cipher suites and enable only strong encryption",
        "evidence": {
          "url": "https://example-healthcare.com:443",
          "weakCiphers": ["TLS_RSA_WITH_3DES_EDE_CBC_SHA"]
        }
      }
    ],
    "passedTests": [
      {
        "id": "https-redirect",
        "category": "technical",
        "title": "HTTPS Redirect",
        "description": "HTTP traffic properly redirected to HTTPS",
        "evidence": {
          "redirectStatus": 301,
          "targetUrl": "https://example-healthcare.com"
        }
      }
    ],
    "failedTests": [
      {
        "id": "hsts-header",
        "category": "technical",
        "severity": "medium",
        "title": "Missing HSTS Header",
        "description": "HTTP Strict Transport Security header not present",
        "recommendation": "Implement HSTS header with appropriate max-age",
        "evidence": {
          "headers": {},
          "expectedHeader": "Strict-Transport-Security"
        }
      }
    ],
    "pagesScanned": ["https://example-healthcare.com"],
    "toolsUsed": ["Nuclei", "SSL Analyzer"],
    "scanStatus": "completed"
  }
}
```

#### Get Security Compliance Scans
```http
GET /compliance/hipaa/security/scans?limit=50&offset=0
Authorization: Bearer <token>
```

### HIPAA Dashboard Data

#### Get Aggregated Dashboard Data
```http
GET /hipaa-dashboard/data
Authorization: Bearer <token>
```

**Response**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalScans": 45,
      "lastScanDate": "2025-06-26T10:30:00Z",
      "overallScore": 82,
      "riskLevel": "medium"
    },
    "modules": {
      "privacy": {
        "latestScore": 85,
        "scanCount": 25,
        "lastScanDate": "2025-06-26T10:30:00Z",
        "status": "compliant"
      },
      "security": {
        "latestScore": 78,
        "scanCount": 20,
        "lastScanDate": "2025-06-26T09:15:00Z",
        "status": "needs_attention"
      }
    },
    "recentActivity": [
      {
        "type": "privacy",
        "timestamp": "2025-06-26T10:30:00Z",
        "score": 85,
        "status": "completed",
        "riskLevel": "low"
      }
    ],
    "analytics": {
      "totalScans": 45,
      "averageScore": 82,
      "complianceRate": 78,
      "riskDistribution": {
        "critical": 2,
        "high": 5,
        "medium": 15,
        "low": 23
      },
      "trendsData": []
    }
  }
}
```

## 🌍 GDPR Compliance Endpoints

### Cookie Consent Analysis

#### Start GDPR Cookie Scan
```http
POST /compliance/gdpr/scan
Content-Type: application/json
Authorization: Bearer <token>

{
  "targetUrl": "https://example-website.com",
  "options": {
    "checkCookies": true,
    "checkPrivacyPolicy": true,
    "checkDataProcessing": true,
    "timeout": 300000
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "scanId": "gdpr-001",
    "targetUrl": "https://example-website.com",
    "scanTimestamp": "2025-06-26T10:30:00Z",
    "overallScore": 75,
    "riskLevel": "medium",
    "findings": [
      {
        "checkId": "cookie_consent_banner",
        "name": "Cookie Consent Banner",
        "passed": true,
        "severity": "high",
        "description": "Cookie consent banner found and functional",
        "details": {
          "bannerPresent": true,
          "optOutAvailable": true,
          "granularConsent": false
        }
      }
    ]
  }
}
```

## ♿ ADA Compliance Endpoints

### Accessibility Analysis

#### Start ADA Accessibility Scan
```http
POST /compliance/ada/scan
Content-Type: application/json
Authorization: Bearer <token>

{
  "targetUrl": "https://example-website.com",
  "options": {
    "checkImages": true,
    "checkForms": true,
    "checkNavigation": true,
    "wcagLevel": "AA"
  }
}
```

## 🔍 General Scan Management

### Get All Scans
```http
GET /compliance/scans?standard=hipaa&status=completed&limit=50
Authorization: Bearer <token>
```

### Get Specific Scan
```http
GET /compliance/scans/{scanId}
Authorization: Bearer <token>
```

### Delete Scan
```http
DELETE /compliance/scans/{scanId}
Authorization: Bearer <token>
```

## 🏥 Health & Status Endpoints

### System Health Check
```http
GET /health
```

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-26T10:30:00Z",
    "version": "1.0.0",
    "uptime": 86400,
    "services": {
      "database": "healthy",
      "keycloak": "healthy",
      "nuclei": "available"
    }
  }
}
```

### Detailed System Status
```http
GET /health/detailed
Authorization: Bearer <token>
```

## ❌ Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `UNAUTHORIZED` | Authentication required | 401 |
| `FORBIDDEN` | Insufficient permissions | 403 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMITED` | Too many requests | 429 |
| `INTERNAL_ERROR` | Server error | 500 |
| `SERVICE_UNAVAILABLE` | External service unavailable | 503 |

## 📝 Rate Limiting

- **Default Limit**: 100 requests per 15 minutes per IP
- **Authenticated Users**: 1000 requests per 15 minutes
- **Scan Endpoints**: 10 scans per hour per user

**Rate Limit Headers**:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 🔧 Development & Testing

### Environment Variables
```bash
# Required
DATABASE_URL=postgresql://user:pass@localhost:5432/comply_checker
KEYCLOAK_URL=http://localhost:8080/auth
KEYCLOAK_REALM=comply-checker
KEYCLOAK_CLIENT_ID=complychecker-backend

# Optional
NUCLEI_ENABLED=true
NUCLEI_PATH=tools/nuclei/nuclei.exe
LOG_LEVEL=info
RATE_LIMIT_ENABLED=true
```

### Testing Endpoints
Use the provided Postman collection or curl commands for testing:

```bash
# Health check
curl -X GET http://localhost:5000/api/v1/health

# Start HIPAA privacy scan
curl -X POST http://localhost:5000/api/v1/compliance/hipaa/privacy/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"targetUrl": "https://example.com"}'
```

---

*This API documentation provides comprehensive coverage of all Comply Checker endpoints with detailed request/response examples and authentication requirements.*
