/**
 * PART 8: Enhanced Production Configuration
 * Updated based on validation findings for better reliability and performance
 */

export interface ProductionConfig {
  // Enhanced timeout configuration
  timeouts: {
    scanTotal: number;
    sslAnalysis: number;
    zapScanning: number;
    networkRequest: number;
    databaseQuery: number;
    healthCheck: number;
  };

  // Reliability configuration
  reliability: {
    maxRetries: number;
    retryDelay: number;
    circuitBreakerThreshold: number;
    circuitBreakerTimeout: number;
    enablePartialResults: boolean;
    gracefulDegradationEnabled: boolean;
    minDataCompleteness: number;
  };

  // Performance optimization
  performance: {
    maxConcurrentScans: number;
    maxConcurrentTests: number;
    memoryLimit: number;
    cpuThreshold: number;
    enableCaching: boolean;
    cacheTimeout: number;
    enableCompression: boolean;
  };

  // Monitoring and observability
  monitoring: {
    enableMetrics: boolean;
    enableTracing: boolean;
    enableLogging: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    metricsInterval: number;
    alertThresholds: {
      errorRate: number;
      responseTime: number;
      memoryUsage: number;
      cpuUsage: number;
    };
  };

  // Security configuration
  security: {
    enableRateLimiting: boolean;
    rateLimitWindow: number;
    rateLimitMax: number;
    enableCors: boolean;
    corsOrigins: string[];
    enableHelmet: boolean;
    enableHttps: boolean;
    sslCertPath?: string;
    sslKeyPath?: string;
  };

  // Database configuration
  database: {
    connectionPool: {
      min: number;
      max: number;
      acquireTimeoutMillis: number;
      idleTimeoutMillis: number;
    };
    enableReadReplicas: boolean;
    enableConnectionRetry: boolean;
    maxRetries: number;
    retryDelay: number;
  };

  // External services
  externalServices: {
    zap: {
      url: string;
      timeout: number;
      maxRetries: number;
      healthCheckInterval: number;
    };
    ssl: {
      timeout: number;
      maxRetries: number;
      enableCaching: boolean;
      cacheTimeout: number;
    };
  };

  // Environment-specific settings
  environment: {
    nodeEnv: 'production' | 'staging' | 'development';
    port: number;
    host: string;
    enableCluster: boolean;
    workerCount: number;
    enableGracefulShutdown: boolean;
    shutdownTimeout: number;
  };
}

// Production-optimized configuration
export const PRODUCTION_CONFIG: ProductionConfig = {
  timeouts: {
    scanTotal: 300000, // 5 minutes (increased from 3 minutes)
    sslAnalysis: 30000, // 30 seconds (increased from 10 seconds)
    zapScanning: 120000, // 2 minutes (increased from 1 minute)
    networkRequest: 15000, // 15 seconds (increased from 10 seconds)
    databaseQuery: 5000, // 5 seconds
    healthCheck: 10000, // 10 seconds
  },

  reliability: {
    maxRetries: 3,
    retryDelay: 2000,
    circuitBreakerThreshold: 5,
    circuitBreakerTimeout: 60000,
    enablePartialResults: true,
    gracefulDegradationEnabled: true,
    minDataCompleteness: 0.7,
  },

  performance: {
    maxConcurrentScans: 10,
    maxConcurrentTests: 5,
    memoryLimit: 1024 * 1024 * 1024, // 1GB
    cpuThreshold: 80,
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableCompression: true,
  },

  monitoring: {
    enableMetrics: true,
    enableTracing: true,
    enableLogging: true,
    logLevel: 'info',
    metricsInterval: 30000,
    alertThresholds: {
      errorRate: 0.05, // 5%
      responseTime: 5000, // 5 seconds
      memoryUsage: 0.8, // 80%
      cpuUsage: 0.8, // 80%
    },
  },

  security: {
    enableRateLimiting: true,
    rateLimitWindow: 900000, // 15 minutes
    rateLimitMax: 100, // 100 requests per window
    enableCors: true,
    corsOrigins: ['https://yourdomain.com'],
    enableHelmet: true,
    enableHttps: true,
  },

  database: {
    connectionPool: {
      min: 2,
      max: 20,
      acquireTimeoutMillis: 30000,
      idleTimeoutMillis: 600000,
    },
    enableReadReplicas: true,
    enableConnectionRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
  },

  externalServices: {
    zap: {
      url: process.env.ZAP_PROXY_URL || 'http://localhost:8080',
      timeout: 120000,
      maxRetries: 3,
      healthCheckInterval: 60000,
    },
    ssl: {
      timeout: 30000,
      maxRetries: 2,
      enableCaching: true,
      cacheTimeout: 3600000, // 1 hour
    },
  },

  environment: {
    nodeEnv: 'production',
    port: parseInt(process.env.PORT || '3000'),
    host: process.env.HOST || '0.0.0.0',
    enableCluster: true,
    workerCount: parseInt(process.env.WORKER_COUNT || '0'), // 0 = auto-detect
    enableGracefulShutdown: true,
    shutdownTimeout: 30000,
  },
};

// Staging configuration (less aggressive timeouts)
export const STAGING_CONFIG: ProductionConfig = {
  ...PRODUCTION_CONFIG,
  timeouts: {
    ...PRODUCTION_CONFIG.timeouts,
    scanTotal: 240000, // 4 minutes
    sslAnalysis: 25000, // 25 seconds
    zapScanning: 90000, // 1.5 minutes
  },
  performance: {
    ...PRODUCTION_CONFIG.performance,
    maxConcurrentScans: 5,
    maxConcurrentTests: 3,
  },
  monitoring: {
    ...PRODUCTION_CONFIG.monitoring,
    logLevel: 'debug',
  },
  environment: {
    ...PRODUCTION_CONFIG.environment,
    nodeEnv: 'staging',
    enableCluster: false,
  },
};

// Development configuration (more lenient)
export const DEVELOPMENT_CONFIG: ProductionConfig = {
  ...PRODUCTION_CONFIG,
  timeouts: {
    ...PRODUCTION_CONFIG.timeouts,
    scanTotal: 180000, // 3 minutes
    sslAnalysis: 20000, // 20 seconds
    zapScanning: 60000, // 1 minute
  },
  reliability: {
    ...PRODUCTION_CONFIG.reliability,
    maxRetries: 2,
    enablePartialResults: true,
  },
  performance: {
    ...PRODUCTION_CONFIG.performance,
    maxConcurrentScans: 3,
    maxConcurrentTests: 2,
    enableCaching: false,
  },
  monitoring: {
    ...PRODUCTION_CONFIG.monitoring,
    logLevel: 'debug',
    enableTracing: false,
  },
  security: {
    ...PRODUCTION_CONFIG.security,
    enableRateLimiting: false,
    enableHttps: false,
    corsOrigins: ['http://localhost:3000', 'http://localhost:3001'],
  },
  environment: {
    ...PRODUCTION_CONFIG.environment,
    nodeEnv: 'development',
    enableCluster: false,
    workerCount: 1,
  },
};

// Configuration factory
export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: ProductionConfig;

  private constructor() {
    this.config = this.loadConfiguration();
  }

  static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  private loadConfiguration(): ProductionConfig {
    const env = process.env.NODE_ENV || 'development';

    switch (env) {
      case 'production':
        return this.mergeWithEnvironmentVariables(PRODUCTION_CONFIG);
      case 'staging':
        return this.mergeWithEnvironmentVariables(STAGING_CONFIG);
      case 'development':
      default:
        return this.mergeWithEnvironmentVariables(DEVELOPMENT_CONFIG);
    }
  }

  private mergeWithEnvironmentVariables(baseConfig: ProductionConfig): ProductionConfig {
    return {
      ...baseConfig,
      timeouts: {
        ...baseConfig.timeouts,
        scanTotal: parseInt(process.env.SCAN_TIMEOUT || baseConfig.timeouts.scanTotal.toString()),
        sslAnalysis: parseInt(
          process.env.SSL_TIMEOUT || baseConfig.timeouts.sslAnalysis.toString(),
        ),
        zapScanning: parseInt(
          process.env.ZAP_TIMEOUT || baseConfig.timeouts.zapScanning.toString(),
        ),
      },
      reliability: {
        ...baseConfig.reliability,
        maxRetries: parseInt(
          process.env.MAX_RETRIES || baseConfig.reliability.maxRetries.toString(),
        ),
        enablePartialResults:
          process.env.ENABLE_PARTIAL_RESULTS === 'true' ||
          baseConfig.reliability.enablePartialResults,
      },
      performance: {
        ...baseConfig.performance,
        maxConcurrentScans: parseInt(
          process.env.MAX_CONCURRENT_SCANS || baseConfig.performance.maxConcurrentScans.toString(),
        ),
        memoryLimit: parseInt(
          process.env.MEMORY_LIMIT || baseConfig.performance.memoryLimit.toString(),
        ),
      },
      monitoring: {
        ...baseConfig.monitoring,
        enableMetrics: process.env.ENABLE_METRICS === 'true' || baseConfig.monitoring.enableMetrics,
        logLevel:
          (process.env.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') ||
          baseConfig.monitoring.logLevel,
      },
      database: {
        ...baseConfig.database,
        connectionPool: {
          ...baseConfig.database.connectionPool,
          max: parseInt(
            process.env.DB_POOL_MAX || baseConfig.database.connectionPool.max.toString(),
          ),
        },
      },
      externalServices: {
        ...baseConfig.externalServices,
        zap: {
          ...baseConfig.externalServices.zap,
          url: process.env.ZAP_PROXY_URL || baseConfig.externalServices.zap.url,
        },
      },
    };
  }

  getConfig(): ProductionConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<ProductionConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  // Validation methods
  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate timeouts
    if (this.config.timeouts.scanTotal < 60000) {
      errors.push('Scan total timeout should be at least 60 seconds');
    }

    if (this.config.timeouts.sslAnalysis < 5000) {
      errors.push('SSL analysis timeout should be at least 5 seconds');
    }

    // Validate performance limits
    if (this.config.performance.maxConcurrentScans < 1) {
      errors.push('Max concurrent scans should be at least 1');
    }

    if (this.config.performance.memoryLimit < 256 * 1024 * 1024) {
      errors.push('Memory limit should be at least 256MB');
    }

    // Validate database configuration
    if (this.config.database.connectionPool.max < this.config.database.connectionPool.min) {
      errors.push('Database pool max should be greater than or equal to min');
    }

    // Validate external services
    if (!this.config.externalServices.zap.url) {
      errors.push('ZAP proxy URL is required');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Environment-specific optimizations
  optimizeForEnvironment(): void {
    const env = this.config.environment.nodeEnv;

    if (env === 'production') {
      // Production optimizations
      this.config.monitoring.enableTracing = true;
      this.config.performance.enableCaching = true;
      this.config.security.enableRateLimiting = true;
    } else if (env === 'development') {
      // Development optimizations
      this.config.monitoring.logLevel = 'debug';
      this.config.performance.enableCaching = false;
      this.config.security.enableRateLimiting = false;
    }
  }
}
