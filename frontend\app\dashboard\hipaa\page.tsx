'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { HipaaDashboard, HipaaDashboardData } from '@/components/dashboard/hipaa';
import { Card, CardContent } from '@/components/ui/Card';
import { AlertTriangle } from 'lucide-react';
import { hipaaDashboardService } from '@/services/hipaa-dashboard-api';

/**
 * Main HIPAA Compliance Dashboard Page
 * Provides unified view of HIPAA privacy and security compliance
 */
export default function HipaaDashboardPage() {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<HipaaDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load dashboard data from API
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await hipaaDashboardService.getDashboardData();
      setDashboardData(data);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadDashboardData();
  };

  const handleStartPrivacyScan = () => {
    router.push('/dashboard/hipaa/privacy');
  };

  const handleStartSecurityScan = () => {
    router.push('/dashboard/hipaa/security');
  };

  const handleViewPrivacyResults = () => {
    router.push('/dashboard/hipaa/privacy');
  };

  const handleViewSecurityResults = () => {
    router.push('/dashboard/hipaa/security');
  };

  if (!dashboardData && !loading && !error) {
    return (
      <div
        className="container mx-auto p-6"
        style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}
      >
        <Card style={{ borderColor: '#E5E5E5', backgroundColor: 'white' }}>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4" style={{ color: '#999999' }} />
              <h3 className="text-lg font-semibold mb-2" style={{ color: '#333333' }}>
                No Data Available
              </h3>
              <p style={{ color: '#666666' }}>Unable to load dashboard data.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F5F5F5', color: '#333333' }}>
      <div className="container mx-auto p-6">
        <HipaaDashboard
          data={
            dashboardData || {
              overview: {
                overallScore: 0,
                riskLevel: 'medium',
                complianceStatus: 'non_compliant',
                lastScanDate: new Date().toISOString(),
                totalScans: 0,
              },
              privacyModule: {
                scanCount: 0,
                status: 'not_scanned',
              },
              securityModule: {
                scanCount: 0,
                status: 'not_scanned',
              },
            }
          }
          loading={loading}
          error={error || undefined}
          onRefresh={handleRefresh}
          onStartPrivacyScan={handleStartPrivacyScan}
          onStartSecurityScan={handleStartSecurityScan}
          onViewPrivacyResults={handleViewPrivacyResults}
          onViewSecurityResults={handleViewSecurityResults}
        />
      </div>
    </div>
  );
}
