'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Loader2, ArrowLeft, TestTube, ExternalLink } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Badge } from '@/components/ui/Badge';
import { useAuth } from '@/context/AuthContext';
import { submitScan, getScanDetails } from '@/lib/api';
// Types will be added as needed to replace any types
// HipaaResultsDisplay removed - using SimpleHipaaResults instead

// Sample URLs for quick testing
const SAMPLE_URLS = [
  {
    name: 'Mayo Clinic',
    url: 'https://www.mayoclinic.org/about-mayo-clinic/privacy-policy',
    description: 'Large healthcare provider privacy policy',
  },
  {
    name: 'Cleveland Clinic',
    url: 'https://www.clevelandclinic.org/privacy-policy',
    description: 'Hospital system privacy policy',
  },
  {
    name: 'Kaiser Permanente',
    url: 'https://www.kp.org/privacy',
    description: 'Health insurance provider privacy policy',
  },
  {
    name: 'Example.com',
    url: 'https://example.com',
    description: 'Non-healthcare site for comparison',
  },
];

/**
 * Test page for enhanced HIPAA analysis system
 * Allows testing the 3-level HIPAA compliance analysis with real URLs
 * @returns {JSX.Element} JSX element for the test page
 */
export default function TestHipaaPage() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanResult, setScanResult] = useState<Record<string, unknown> | null>(null);
  const [scanId, setScanId] = useState<string | null>(null);
  const { authenticated } = useAuth();

  // Add a test to verify frontend is working
  React.useEffect(() => {
    // Frontend test - page loaded successfully
  }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setScanResult(null);
    setScanId(null);

    if (!authenticated) {
      setError('Please log in to test HIPAA analysis');
      return;
    }

    if (!url.trim()) {
      setError('Please enter a valid URL');
      return;
    }

    try {
      new URL(url);
    } catch (_) {
      setError('Please enter a valid URL format (e.g., https://example.com)');
      return;
    }

    setIsLoading(true);
    try {
      // Submit scan with enhanced HIPAA analysis
      const response = await submitScan({
        url,
        standards: ['hipaa'],
        // Add options for enhanced analysis
        options: {
          enableEnhancedAnalysis: true,
          analysisDepth: 'comprehensive',
          includePerformanceMetrics: true,
        },
      });

      setScanId(response.id);

      // Poll for real results from the backend
      const pollForResults = async (scanId: string, attempts = 0) => {
        if (attempts > 90) {
          // Max 90 attempts (90 seconds) - increased for enhanced analysis
          setError(
            'Enhanced analysis timed out after 90 seconds. Please try again or check the scan details page.',
          );
          setIsLoading(false);
          return;
        }

        try {
          const scanData = await getScanDetails(scanId);

          if (scanData.status === 'completed') {
            // Check if enhanced HIPAA results are available
            if (scanData.enhancedHipaaResults) {
              // Use real backend results

              // Extract scores directly from backend data
              const enhanced = scanData.enhancedHipaaResults;
              const comprehensiveCheck = enhanced?.checksBreakdown?.find(
                (check: Record<string, unknown>) => check.checkId === 'HIPAA-COMPREHENSIVE-001',
              );

              if (comprehensiveCheck?.analysisLevels) {
                const levels = comprehensiveCheck.analysisLevels;
                const level1Score = parseFloat(
                  ((levels as Record<string, unknown>[]).find(
                    (l: Record<string, unknown>) => l.level === 1,
                  )?.score as string) || '0',
                );
                const level2Score = parseFloat(
                  ((levels as Record<string, unknown>[]).find(
                    (l: Record<string, unknown>) => l.level === 2,
                  )?.score as string) || '0',
                );
                const level3Score = parseFloat(
                  ((levels as Record<string, unknown>[]).find(
                    (l: Record<string, unknown>) => l.level === 3,
                  )?.score as string) || '0',
                );

                const privacyCheck = enhanced.checksBreakdown?.find(
                  (check: Record<string, unknown>) => check.checkId === 'HIPAA-PP-001',
                );

                const directResults = {
                  overallScore: parseFloat(enhanced.overallScore) || 0,
                  privacyPolicyPresent: privacyCheck?.passed || false,
                  privacyPolicyUrl: privacyCheck?.passed ? 'Found' : undefined,
                  contentAnalysisScore: parseFloat(enhanced.overallScore) || 0,
                  contactInformationScore: Math.max(
                    0,
                    (parseFloat(enhanced.overallScore) || 0) - 10,
                  ),
                  analysisLevels: {
                    level1: {
                      score: level1Score,
                      method: 'pattern_matching' as const,
                      findings: [],
                      processingTime:
                        ((levels as Record<string, unknown>[]).find(
                          (l: Record<string, unknown>) => l.level === 1,
                        )?.processingTime as number) || 0,
                    },
                    level2: {
                      score: level2Score,
                      method: 'nlp_analysis' as const,
                      entities: { people: [], organizations: [], phoneNumbers: [], emails: [] },
                      statements: { privacy: 0, rights: 0 },
                      processingTime:
                        ((levels as Record<string, unknown>[]).find(
                          (l: Record<string, unknown>) => l.level === 2,
                        )?.processingTime as number) || 0,
                    },
                    level3: {
                      score: level3Score,
                      method: 'ai_analysis' as const,
                      complianceGaps: [],
                      recommendations: [],
                      processingTime:
                        ((levels as Record<string, unknown>[]).find(
                          (l: Record<string, unknown>) => l.level === 3,
                        )?.processingTime as number) || 0,
                    },
                  },
                  findings: [],
                  recommendations: enhanced.recommendations || [],
                  totalProcessingTime: enhanced.metadata?.processingTime || 0,
                  analysisTimestamp: new Date().toISOString(),
                };

                setScanResult(directResults);
              } else {
                setScanResult(null);
              }
              setIsLoading(false);
            } else {
              // Scan is completed but enhanced results not yet available
              if (attempts > 60) {
                // After 60 seconds, show a message with link to scan details
                setError(
                  `Enhanced analysis is taking longer than expected. You can view the results at: /dashboard/scans/${scanId}`,
                );
                setIsLoading(false);
              } else {
                // Continue polling for enhanced results with a longer delay for database consistency

                // Use a 2-second delay to give the database more time to commit enhanced results
                setTimeout(() => pollForResults(scanId, attempts + 1), 2000);
              }
            }
          } else if (scanData.status === 'failed') {
            setError(`Analysis failed: ${scanData.error || 'Unknown error'}`);
            setIsLoading(false);
          } else {
            // Still processing, poll again

            setTimeout(() => pollForResults(scanId, attempts + 1), 1000);
          }
        } catch (error) {
          setError(
            `Failed to get analysis results: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          setIsLoading(false);
        }
      };

      // Transform backend results to match frontend interface (unused but kept for reference)
      /* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any */
      const transformBackendResults = (scanData: any, url: string) => {
        // Extract HIPAA results from enhanced backend response
        const enhancedResults = scanData.enhancedHipaaResults;

        if (!enhancedResults) {
          throw new Error('No enhanced HIPAA analysis results found in backend response');
        }

        // FIXED: The backend returns checksBreakdown, not checks
        const checksBreakdown = enhancedResults.checksBreakdown || enhancedResults.checks || [];
        const hipaaContentCheck = checksBreakdown.find(
          (check: any) => check.analysisLevels && check.analysisLevels.length > 0,
        );

        // COMPREHENSIVE FIX: Extract level results from multiple possible locations
        let analysisLevels = [];
        let level1 = {};
        let level2 = {};
        let level3 = {};

        // Try multiple data structure formats
        if (hipaaContentCheck?.analysisLevels && Array.isArray(hipaaContentCheck.analysisLevels)) {
          // Format 1: analysisLevels as array
          analysisLevels = hipaaContentCheck.analysisLevels;
        } else if (hipaaContentCheck?.levelResults) {
          // Format 2: levelResults as object with level1, level2, level3 keys
          const levelResults = hipaaContentCheck.levelResults;
          analysisLevels = [
            levelResults.level1 && { ...levelResults.level1, level: 1 },
            levelResults.level2 && { ...levelResults.level2, level: 2 },
            levelResults.level3 && { ...levelResults.level3, level: 3 },
          ].filter(Boolean);
        } else {
          // Format 3: Check if data is nested elsewhere or use fallback

          // Fallback: Try to extract from any nested structure
          if (hipaaContentCheck) {
            const checkData = hipaaContentCheck;
            // Look for any properties that might contain level data
            Object.keys(checkData).forEach((key) => {
              const value = checkData[key];
              if (
                value &&
                typeof value === 'object' &&
                (value.level1 || value.level2 || value.level3)
              ) {
                analysisLevels = [
                  value.level1 && { ...value.level1, level: 1 },
                  value.level2 && { ...value.level2, level: 2 },
                  value.level3 && { ...value.level3, level: 3 },
                ].filter(Boolean);
              }
            });
          }
        }

        // Extract individual levels
        level1 = analysisLevels.find((level: any) => level.level === 1) || {};
        level2 = analysisLevels.find((level: any) => level.level === 2) || {};
        level3 = analysisLevels.find((level: any) => level.level === 3) || {};

        // FIXED: Parse scores correctly from string format with robust error handling
        const parseScore = (scoreValue: any): number => {
          if (typeof scoreValue === 'number') return scoreValue;
          if (typeof scoreValue === 'string') {
            const parsed = parseFloat(scoreValue);
            return isNaN(parsed) ? 0 : parsed;
          }
          return 0;
        };

        const level1Score = parseScore((level1 as any).score);
        const level2Score = parseScore((level2 as any).score);
        const level3Score = parseScore((level3 as any).score);

        // FIXED: Use the parsed scores and ensure proper data transformation
        return {
          overallScore: parseFloat(enhancedResults.overallScore) || 0,
          privacyPolicyPresent:
            checksBreakdown.some(
              (check: any) => check.name.includes('Privacy Policy') && check.passed,
            ) || false,
          privacyPolicyUrl: url.includes('privacy') ? url : undefined,
          contentAnalysisScore: parseFloat(enhancedResults.overallScore) || 0,
          contactInformationScore: Math.max(
            0,
            (parseFloat(enhancedResults.overallScore) || 0) - 10,
          ),

          analysisLevels: {
            level1: {
              score: level1Score, // Use parsed score
              method: 'pattern_matching' as const,
              findings:
                (level1 as any).findings?.map(
                  (f: any) => f.content || f.requirement || f.text || 'Pattern found',
                ) || [],
              processingTime: (level1 as any).processingTime || 0,
            },
            level2: {
              score: level2Score, // Use parsed score
              method: 'nlp_analysis' as const,
              entities: {
                people: (level2 as any).entities?.people || [],
                organizations: (level2 as any).entities?.organizations || [],
                phoneNumbers: (level2 as any).entities?.phoneNumbers || [],
                emails: (level2 as any).entities?.emails || [],
              },
              statements: {
                privacy: (level2 as any).privacyStatements?.length || 0,
                rights: (level2 as any).rightsStatements?.length || 0,
              },
              processingTime: (level2 as any).processingTime || 0,
            },
            level3: {
              score: level3Score, // Use parsed score
              method: 'ai_analysis' as const,
              complianceGaps: (level3 as any).identifiedGaps || [],
              recommendations: (level3 as any).recommendations || [],
              processingTime: (level3 as any).processingTime || 0,
            },
          },

          findings: checksBreakdown.flatMap((check: any) => check.findings || []) || [],
          recommendations: enhancedResults.recommendations || [],
          totalProcessingTime: enhancedResults.metadata?.processingTime || 0,
          analysisTimestamp:
            enhancedResults.timestamp || scanData.completed_at || new Date().toISOString(),
        };
      };
      /* eslint-enable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any */

      // Start polling for results
      pollForResults(response.id);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Failed to submit scan');
      } else {
        setError('Failed to submit scan');
      }
      setIsLoading(false);
    }
  };

  const handleSampleUrl = (sampleUrl: string) => {
    setUrl(sampleUrl);
    setError(null);
    setScanResult(null);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/scan/new">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Scan
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            Test Enhanced HIPAA Analysis
          </h1>
          <p className="text-muted-foreground">
            Test the new 3-level HIPAA compliance analysis system with real URLs
          </p>
        </div>
      </div>

      {/* URL Input Form */}
      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Enter URL to Analyze</CardTitle>
            <CardDescription>
              Enter a website URL to test the enhanced HIPAA privacy policy analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="url">Website URL</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com/privacy-policy"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>

            {/* Sample URLs */}
            <div className="space-y-2">
              <Label>Quick Test URLs</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {SAMPLE_URLS.map((sample, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    type="button"
                    onClick={() => handleSampleUrl(sample.url)}
                    disabled={isLoading}
                    className="justify-start h-auto p-3"
                  >
                    <div className="text-left">
                      <div className="font-medium">{sample.name}</div>
                      <div className="text-xs text-muted-foreground">{sample.description}</div>
                    </div>
                    <ExternalLink className="h-3 w-3 ml-auto" />
                  </Button>
                ))}
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {scanId && !scanResult && (
              <Alert>
                <AlertDescription>
                  Scan submitted successfully! Scan ID: {scanId}
                  <br />
                  Running enhanced 3-level analysis...
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing with 3-Level System...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  Run Enhanced HIPAA Analysis
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {/* Results Display */}
      {scanResult && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Analysis Complete</Badge>
            <span className="text-sm text-muted-foreground">
              Processed in {(scanResult.totalProcessingTime as number) || 0}ms
            </span>
          </div>
          {/* TODO: Replace with SimpleHipaaResults component */}
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p>
              Enhanced HIPAA results would be displayed here using SimpleHipaaResults component.
            </p>
            <pre className="text-xs mt-2 overflow-auto">{JSON.stringify(scanResult, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
}
