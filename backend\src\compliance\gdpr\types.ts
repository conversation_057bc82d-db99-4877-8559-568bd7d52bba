// GDPR Compliance Module - Type Definitions

/**
 * Represents the result of a single GDPR compliance check.
 */
export interface GdprCheckResult {
  checkId: string; // e.g., GDPR-CC-001 (<PERSON><PERSON>)
  name: string; // e.g., "<PERSON><PERSON> Banner Check"
  passed: boolean;
  description: string; // Summary of the check's finding
  details?: Record<string, unknown> | string; // More detailed findings or raw data
  // Add other GDPR-specific fields as needed
}

// Add other shared types for the GDPR module here
