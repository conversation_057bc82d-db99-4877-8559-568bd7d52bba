{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".", // Base for path resolution is this tsconfig's directory
    "paths": {
      "@lib/*": ["../lib/*", "./lib/*"] // Support both local dev and Docker paths
    },
    "outDir": "./dist", // Output directory relative to backend directory
    "module": "commonjs",
    "target": "es2020",
    "sourceMap": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "noEmit": false, // We want to emit JS files for the backend
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "isolatedModules": false,
    "declaration": true,
    "declarationMap": true
  },
  "include": [
    "src/**/*.ts", // Backend source files
    "../lib/**/*.ts", // Shared lib files
    "knexfile.ts", // Knex configuration
    "jest.config.js", // Jest configuration
    "preload-env.js", // Environment preloader
    ".eslintrc.js", // ESLint configuration
    "babel.config.js", // Babel configuration
    "./knexfile.d.ts"   // Knex declaration file
  ],
  "exclude": ["node_modules"]
}
