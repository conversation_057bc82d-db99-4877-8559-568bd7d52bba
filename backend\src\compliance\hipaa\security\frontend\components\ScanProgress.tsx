/**
 * Enhanced Scan Progress Component
 * Handles timeouts, retries, and progressive loading
 */

import React, { useState, useEffect } from 'react';
import { ScanProgress, ScanError, PerformanceMetrics } from '../types';

interface ScanProgressProps {
  progress: ScanProgress;
  onCancel: () => void;
  onRetry?: () => void;
  showPerformanceMetrics?: boolean;
}

export const EnhancedScanProgress: React.FC<ScanProgressProps> = ({
  progress,
  onCancel,
  onRetry,
  showPerformanceMetrics = false
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - progress.startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [progress.startTime]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPhaseIcon = (phase: string): string => {
    switch (phase) {
      case 'initializing': return '🔄';
      case 'discovery': return '🔍';
      case 'testing': return '🧪';
      case 'analysis': return '📊';
      case 'complete': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getPhaseDescription = (phase: string): string => {
    switch (phase) {
      case 'initializing': return 'Setting up scan environment...';
      case 'discovery': return 'Discovering pages and endpoints...';
      case 'testing': return 'Running HIPAA compliance tests...';
      case 'analysis': return 'Analyzing results and calculating scores...';
      case 'complete': return 'Scan completed successfully!';
      case 'error': return 'Scan encountered an error';
      default: return 'Processing...';
    }
  };

  const isStalled = elapsedTime > progress.estimatedTimeRemaining + 30;
  const hasTimeouts = progress.timeouts.length > 0;
  const hasRetries = progress.retries > 0;

  return (
    <div className="scan-progress-container">
      {/* Main Progress Display */}
      <div className="progress-header">
        <div className="phase-indicator">
          <span className="phase-icon">{getPhaseIcon(progress.phase)}</span>
          <div className="phase-info">
            <h3 className="phase-title">{progress.phase.toUpperCase()}</h3>
            <p className="phase-description">{getPhaseDescription(progress.phase)}</p>
          </div>
        </div>
        
        <div className="time-info">
          <div className="elapsed-time">
            <span className="label">Elapsed:</span>
            <span className="value">{formatTime(elapsedTime)}</span>
          </div>
          {progress.estimatedTimeRemaining > 0 && (
            <div className="remaining-time">
              <span className="label">Remaining:</span>
              <span className="value">{formatTime(progress.estimatedTimeRemaining)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="progress-bar-container">
        <div className="progress-bar">
          <div 
            className={`progress-fill ${isStalled ? 'stalled' : ''}`}
            style={{ width: `${progress.progress}%` }}
          />
        </div>
        <div className="progress-text">
          {progress.progress.toFixed(1)}% ({progress.testsCompleted}/{progress.totalTests} tests)
        </div>
      </div>

      {/* Current Test */}
      {progress.currentTest && (
        <div className="current-test">
          <span className="label">Current Test:</span>
          <span className="test-name">{progress.currentTest}</span>
        </div>
      )}

      {/* Warnings and Issues */}
      {(hasTimeouts || hasRetries || isStalled) && (
        <div className="progress-warnings">
          {isStalled && (
            <div className="warning stalled">
              ⚠️ Scan appears to be stalled. This may indicate network issues.
            </div>
          )}
          
          {hasTimeouts && (
            <div className="warning timeouts">
              ⏱️ {progress.timeouts.length} test(s) experienced timeouts: {progress.timeouts.join(', ')}
            </div>
          )}
          
          {hasRetries && (
            <div className="warning retries">
              🔄 {progress.retries} retry attempt(s) made
            </div>
          )}
        </div>
      )}

      {/* Detailed Progress */}
      <div className="progress-details">
        <button 
          className="toggle-details"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? '▼' : '▶'} {showDetails ? 'Hide' : 'Show'} Details
        </button>
        
        {showDetails && (
          <div className="details-content">
            <div className="test-progress">
              <h4>Test Progress</h4>
              <div className="test-categories">
                <div className="category">
                  <span className="category-name">Access Control</span>
                  <span className="category-status">✅ Complete</span>
                </div>
                <div className="category">
                  <span className="category-name">Authentication</span>
                  <span className="category-status">🔄 Running</span>
                </div>
                <div className="category">
                  <span className="category-name">Transmission Security</span>
                  <span className="category-status">⏳ Pending</span>
                </div>
                <div className="category">
                  <span className="category-name">ePHI Detection</span>
                  <span className="category-status">⏳ Pending</span>
                </div>
                <div className="category">
                  <span className="category-name">Audit Controls</span>
                  <span className="category-status">⏳ Pending</span>
                </div>
              </div>
            </div>

            {showPerformanceMetrics && (
              <div className="performance-metrics">
                <h4>Performance Metrics</h4>
                <div className="metrics-grid">
                  <div className="metric">
                    <span className="metric-label">Average Response Time</span>
                    <span className="metric-value">1.2s</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Success Rate</span>
                    <span className="metric-value">94%</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Network Latency</span>
                    <span className="metric-value">45ms</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="progress-actions">
        <button 
          className="btn btn-secondary"
          onClick={onCancel}
        >
          Cancel Scan
        </button>
        
        {(isStalled || hasTimeouts) && onRetry && (
          <button 
            className="btn btn-primary"
            onClick={onRetry}
          >
            Retry Failed Tests
          </button>
        )}
      </div>

      {/* Helpful Tips */}
      <div className="progress-tips">
        <h4>💡 Tips</h4>
        <ul>
          <li>HIPAA scans typically take 2-5 minutes depending on site complexity</li>
          <li>SSL analysis may take longer for sites with complex certificate chains</li>
          <li>You can cancel and resume scans at any time</li>
          {hasTimeouts && (
            <li>Network timeouts are normal for some tests - partial results will be available</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default EnhancedScanProgress;
