// backend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'], // Specifies the root directory for Jest to scan for tests and modules
  modulePaths: ['<rootDir>/src', '<rootDir>/../lib'], // Helps Jest resolve modules from these paths
  testMatch: ['**/__tests__/**/*.+(ts|tsx|js)', '**/?(*.)+(spec|test).+(ts|tsx|js)'],
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json', // Points ts-jest to your backend's tsconfig
      // isolatedModules: true, // Can speed up transpilation but skips type-checking. Use with caution.
    },
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    // Ensure this aligns with your tsconfig.json paths
    '^@lib/(.*)$': '<rootDir>/../lib/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@compliance/(.*)$': '<rootDir>/src/compliance/$1',
    '^@routes/(.*)$': '<rootDir>/src/routes/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
    '^@customTypes/(.*)$': '<rootDir>/src/types/$1', // Assuming 'types' is aliased as '@customTypes' or similar
    // ES module mocks
    '^@xenova/transformers$': '<rootDir>/src/__mocks__/@xenova/transformers.js',
  },
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
  // Transform ES modules from node_modules
  transformIgnorePatterns: ['node_modules/(?!(@xenova/transformers|axios)/)'],
  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',
  // Indicates which provider should be used to instrument code for coverage
  coverageProvider: 'v8', // or "babel"
  // Add setup files if you have any global test setup
  // setupFilesAfterEnv: ['<rootDir>/src/jest.setup.ts'], // Example
};
