import knex, { Knex as KnexType } from 'knex';
import knexConfig from '../../knexfile'; // Adjust path if knexfile is elsewhere
import { env } from '@lib/env'; // Assuming env is in backend/src/lib/env.ts or similar

// Determine the environment
const environment = env.NODE_ENV || 'development';
const dbConfig = knexConfig[environment];

if (!dbConfig) {
  throw new Error(`Knex configuration not found for environment: ${environment}`);
}

// Initialize Knex with the configuration for the current environment
const db: KnexType = knex(dbConfig);

// Optional: Test the connection
db.raw('SELECT 1+1 AS result')
  .then(() => {
    console.log(`Successfully connected to the database using environment: ${environment}`);
  })
  .catch((err) => {
    console.error(`Failed to connect to the database using environment: ${environment}`, err);
    // Depending on your error handling strategy, you might want to exit the process
    // process.exit(1);
  });

export default db;
