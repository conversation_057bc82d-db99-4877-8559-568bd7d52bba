// backend/src/compliance/hipaa/privacy/checks/privacy-policy-presence.ts

/**
 * Enhanced Privacy Policy Presence Check
 * Comprehensive detection of privacy policy links with accessibility validation
 */

import {
  HipaaCheckResult,
  HipaaCheckCategory,
  HipaaSeverity,
  HipaaFindingType,
  HipaaFinding,
  CheckOptions,
  HipaaRemediation,
  HipaaEvidence,
  HipaaResource,
} from '../types';
import { URLResolver } from '../utils/url-resolver';
import { ContentAnalyzer } from '../utils/content-analyzer';

/**
 * Enhanced privacy policy presence check with comprehensive validation
 * @param targetUrl - The URL to check for privacy policy presence
 * @param options - Configuration options for the check
 * @returns Promise<HipaaCheckResult> - Detailed check result
 */
export async function checkPrivacyPolicyPresence(
  targetUrl: string,
  options: CheckOptions = {},
): Promise<HipaaCheckResult> {
  const startTime = Date.now();

  const result: HipaaCheckResult = {
    checkId: 'HIPAA-PP-001',
    name: 'Privacy Policy Presence',
    category: HipaaCheckCategory.PRESENCE,
    passed: false,
    severity: HipaaSeverity.CRITICAL,
    confidence: 0,
    description: 'Validates the presence and accessibility of privacy policy links on the website',
    details: {
      summary: '',
      findings: [],
      metrics: {
        processingTime: 0,
        contentLength: 0,
        linksFound: 0,
        accessibilityScore: 0,
      },
      context: {
        url: targetUrl,
        pageTitle: '',
        lastModified: '',
        contentType: '',
        language: '',
      },
    },
    remediation: {
      priority: 'critical',
      effort: 'minimal',
      steps: [],
      resources: [],
      timeline: '1-2 days',
      estimatedCost: '$500 - $1,000',
    },
    evidence: [],
    metadata: {
      checkVersion: '2.0',
      processingTime: 0,
      analysisLevels: [1],
      warnings: [],
    },
  };

  try {
    // Step 1: Fetch page content
    const htmlContent = await URLResolver.fetchPageContent(targetUrl, {
      timeout: options.timeout || 30000,
      userAgent: options.userAgent,
    });

    result.details.metrics.contentLength = htmlContent.length;
    result.details.context.contentType = 'text/html';

    // Step 2: Extract page metadata
    const pageTitle = ContentAnalyzer.extractPageTitle(htmlContent);
    const language = ContentAnalyzer.detectLanguage(htmlContent);

    result.details.context.pageTitle = pageTitle;
    result.details.context.language = language;

    // Step 3: Find privacy policy links
    const privacyLinks = await findPrivacyPolicyLinks(htmlContent, targetUrl);
    result.details.metrics.linksFound = privacyLinks.length;

    // Step 4: Validate accessibility
    const accessibilityScore = await validateAccessibility(privacyLinks, htmlContent);
    result.details.metrics.accessibilityScore = accessibilityScore;

    // Step 5: Check for multiple formats (HTML, PDF)
    const formatSupport = await checkFormatSupport(privacyLinks);

    // Step 6: Validate mobile responsiveness
    const mobileScore = await validateMobileResponsiveness(htmlContent);

    // Step 7: Calculate overall result
    const { passed, confidence, findings } = calculatePresenceResult(
      privacyLinks,
      accessibilityScore,
      formatSupport,
      mobileScore,
    );

    result.passed = passed;
    result.confidence = confidence;
    result.details.findings = findings;

    // Step 8: Generate remediation guidance
    if (!passed) {
      result.remediation = generateRemediationGuidance(findings);
      result.severity = determineSeverity(findings);
    } else {
      result.severity = HipaaSeverity.INFO;
      result.remediation.priority = 'low';
      result.remediation.steps = ['Privacy policy presence validated successfully'];
    }

    // Step 9: Collect evidence
    result.evidence = collectEvidence(privacyLinks, htmlContent);

    // Step 10: Generate summary
    result.details.summary = generateSummary(result);
  } catch (error) {
    // Handle errors gracefully
    result.passed = false;
    result.confidence = 0;
    result.severity = HipaaSeverity.CRITICAL;
    result.details.summary = `Error during privacy policy presence check: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    result.details.findings.push({
      type: HipaaFindingType.ERROR,
      location: 'Check execution',
      content: error instanceof Error ? error.message : 'Unknown error occurred',
      severity: HipaaSeverity.CRITICAL,
      message: 'Failed to complete privacy policy presence check',
      suggestion: 'Verify URL accessibility and try again',
      context: `Error: ${error instanceof Error ? error.name : 'Error'}`,
      confidence: 100,
    });
    (result.metadata.warnings = result.metadata.warnings || []).push(
      `Check failed with error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
    );
  }

  // Final processing
  const processingTime = Date.now() - startTime;
  result.details.metrics.processingTime = processingTime;
  result.metadata.processingTime = processingTime;

  return result;
}

/**
 * Find privacy policy links in HTML content
 */
async function findPrivacyPolicyLinks(
  htmlContent: string,
  baseUrl: string,
): Promise<PrivacyLink[]> {
  console.log('🔍 [Privacy Policy Detection] Starting link detection for base URL:', baseUrl);
  console.log('📄 [Privacy Policy Detection] HTML content length:', htmlContent.length);

  const links: PrivacyLink[] = [];

  // Enhanced privacy policy link patterns (matches url-resolver.ts)
  const privacyPatterns = [
    /privacy\s*policy/i,
    /privacy\s*notice/i,
    /hipaa\s*notice/i,
    /notice\s*of\s*privacy\s*practices/i,
    /privacy\s*statement/i,
    /data\s*privacy/i,
    /\bprivacy\b/i, // Just "Privacy" link
    /data\s*protection/i,
    /cookie\s*policy/i,
    /terms\s*.*\s*privacy/i,
    // Enhanced patterns for healthcare/insurance sites
    /legal\s*notices?/i,
    /legal\s*&?\s*policy\s*info/i,
    /plan\s*disclosures?/i,
    /privacy\s*center/i,
    /privacy\s*hub/i,
    /program\s*provisions/i,
    /member\s*privacy/i,
    /patient\s*privacy/i,
    /health\s*information\s*privacy/i,
    /protected\s*health\s*information/i,
    /phi\s*policy/i,
    /compliance\s*notices?/i,
    /regulatory\s*notices?/i,
    /legal\s*information/i,
    /terms\s*of\s*use/i, // Often contains privacy info
    /website\s*security/i,
    /fraud\s*prevention/i, // Healthcare sites often link privacy from fraud prevention
    /non.?discrimination\s*notice/i,
  ];

  console.log(
    '🎯 [Privacy Policy Detection] Using patterns:',
    privacyPatterns.map((p) => p.toString()),
  );

  // Extract all links from HTML
  const linkRegex = /<a[^>]*href\s*=\s*["']([^"']*)["'][^>]*>(.*?)<\/a>/gi;
  let match;
  let totalLinksFound = 0;

  while ((match = linkRegex.exec(htmlContent)) !== null) {
    totalLinksFound++;
    const href = match[1];
    const linkText = match[2].replace(/<[^>]*>/g, '').trim();

    // Check if link text matches privacy patterns
    const isPrivacyLink = privacyPatterns.some((pattern) => pattern.test(linkText));

    if (isPrivacyLink) {
      const fullUrl = URLResolver.resolveUrl(href, baseUrl);
      const linkFormat = detectLinkFormat(href);
      const linkContext = extractLinkContext(htmlContent, match.index);

      const privacyLink = {
        url: fullUrl || href,
        text: linkText,
        position: match.index,
        accessible: true, // Will be validated separately
        format: linkFormat,
        context: linkContext,
      };

      links.push(privacyLink);

      console.log('🎯 [Privacy Policy Detection] Found privacy policy link:', {
        url: privacyLink.url,
        linkText: linkText,
        href: href,
        format: linkFormat,
        position: match.index,
        context: linkContext.substring(0, 100) + '...',
      });
    }
  }

  console.log('✅ [Privacy Policy Detection] Link detection complete:', {
    totalLinksScanned: totalLinksFound,
    privacyPolicyLinksFound: links.length,
    discoveredUrls: links.map((link) => ({ url: link.url, text: link.text })),
  });

  return links;
}

/**
 * Validate accessibility of privacy policy links
 */
async function validateAccessibility(links: PrivacyLink[], htmlContent: string): Promise<number> {
  if (links.length === 0) return 0;

  let accessibilityScore = 0;
  let totalChecks = 0;

  for (const link of links) {
    totalChecks += 4; // 4 accessibility checks per link

    // Check 1: Link has descriptive text
    if (link.text.length > 5 && !link.text.toLowerCase().includes('click here')) {
      accessibilityScore += 25;
    }

    // Check 2: Link is not hidden or tiny
    const linkElement = extractLinkElement(htmlContent, link.position);
    if (!isHiddenOrTiny(linkElement)) {
      accessibilityScore += 25;
    }

    // Check 3: Link has proper contrast (basic check)
    if (hasProperContrast(linkElement)) {
      accessibilityScore += 25;
    }

    // Check 4: Link is keyboard accessible
    if (isKeyboardAccessible(linkElement)) {
      accessibilityScore += 25;
    }
  }

  return totalChecks > 0 ? Math.round((accessibilityScore / totalChecks) * 100) : 0;
}

/**
 * Check for multiple format support (HTML, PDF)
 */
async function checkFormatSupport(links: PrivacyLink[]): Promise<FormatSupport> {
  const support: FormatSupport = {
    html: false,
    pdf: false,
    downloadable: false,
    printable: false,
  };

  for (const link of links) {
    if (link.format === 'html') {
      support.html = true;
    } else if (link.format === 'pdf') {
      support.pdf = true;
      support.downloadable = true;
    }
  }

  // Check for print-friendly versions
  support.printable = links.some(
    (link) => link.text.toLowerCase().includes('print') || link.url.includes('print'),
  );

  return support;
}

/**
 * Validate mobile responsiveness of privacy policy access
 */
async function validateMobileResponsiveness(htmlContent: string): Promise<number> {
  let score = 0;

  // Check for viewport meta tag
  if (htmlContent.includes('viewport')) {
    score += 30;
  }

  // Check for responsive CSS
  if (htmlContent.includes('@media') || htmlContent.includes('responsive')) {
    score += 30;
  }

  // Check for mobile-friendly navigation
  if (htmlContent.includes('mobile-menu') || htmlContent.includes('hamburger')) {
    score += 20;
  }

  // Check for touch-friendly elements
  if (htmlContent.includes('touch') || htmlContent.includes('tap')) {
    score += 20;
  }

  return Math.min(score, 100);
}

/**
 * Calculate overall presence result
 */
function calculatePresenceResult(
  links: PrivacyLink[],
  accessibilityScore: number,
  formatSupport: FormatSupport,
  mobileScore: number,
): { passed: boolean; confidence: number; findings: HipaaFinding[] } {
  const findings: HipaaFinding[] = [];
  let confidence = 0;
  let passed = false;

  // Primary check: Privacy policy link exists
  if (links.length > 0) {
    passed = true;
    confidence += 40;

    findings.push({
      type: HipaaFindingType.EXACT_MATCH,
      location: 'Page navigation',
      content: `Found ${links.length} privacy policy link(s)`,
      severity: HipaaSeverity.INFO,
      message: 'Privacy policy link detected',
      suggestion: 'Ensure link leads to comprehensive privacy policy',
      context: links.map((l) => l.text).join(', '),
      confidence: 95,
    });
  } else {
    findings.push({
      type: HipaaFindingType.MISSING_CONTENT,
      location: 'Page navigation',
      content: '',
      severity: HipaaSeverity.CRITICAL,
      message: 'No privacy policy link found',
      suggestion: 'Add clearly labeled privacy policy link to main navigation',
      context: 'Required for HIPAA compliance',
      confidence: 100,
    });
  }

  // Accessibility validation
  if (accessibilityScore >= 80) {
    confidence += 25;
    findings.push({
      type: HipaaFindingType.ACCESSIBILITY_PASS,
      location: 'Privacy policy links',
      content: `Accessibility score: ${accessibilityScore}%`,
      severity: HipaaSeverity.INFO,
      message: 'Privacy policy links meet accessibility standards',
      suggestion: 'Continue maintaining accessibility best practices',
      context: 'WCAG compliance',
      confidence: accessibilityScore,
    });
  } else if (links.length > 0) {
    findings.push({
      type: HipaaFindingType.ACCESSIBILITY_ISSUE,
      location: 'Privacy policy links',
      content: `Accessibility score: ${accessibilityScore}%`,
      severity: HipaaSeverity.MEDIUM,
      message: 'Privacy policy links have accessibility issues',
      suggestion: 'Improve link text, contrast, and keyboard navigation',
      context: 'WCAG compliance required',
      confidence: 100 - accessibilityScore,
    });
  }

  // Format support validation
  if (formatSupport.html && formatSupport.pdf) {
    confidence += 20;
  } else if (formatSupport.html) {
    confidence += 15;
  }

  // Mobile responsiveness
  if (mobileScore >= 70) {
    confidence += 15;
  }

  return { passed, confidence: Math.min(confidence, 100), findings };
}

// Helper interfaces
interface PrivacyLink {
  url: string;
  text: string;
  position: number;
  accessible: boolean;
  format: 'html' | 'pdf' | 'other';
  context: string;
}

interface FormatSupport {
  html: boolean;
  pdf: boolean;
  downloadable: boolean;
  printable: boolean;
}

// Helper functions (simplified implementations)
function detectLinkFormat(href: string): 'html' | 'pdf' | 'other' {
  if (href.toLowerCase().endsWith('.pdf')) return 'pdf';
  if (href.includes('.html') || href.includes('.htm') || !href.includes('.')) return 'html';
  return 'other';
}

function extractLinkContext(htmlContent: string, position: number): string {
  const start = Math.max(0, position - 100);
  const end = Math.min(htmlContent.length, position + 100);
  return htmlContent
    .substring(start, end)
    .replace(/<[^>]*>/g, '')
    .trim();
}

function extractLinkElement(htmlContent: string, position: number): string {
  const start = htmlContent.lastIndexOf('<a', position);
  const end = htmlContent.indexOf('</a>', position) + 4;
  return htmlContent.substring(start, end);
}

function isHiddenOrTiny(linkElement: string): boolean {
  return (
    linkElement.includes('display:none') ||
    linkElement.includes('visibility:hidden') ||
    linkElement.includes('font-size:0') ||
    linkElement.includes('height:0') ||
    linkElement.includes('width:0')
  );
}

function hasProperContrast(linkElement: string): boolean {
  // Basic contrast check - in real implementation would use color analysis
  return !linkElement.includes('color:#fff') || !linkElement.includes('background:#fff');
}

function isKeyboardAccessible(linkElement: string): boolean {
  return !linkElement.includes('tabindex="-1"');
}

function determineSeverity(findings: HipaaFinding[]): HipaaSeverity {
  const hasCritical = findings.some((f) => f.severity === HipaaSeverity.CRITICAL);
  const hasHigh = findings.some((f) => f.severity === HipaaSeverity.HIGH);

  if (hasCritical) return HipaaSeverity.CRITICAL;
  if (hasHigh) return HipaaSeverity.HIGH;
  return HipaaSeverity.MEDIUM;
}

function generateRemediationGuidance(findings: HipaaFinding[]): HipaaRemediation {
  const steps: string[] = [];
  const resources: HipaaResource[] = [];

  if (findings.some((f) => f.type === HipaaFindingType.MISSING_CONTENT)) {
    steps.push(
      'Add a clearly labeled "Privacy Policy" or "Privacy Notice" link to your main navigation',
    );
    steps.push('Ensure the link is visible on all pages of your website');
    steps.push('Use descriptive link text like "Privacy Policy" rather than "Click Here"');
  }

  if (findings.some((f) => f.type === HipaaFindingType.ACCESSIBILITY_ISSUE)) {
    steps.push('Improve link accessibility by ensuring proper contrast ratios');
    steps.push('Make links keyboard navigable and screen reader friendly');
    steps.push('Use descriptive link text that explains the destination');
  }

  resources.push({
    title: 'HIPAA Privacy Rule Requirements',
    url: 'https://www.hhs.gov/hipaa/for-professionals/privacy/index.html',
    type: 'regulation' as const,
    description: 'Official HHS guidance on HIPAA privacy requirements',
  });

  resources.push({
    title: 'Web Accessibility Guidelines',
    url: 'https://www.w3.org/WAI/WCAG21/quickref/',
    type: 'guide' as const,
    description: 'WCAG 2.1 quick reference for accessibility compliance',
  });

  return {
    priority: 'critical',
    effort: 'minimal',
    steps,
    resources,
    timeline: '1-2 days',
    estimatedCost: '$500 - $1,000',
  };
}

function collectEvidence(links: PrivacyLink[], _htmlContent: string): HipaaEvidence[] {
  const evidence: HipaaEvidence[] = [];

  links.forEach((link, index) => {
    evidence.push({
      type: 'link' as const,
      content: link.text,
      location: `Link ${index + 1}: ${link.url}`,
      timestamp: new Date().toISOString(),
      relevance: 95,
    });
  });

  if (links.length === 0) {
    evidence.push({
      type: 'metadata' as const,
      content: 'No privacy policy links found',
      location: 'Page navigation',
      timestamp: new Date().toISOString(),
      relevance: 100,
    });
  }

  return evidence;
}

function generateSummary(result: HipaaCheckResult): string {
  const { passed, confidence, details } = result;
  const linksFound = details.metrics.linksFound;
  const accessibilityScore = details.metrics.accessibilityScore;

  if (passed) {
    return `Privacy policy presence validated successfully. Found ${linksFound} privacy policy link(s) with ${accessibilityScore}% accessibility score. Confidence: ${confidence}%`;
  } else {
    return `Privacy policy presence check failed. ${linksFound === 0 ? 'No privacy policy links found.' : `Found ${linksFound} link(s) but with accessibility issues.`} Confidence: ${confidence}%`;
  }
}
