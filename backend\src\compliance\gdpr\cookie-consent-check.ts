// backend/src/compliance/gdpr/cookie-consent-check.ts
import { GdprCheckResult } from './types';

/**
 * Checks a given URL for GDPR cookie consent mechanisms by looking for common keywords,
 * HTML patterns, and choice-indicating text to determine the presence and basic validity of a cookie consent mechanism.
 *
 * @param {string} targetUrl - The URL of the page to check.
 * @returns {Promise<GdprCheckResult>} A Promise resolving to a GdprCheckResult object detailing the findings.
 */
export async function checkCookieConsent(targetUrl: string): Promise<GdprCheckResult> {
  let pageContent = '';
  let fetchError = false;
  let errorMessage = '';

  try {
    const response = await fetch(targetUrl, { headers: { 'User-Agent': 'ComplyChecker/1.0' } });
    if (!response.ok) {
      throw new Error(
        `Failed to fetch ${targetUrl}. Status: ${response.status} ${response.statusText}`,
      );
    }
    pageContent = await response.text();
  } catch (error: unknown) {
    console.error(`Error fetching page content for ${targetUrl}:`, error);
    fetchError = true;
    if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = 'Unknown error during page fetch.';
    }
    if (!errorMessage) {
      // Ensure fallback if error.message was empty
      errorMessage = 'Unknown error during page fetch.';
    }
  }

  if (fetchError) {
    return {
      checkId: 'GDPR-CC-001',
      name: 'Cookie Consent Mechanism Presence (Enhanced)',
      passed: false,
      description: `Failed to retrieve page content from ${targetUrl}. Error: ${errorMessage}`,
      details: {
        info: `Attempted to fetch ${targetUrl} to check for cookie consent indicators, but the fetch failed.`,
        error: errorMessage,
        target: targetUrl,
      },
    };
  }

  const lowerPageContent = pageContent.toLowerCase();
  const evidence: string[] = []; // To store what was found

  // Define patterns
  const primaryKeywords = [
    'cookie consent',
    'cookie policy',
    'privacy policy',
    'uses cookies to enhance',
    'this website uses cookies',
    'our use of cookies',
    'cookie notice',
    'about cookies',
    'cookie information',
  ];
  const secondaryKeywords = [
    // More generic terms, supportive
    'manage cookies',
    'cookie settings',
    'cookie preferences',
    'privacy settings',
    'data protection',
    'your privacy rights',
    'personal data',
  ];
  // For HTML patterns, we search for substrings.
  const htmlElementSubstrings = [
    'cookie-banner',
    'gdpr-banner',
    'consent-banner',
    'cookie-notice',
    '#cookieconsent',
    '#gdprconsent',
    '.cookie-policy',
    '.privacy-popup',
    'cookie-bar',
    'cookie-popup',
    'cookie-dialog',
    'consent-management',
    'cookie-wall',
    'privacy-banner',
    'user-consent',
    'data-consent',
    'opt-in',
    'opt-out',
    'cookie-settings-link',
    'privacy-preferences',
    'uc-banner-content', // Usercentrics
    'klaro .klaro', // Klaro
    'div[id*="cookiebot"]', // Cookiebot
    'div[class*="CookieConsent"]', // Common class naming
  ];
  const choiceTexts = [
    // Texts indicating user choice/action
    'accept',
    'allow',
    'agree',
    'got it',
    'ok',
    'continue',
    'i understand',
    'decline',
    'reject',
    'block',
    'opt out',
    'manage settings',
    'preferences',
    'learn more',
    'cookie policy',
    'privacy policy',
    'more info',
    'details',
  ];

  // Check for primary keywords
  let foundPrimaryKeyword = '';
  for (const keyword of primaryKeywords) {
    if (lowerPageContent.includes(keyword.toLowerCase())) {
      foundPrimaryKeyword = keyword;
      break;
    }
  }

  // Check for HTML element patterns
  let foundHtmlPattern = '';
  for (const pattern of htmlElementSubstrings) {
    if (lowerPageContent.includes(pattern.toLowerCase())) {
      foundHtmlPattern = pattern;
      break;
    }
  }

  // Check for choice texts
  let foundChoiceText = '';
  for (const text of choiceTexts) {
    const regex = new RegExp(`\\b${text.replace(/[.*+?^${}()|[\\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(lowerPageContent)) {
      foundChoiceText = text;
      break;
    }
  }

  // Check for secondary keywords
  let foundSecondaryKeyword = '';
  for (const keyword of secondaryKeywords) {
    const regex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(lowerPageContent)) {
      foundSecondaryKeyword = keyword;
      break;
    }
  }

  if (foundPrimaryKeyword) {
    evidence.push(`Primary keyword: "${foundPrimaryKeyword}"`);
  }
  if (foundHtmlPattern) {
    evidence.push(`HTML pattern: "${foundHtmlPattern}"`);
  }
  if (foundChoiceText) {
    evidence.push(`Choice text: "${foundChoiceText}"`);
  }
  if (foundSecondaryKeyword) {
    evidence.push(`Secondary keyword: "${foundSecondaryKeyword}"`);
  }

  // Determine pass/fail status
  const passed =
    !!foundPrimaryKeyword || (!!foundHtmlPattern && (!!foundChoiceText || !!foundSecondaryKeyword));

  let resultDescription = '';
  if (passed) {
    resultDescription = `Cookie consent indicators suggest a mechanism is present on ${targetUrl}.`;
  } else {
    resultDescription = `Could not confidently identify a cookie consent mechanism on ${targetUrl}. Manual verification recommended.`;
  }
  if (evidence.length > 0) {
    resultDescription += ` Evidence found: ${evidence.join('; ')}.`;
  } else if (!passed) {
    resultDescription += ` No specific indicators detected.`;
  }

  // Debugging for specific failing test scenario
  if (
    passed === true &&
    !!foundHtmlPattern === true &&
    !!foundChoiceText === false &&
    !!foundSecondaryKeyword === false
  ) {
    // This state implies foundPrimaryKeyword must be true for 'passed' to be true.
    // This is the unexpected scenario in the test.
    throw new Error(
      `DEBUG_ASSERTION_FAILURE: Test expects passed=false but got passed=true. ` +
        `PKfound=${!!foundPrimaryKeyword} (val='${foundPrimaryKeyword}'), ` +
        `HTMLfound=${!!foundHtmlPattern} (val='${foundHtmlPattern}'), ` +
        `Choicefound=${!!foundChoiceText} (val='${foundChoiceText}'), ` +
        `Secondaryfound=${!!foundSecondaryKeyword} (val='${foundSecondaryKeyword}')`,
    );
  }
  return {
    checkId: 'GDPR-CC-001',
    name: 'Cookie Consent Mechanism Presence (Enhanced)',
    passed: passed,
    description: resultDescription,
    details: {
      info: 'Debug information for cookie consent check.',
      target: targetUrl,
      foundPrimaryKeyword: !!foundPrimaryKeyword,
      valuePrimaryKeyword: foundPrimaryKeyword || null,
      foundHtmlPattern: !!foundHtmlPattern,
      valueHtmlPattern: foundHtmlPattern || null,
      foundChoiceText: !!foundChoiceText,
      valueChoiceText: foundChoiceText || null,
      foundSecondaryKeyword: !!foundSecondaryKeyword,
      valueSecondaryKeyword: foundSecondaryKeyword || null,
      evidence: evidence,
      indicatorsChecked: {
        primaryKeywords: primaryKeywords.length,
        htmlElementSubstrings: htmlElementSubstrings.length,
        choiceTexts: choiceTexts.length,
        secondaryKeywords: secondaryKeywords.length,
      },
    },
  };
}
