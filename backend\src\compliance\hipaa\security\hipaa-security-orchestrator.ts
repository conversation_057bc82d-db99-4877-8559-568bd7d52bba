import { NucleiClient } from './services/nuclei-client';
import { SSLAnalyzer } from './services/ssl-analyzer';
import { ContentAnalyzer } from './services/content-analyzer';
import { ScannerConfigService } from './services/scanner-config';
import { SecurityURLResolver } from './services/security-url-resolver';
import { AccessControlTest } from './tests/access-control-test';
import { AuthenticationTest } from './tests/authentication-test';
import { TransmissionSecurityTest } from './tests/transmission-security-test';
import { EPHIDetectionTest } from './tests/ephi-detection-test';
import { AuditControlsTest } from './tests/audit-controls-test';
import { HipaaSecurityDatabase } from './database/hipaa-security-database';
const fetch = require('node-fetch');
import {
  HipaaSecurityScanConfig,
  HipaaSecurityScanResult,
  HipaaTestDetail,
  HipaaTestFailure,
  CategoryResult,
  RiskLevel,
  VulnerabilityResult,
  SSLVulnerability,
} from './types';
import { HIPAA_SECURITY_CONSTANTS } from './constants';

export class HipaaSecurityOrchestrator {
  private nucleiClient: NucleiClient;
  private sslAnalyzer: SSLAnalyzer;
  private contentAnalyzer: ContentAnalyzer;
  private database: HipaaSecurityDatabase;
  private configService: ScannerConfigService;

  // Test modules
  private accessControlTest: AccessControlTest;
  private authenticationTest: AuthenticationTest;
  private transmissionSecurityTest: TransmissionSecurityTest;
  private ephiDetectionTest: EPHIDetectionTest;
  private auditControlsTest: AuditControlsTest;

  constructor() {
    // Initialize configuration service first
    this.configService = ScannerConfigService.getInstance();

    // Initialize services
    this.nucleiClient = new NucleiClient();
    this.sslAnalyzer = new SSLAnalyzer();
    this.contentAnalyzer = new ContentAnalyzer();
    this.database = new HipaaSecurityDatabase();

    // Initialize test modules with Nuclei client
    this.accessControlTest = new AccessControlTest(this.nucleiClient, this.contentAnalyzer);
    this.authenticationTest = new AuthenticationTest(this.nucleiClient, this.contentAnalyzer);
    this.transmissionSecurityTest = new TransmissionSecurityTest(
      this.nucleiClient,
      this.sslAnalyzer,
      this.contentAnalyzer,
    );
    this.ephiDetectionTest = new EPHIDetectionTest(this.nucleiClient, this.contentAnalyzer);
    this.auditControlsTest = new AuditControlsTest(this.nucleiClient, this.contentAnalyzer);
  }

  async performComprehensiveScan(
    targetUrl: string,
    _config: Partial<HipaaSecurityScanConfig> = {},
  ): Promise<HipaaSecurityScanResult> {
    const scanConfig: HipaaSecurityScanConfig = {
      ...HIPAA_SECURITY_CONSTANTS.DEFAULT_SCAN_CONFIG,
      targetUrl,
      ..._config,
    };

    // Test database connection and table existence
    console.log('🔌 Verifying database setup...');

    let scanId: string;
    try {
      // Add timeout wrapper for the entire database setup
      const dbSetupPromise = (async () => {
        console.log('🔌 Testing database connection...');
        const dbConnected = await this.database.testConnection();
        if (!dbConnected) {
          throw new Error('Database connection failed');
        }

        console.log('🗄️ Checking table existence...');
        const tableExists = await this.database.checkTableExists();
        if (!tableExists) {
          throw new Error('HIPAA security tables not found. Please run database migrations.');
        }

        console.log('📝 Creating scan record...');
        return await this.database.createScan(scanConfig);
      })();

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database setup timeout after 30 seconds')), 30000);
      });

      scanId = await Promise.race([dbSetupPromise, timeoutPromise]);
      console.log('✅ Database setup completed successfully');
    } catch (error) {
      console.error('❌ Database setup failed:', error);
      console.log('🔄 Continuing without database persistence...');

      // Generate a temporary scan ID for this session
      scanId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      console.log(`📋 Using temporary scan ID: ${scanId}`);
    }
    const startTime = Date.now();

    console.log(`🚀 HIPAA Security Scan Started - ID: ${scanId}`);
    console.log(`📋 Target URL: ${targetUrl}`);
    console.log(`⚙️ Configuration:`, {
      maxPages: scanConfig.maxPages,
      scanDepth: scanConfig.scanDepth,
      timeout: scanConfig.timeout,
      enableVulnerabilityScanning: scanConfig.enableVulnerabilityScanning,
      enableSSLAnalysis: scanConfig.enableSSLAnalysis,
      enableContentAnalysis: scanConfig.enableContentAnalysis,
    });

    try {
      // Update scan status if we have a real database connection
      if (!scanId.startsWith('temp-')) {
        await this.database.updateScanStatus(scanId, 'running');
      }

      // Phase 1: Discovery and Page Collection
      console.log('🔍 Phase 1: Discovering pages...');
      const pagesToScan = await this.discoverPages(targetUrl, scanConfig);
      console.log(`📄 Found ${pagesToScan.length} pages to scan:`, pagesToScan);

      // Phase 2: Run all test modules
      console.log('🔬 Phase 2: Running HIPAA security tests...');
      const allTestResults = await this.runAllTests(targetUrl, pagesToScan, scanConfig);
      console.log(`✅ Completed ${allTestResults.length} security tests`);

      // Phase 3: Analyze and categorize results
      console.log('📊 Phase 3: Analyzing results...');
      const categorizedResults = this.categorizeResults(allTestResults);

      // Phase 4: Calculate overall score and risk level
      console.log('🧮 Phase 4: Calculating scores...');
      const overallScore = this.calculateOverallScore(categorizedResults);
      const riskLevel = this.calculateOverallRiskLevel(categorizedResults);

      // Phase 5: Run vulnerability scanning if enabled
      const vulnerabilities: VulnerabilityResult[] = [];
      if (scanConfig.enableVulnerabilityScanning) {
        console.log('🕷️ Phase 5: Running vulnerability scanning...');
        try {
          const vulnResults = await this.runVulnerabilityScanning(pagesToScan, scanConfig);
          vulnerabilities.push(...vulnResults);
          console.log(`🔍 Found ${vulnerabilities.length} vulnerabilities`);
        } catch (error) {
          console.warn(
            '⚠️ Vulnerability scanning failed, continuing without Nuclei results:',
            error instanceof Error ? error.message : 'Unknown error',
          );
        }
      }

      // Phase 6: Build final result
      const scanDuration = Date.now() - startTime;
      const result: HipaaSecurityScanResult = {
        scanId,
        targetUrl,
        scanTimestamp: new Date(),
        scanDuration,
        overallScore,
        riskLevel,
        passedTests: allTestResults.filter((test) => test.passed) as HipaaTestDetail[],
        failedTests: allTestResults.filter((test) => !test.passed) as HipaaTestFailure[],
        technicalSafeguards: categorizedResults.technical,
        administrativeSafeguards: categorizedResults.administrative,
        organizationalSafeguards: categorizedResults.organizational,
        physicalSafeguards: categorizedResults.physical,
        vulnerabilities,
        pagesScanned: pagesToScan,
        toolsUsed: this.getToolsUsed(scanConfig),
        scanStatus: 'completed',
      };

      // Phase 7: Save results to database
      console.log('💾 Phase 7: Saving results to database...');
      if (!scanId.startsWith('temp-')) {
        try {
          await this.database.saveScanResults(result);
          await this.database.updateScanStatus(scanId, 'completed');
          console.log('✅ Results saved to database successfully');
        } catch (dbError) {
          console.warn('⚠️ Failed to save to database, but scan completed successfully:', dbError);
        }
      } else {
        console.log('📋 Temporary scan - results not persisted to database');
      }

      console.log(`🎉 HIPAA Security scan completed successfully!`);
      console.log(`📊 Final Results:`, {
        scanId,
        overallScore,
        riskLevel,
        passedTests: result.passedTests.length,
        failedTests: result.failedTests.length,
        vulnerabilities: vulnerabilities.length,
        scanDuration: `${scanDuration}ms`,
      });

      return result;
    } catch (error) {
      console.error('❌ HIPAA Security scan failed:', error);

      // Update scan status if we have a real database connection
      if (!scanId.startsWith('temp-')) {
        try {
          await this.database.updateScanStatus(
            scanId,
            'failed',
            error instanceof Error ? error.message : 'Unknown error',
          );
        } catch (dbError) {
          console.warn('⚠️ Failed to update scan status in database:', dbError);
        }
      }

      throw error;
    }
  }

  private async discoverPages(
    targetUrl: string,
    config: HipaaSecurityScanConfig,
  ): Promise<string[]> {
    console.log('🔍 Phase 1: Discovering pages...');
    console.log(`🔍 Discovering security-relevant pages for: ${targetUrl}`);

    try {
      // OPTION 1: Try to get ALL internal links from homepage (for ZAP scanning)
      console.log('🔍 Attempting to extract ALL internal links from homepage...');
      const allInternalUrls = await SecurityURLResolver.findAllInternalLinks(
        targetUrl,
        config.maxPages,
      );

      if (allInternalUrls.length > 0) {
        console.log(`📄 Found ${allInternalUrls.length} internal URLs from homepage`);
        console.log(
          `🔗 Sample URLs: ${allInternalUrls
            .slice(0, 5)
            .map((url) => new URL(url).pathname)
            .join(', ')}${allInternalUrls.length > 5 ? '...' : ''}`,
        );
        return allInternalUrls;
      }

      // OPTION 2: Fallback to security-relevant URLs if all internal links extraction fails
      console.log('🔄 Falling back to security-relevant URL discovery...');
      const securityLinks = await SecurityURLResolver.findSecurityRelevantUrls(
        targetUrl,
        config.maxPages,
      );

      if (securityLinks.length > 0) {
        const pages = securityLinks.map((link) => link.url);

        // Log discovered pages by type
        const pagesByType = securityLinks.reduce(
          (acc, link) => {
            acc[link.type] = (acc[link.type] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        );

        console.log(`📊 Discovered ${pages.length} security-relevant pages:`);
        Object.entries(pagesByType).forEach(([type, count]) => {
          console.log(`   📄 ${type}: ${count} pages`);
        });

        // Always include the main page if not already included
        if (!pages.includes(targetUrl)) {
          pages.unshift(targetUrl);
        }

        console.log(
          `📄 Found ${pages.length} pages to scan: [${pages.map((p) => `'${new URL(p).pathname}'`).join(', ')}]`,
        );
        return pages.slice(0, config.maxPages);
      }
    } catch (error) {
      console.warn('⚠️ Smart URL discovery failed, falling back to common paths:', error);
    }

    // Fallback to common paths if smart discovery fails
    console.log('🔄 Using fallback page discovery...');
    const commonPages = [
      '/',
      '/privacy',
      '/terms',
      '/login',
      '/contact',
      '/about',
      '/admin',
      '/dashboard',
      '/api',
      '/account',
      '/health',
      '/security',
      '/policy',
    ];

    const existingPages: string[] = [];

    for (const page of commonPages) {
      if (existingPages.length >= config.maxPages) break;

      try {
        console.log(`📄 Checking page: ${targetUrl}${page}`);

        // Use direct HTTP request instead of ZAP for page discovery
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(`${targetUrl}${page}`, {
          method: 'HEAD',
          signal: controller.signal,
          headers: {
            'User-Agent': 'HIPAA-Security-Scanner/1.0',
          },
        });

        clearTimeout(timeoutId);

        if (response.status < 500) {
          existingPages.push(`${targetUrl}${page}`);
          console.log(`✅ Found page: ${page} (${response.status})`);
        } else {
          console.log(`⚠️ Page returned server error: ${page} (${response.status})`);
        }
      } catch (error) {
        console.log(
          `❌ Page not accessible: ${page} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        continue;
      }
    }

    console.log(`📊 Discovered ${existingPages.length} pages to scan`);
    return existingPages;
  }

  private async runAllTests(
    targetUrl: string,
    pagesToScan: string[],
    _config: HipaaSecurityScanConfig,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const allResults: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('🔬 Running comprehensive HIPAA security tests...');

      // Run HTTPS/SSL Tests
      console.log('🔒 Running HTTPS/SSL tests...');
      const httpsResults = await this.runHTTPSTests(targetUrl);
      allResults.push(...httpsResults);
      console.log(`   ✅ HTTPS/SSL tests completed: ${httpsResults.length} tests`);

      // Run Security Headers Tests
      console.log('🛡️ Running Security Headers tests...');
      const headerResults = await this.runSecurityHeadersTests(targetUrl);
      allResults.push(...headerResults);
      console.log(`   ✅ Security Headers tests completed: ${headerResults.length} tests`);

      // Run Privacy Policy Tests
      console.log('📋 Running Privacy Policy tests...');
      const privacyResults = await this.runPrivacyPolicyTests(targetUrl, pagesToScan);
      allResults.push(...privacyResults);
      console.log(`   ✅ Privacy Policy tests completed: ${privacyResults.length} tests`);

      // Run Access Control Tests
      console.log('🔐 Running Access Control tests...');
      const accessResults = await this.runAccessControlTests(targetUrl);
      allResults.push(...accessResults);
      console.log(`   ✅ Access Control tests completed: ${accessResults.length} tests`);

      // Run Content Security Tests
      console.log('📄 Running Content Security tests...');
      const contentResults = await this.runContentSecurityTests(targetUrl);
      allResults.push(...contentResults);
      console.log(`   ✅ Content Security tests completed: ${contentResults.length} tests`);

      // Run Cookie Security Tests
      console.log('🍪 Running Cookie Security tests...');
      const cookieResults = await this.runCookieSecurityTests(targetUrl, _config);
      allResults.push(...cookieResults);
      console.log(`   ✅ Cookie Security tests completed: ${cookieResults.length} tests`);

      const passedTests = allResults.filter((test) => test.passed).length;
      const failedTests = allResults.filter((test) => !test.passed).length;

      console.log(`✅ All HIPAA security tests completed!`);
      console.log(
        `📊 Test Summary: ${allResults.length} total, ${passedTests} passed, ${failedTests} failed`,
      );

      return allResults;
    } catch (error) {
      console.error('❌ Error running HIPAA security tests:', error);
      throw error;
    }
  }

  private categorizeResults(results: (HipaaTestDetail | HipaaTestFailure)[]): {
    technical: CategoryResult;
    administrative: CategoryResult;
    organizational: CategoryResult;
    physical: CategoryResult;
  } {
    const categories = {
      technical: this.buildCategoryResult('technical', results),
      administrative: this.buildCategoryResult('administrative', results),
      organizational: this.buildCategoryResult('organizational', results),
      physical: this.buildCategoryResult('physical', results),
    };

    return categories;
  }

  private buildCategoryResult(
    category: string,
    results: (HipaaTestDetail | HipaaTestFailure)[],
  ): CategoryResult {
    const categoryTests = results.filter((test) => test.category === category);
    const passedTests = categoryTests.filter((test) => test.passed).length;
    const failedTests = categoryTests.filter((test) => !test.passed).length;
    const totalTests = categoryTests.length;

    // Count issues by risk level
    const failedTestsTyped = categoryTests.filter((test) => !test.passed) as HipaaTestFailure[];
    const criticalIssues = failedTestsTyped.filter((test) => test.riskLevel === 'critical').length;
    const highIssues = failedTestsTyped.filter((test) => test.riskLevel === 'high').length;
    const mediumIssues = failedTestsTyped.filter((test) => test.riskLevel === 'medium').length;
    const lowIssues = failedTestsTyped.filter((test) => test.riskLevel === 'low').length;

    // Calculate risk-weighted score
    console.log(`🏷️ Calculating score for ${category} category with ${categoryTests.length} tests`);
    const score = this.calculateRiskWeightedScore(categoryTests);

    // Determine risk level based on score ranges (now aligned with scoring)
    const riskLevel = this.determineRiskLevelFromScore(
      score,
      criticalIssues,
      highIssues,
      mediumIssues,
    );
    console.log(`🏷️ ${category} category: ${score}% = ${riskLevel.toUpperCase()} risk`);

    return {
      category: category as 'technical' | 'administrative' | 'organizational' | 'physical',
      totalTests,
      passedTests,
      failedTests,
      score,
      riskLevel,
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
    };
  }

  /**
   * Calculate risk-weighted score for a category
   * Critical issues heavily impact the score, while low issues have minimal impact
   */
  private calculateRiskWeightedScore(
    categoryTests: (HipaaTestDetail | HipaaTestFailure)[],
  ): number {
    console.log(`🧮 CALCULATING RISK-WEIGHTED SCORE for ${categoryTests.length} tests`);

    if (categoryTests.length === 0) return 100;

    // Define point values for each risk level
    const riskWeights = {
      critical: 40, // Critical tests are worth 40 points each
      high: 30, // High tests are worth 30 points each
      medium: 20, // Medium tests are worth 20 points each
      low: 10, // Low tests are worth 10 points each
    };

    let totalPossiblePoints = 0;
    let earnedPoints = 0;

    categoryTests.forEach((test) => {
      // Determine test risk level
      let testRiskLevel: 'critical' | 'high' | 'medium' | 'low' = 'low';

      if (!test.passed) {
        const failedTest = test as HipaaTestFailure;
        testRiskLevel = failedTest.riskLevel;
      } else {
        // For passed tests, infer risk level from test ID or default to medium
        if (test.testId.includes('HTTPS') || test.testId.includes('SSL')) {
          testRiskLevel = 'critical';
        } else if (test.testId.includes('HEADERS') || test.testId.includes('AUTH')) {
          testRiskLevel = 'high';
        } else if (test.testId.includes('PRIVACY') || test.testId.includes('CONTENT')) {
          testRiskLevel = 'medium';
        } else {
          testRiskLevel = 'low';
        }
      }

      const pointValue = riskWeights[testRiskLevel];
      totalPossiblePoints += pointValue;

      if (test.passed) {
        earnedPoints += pointValue;
      }
      // Failed tests contribute 0 points
    });

    // Calculate percentage score
    const score =
      totalPossiblePoints > 0 ? Math.round((earnedPoints / totalPossiblePoints) * 100) : 100;
    const finalScore = Math.max(0, Math.min(100, score)); // Ensure score is between 0-100

    console.log(`🧮 RISK-WEIGHTED SCORING RESULT:`);
    console.log(`   📊 Total Possible Points: ${totalPossiblePoints}`);
    console.log(`   ✅ Earned Points: ${earnedPoints}`);
    console.log(`   📈 Raw Score: ${score}%`);
    console.log(`   🎯 Final Score: ${finalScore}%`);

    return finalScore;
  }

  /**
   * Determine risk level based on score ranges, aligned with the weighted scoring
   */
  private determineRiskLevelFromScore(
    score: number,
    criticalIssues: number,
    _highIssues: number,
    _mediumIssues: number,
  ): RiskLevel {
    // If there are critical issues, cap the risk level appropriately
    if (criticalIssues > 0) {
      return 'critical';
    }

    // Use score-based risk levels that align with our weighted scoring
    if (score <= 30) return 'critical'; // 0-30%: Critical Risk
    if (score <= 60) return 'high'; // 31-60%: High Risk
    if (score <= 80) return 'medium'; // 61-80%: Medium Risk
    return 'low'; // 81-100%: Low Risk
  }

  private calculateOverallScore(categories: {
    technical: CategoryResult;
    administrative: CategoryResult;
    organizational: CategoryResult;
    physical: CategoryResult;
  }): number {
    // Weight technical safeguards more heavily as they're more automatable
    const weights = {
      technical: 0.5,
      administrative: 0.3,
      organizational: 0.15,
      physical: 0.05, // Very low weight since we can't test these externally
    };

    const weightedScore =
      categories.technical.score * weights.technical +
      categories.administrative.score * weights.administrative +
      categories.organizational.score * weights.organizational +
      categories.physical.score * weights.physical;

    return Math.round(weightedScore);
  }

  private calculateOverallRiskLevel(categories: {
    technical: CategoryResult;
    administrative: CategoryResult;
    organizational: CategoryResult;
    physical: CategoryResult;
  }): RiskLevel {
    // Calculate overall score first
    const overallScore = this.calculateOverallScore(categories);

    // Count total critical and high issues across all categories
    const totalCriticalIssues = Object.values(categories).reduce(
      (sum, cat) => sum + cat.criticalIssues,
      0,
    );
    const totalHighIssues = Object.values(categories).reduce((sum, cat) => sum + cat.highIssues, 0);
    const totalMediumIssues = Object.values(categories).reduce(
      (sum, cat) => sum + cat.mediumIssues,
      0,
    );

    // Use score-based risk level determination, but still prioritize critical issues
    return this.determineRiskLevelFromScore(
      overallScore,
      totalCriticalIssues,
      totalHighIssues,
      totalMediumIssues,
    );
  }

  async getScanResult(scanId: string): Promise<HipaaSecurityScanResult | null> {
    return await this.database.getScanResult(scanId);
  }

  async getAllScans(limit: number = 50): Promise<HipaaSecurityScanResult[]> {
    return await this.database.getAllScans(limit);
  }

  async deleteScan(scanId: string): Promise<boolean> {
    return await this.database.deleteScan(scanId);
  }

  /**
   * Recalculate existing scan result with new risk-weighted scoring
   */
  async recalculateScanScoring(scanId: string): Promise<HipaaSecurityScanResult | null> {
    try {
      console.log(`🔄 Recalculating scan ${scanId} with new risk-weighted scoring...`);

      // Get existing scan result
      const existingScan = await this.database.getScanResult(scanId);
      if (!existingScan) {
        console.warn(`⚠️ Scan ${scanId} not found for recalculation`);
        return null;
      }

      // Recalculate scores using new risk-weighted system
      const allTests = [...existingScan.passedTests, ...existingScan.failedTests];
      const newOverallScore = this.calculateRiskWeightedScore(allTests);

      // Recalculate risk level
      const criticalIssues =
        existingScan.failedTests.filter((t) => t.riskLevel === 'critical').length +
        existingScan.vulnerabilities.filter((v) => v.severity === 'critical').length;
      const highIssues =
        existingScan.failedTests.filter((t) => t.riskLevel === 'high').length +
        existingScan.vulnerabilities.filter((v) => v.severity === 'high').length;
      const mediumIssues =
        existingScan.failedTests.filter((t) => t.riskLevel === 'medium').length +
        existingScan.vulnerabilities.filter((v) => v.severity === 'medium').length;

      const newRiskLevel = this.determineRiskLevelFromScore(
        newOverallScore,
        criticalIssues,
        highIssues,
        mediumIssues,
      );

      // Update the scan result
      const updatedScan: HipaaSecurityScanResult = {
        ...existingScan,
        overallScore: newOverallScore,
        riskLevel: newRiskLevel,
      };

      // Recalculate category scores
      this.recalculateCategoryScores(updatedScan);

      // Save updated results
      await this.database.saveScanResults(updatedScan);

      console.log(
        `✅ Scan ${scanId} recalculated: ${existingScan.overallScore}% → ${newOverallScore}% (${existingScan.riskLevel} → ${newRiskLevel})`,
      );

      return updatedScan;
    } catch (error) {
      console.error(`❌ Failed to recalculate scan ${scanId}:`, error);
      return null;
    }
  }

  /**
   * Recalculate category scores with new risk-weighted system
   */
  private recalculateCategoryScores(scanResult: HipaaSecurityScanResult): void {
    const categories = ['technical', 'administrative', 'organizational', 'physical'] as const;

    categories.forEach((category) => {
      const categoryTests = [
        ...scanResult.passedTests.filter((t) => t.category === category),
        ...scanResult.failedTests.filter((t) => t.category === category),
      ];

      if (categoryTests.length > 0) {
        const categoryScore = this.calculateRiskWeightedScore(categoryTests);
        const categoryFailed = scanResult.failedTests.filter((t) => t.category === category);

        const criticalIssues = categoryFailed.filter((t) => t.riskLevel === 'critical').length;
        const highIssues = categoryFailed.filter((t) => t.riskLevel === 'high').length;
        const mediumIssues = categoryFailed.filter((t) => t.riskLevel === 'medium').length;

        const categoryRiskLevel = this.determineRiskLevelFromScore(
          categoryScore,
          criticalIssues,
          highIssues,
          mediumIssues,
        );

        // Update the appropriate category
        const categoryResult = {
          category,
          totalTests: categoryTests.length,
          passedTests: scanResult.passedTests.filter((t) => t.category === category).length,
          failedTests: categoryFailed.length,
          score: categoryScore,
          riskLevel: categoryRiskLevel,
          criticalIssues,
          highIssues,
          mediumIssues,
          lowIssues: categoryFailed.filter((t) => t.riskLevel === 'low').length,
        };

        switch (category) {
          case 'technical':
            scanResult.technicalSafeguards = categoryResult;
            break;
          case 'administrative':
            scanResult.administrativeSafeguards = categoryResult;
            break;
          case 'organizational':
            scanResult.organizationalSafeguards = categoryResult;
            break;
          case 'physical':
            scanResult.physicalSafeguards = categoryResult;
            break;
        }
      }
    });
  }

  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up HIPAA Security Orchestrator...');
      await this.database.close();
      console.log('✅ Cleanup completed successfully');
    } catch (error) {
      console.warn('⚠️ Error during cleanup:', error);
    }
  }

  private getToolsUsed(config: HipaaSecurityScanConfig): string[] {
    const tools = ['HTTP Client', 'SSL Analyzer', 'Content Analyzer'];

    if (config.enableVulnerabilityScanning) {
      tools.push('Nuclei Scanner (if available)');
    }

    return tools;
  }

  private async runVulnerabilityScanning(
    urlsToScan: string[],
    config: HipaaSecurityScanConfig,
  ): Promise<VulnerabilityResult[]> {
    const vulnerabilities: VulnerabilityResult[] = [];

    try {
      console.log(`🔍 Starting HIPAA-focused Nuclei vulnerability scanning...`);

      // Check if Nuclei is available with detailed logging
      console.log('🔍 Checking Nuclei availability...');
      const nucleiAvailable = await this.nucleiClient.isAvailable();
      console.log(`📋 Nuclei availability result: ${nucleiAvailable}`);

      if (!nucleiAvailable) {
        console.warn('⚠️ Nuclei not available, performing basic vulnerability checks...');
        console.log('🔍 Nuclei unavailability details:', {
          reason: 'Nuclei installation check failed',
          fallbackAction: 'Using basic vulnerability checks',
          targetUrl: config.targetUrl,
          timestamp: new Date().toISOString(),
        });
        return await this.runBasicVulnerabilityChecks(urlsToScan[0]); // Fallback to first URL only
      }

      // Update templates for latest vulnerability checks
      console.log('📥 Updating Nuclei templates...');
      try {
        const updateResult = await this.nucleiClient.updateTemplates();
        console.log(`📥 Template update result: ${updateResult ? 'success' : 'failed'}`);
      } catch (updateError) {
        console.warn(
          '⚠️ Template update failed, continuing with existing templates:',
          updateError instanceof Error ? updateError.message : 'Unknown error',
        );
      }

      // Step 1: Get HIPAA-critical pages using SecurityURLResolver
      console.log('🎯 Discovering HIPAA-critical pages (auth, privacy, admin, etc.)...');
      const securityLinks = await SecurityURLResolver.findSecurityRelevantUrls(
        config.targetUrl,
        Math.max(config.maxPages, 30), // Ensure we get comprehensive HIPAA-critical pages
      );

      const hipaaUrls = securityLinks.map((link) => link.url);
      console.log(`🔐 Found ${hipaaUrls.length} HIPAA-critical URLs:`);

      // Log breakdown by type for visibility
      const urlsByType = securityLinks.reduce(
        (acc, link) => {
          acc[link.type] = (acc[link.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      console.log(
        `📊 HIPAA URL breakdown: ${Object.entries(urlsByType)
          .map(([type, count]) => `${type}: ${count}`)
          .join(', ')}`,
      );

      // Step 2: Let Nuclei discover additional URLs for comprehensive coverage
      console.log('🕷️ Nuclei discovering additional URLs...');
      const nucleiDiscoveredUrls = await this.nucleiClient.discoverUrls(
        config.targetUrl,
        Math.max(config.maxPages, 25),
      );

      // Step 3: Combine and deduplicate URLs, prioritizing HIPAA-critical pages
      const allUrls = new Set([...hipaaUrls, ...nucleiDiscoveredUrls]);
      const finalUrls = Array.from(allUrls);

      console.log(
        `🔗 Total URLs for scanning: ${finalUrls.length} (${hipaaUrls.length} HIPAA-critical + ${nucleiDiscoveredUrls.length - nucleiDiscoveredUrls.filter((url) => hipaaUrls.includes(url)).length} additional)`,
      );

      // Step 4: Run Nuclei HIPAA-focused vulnerability scan on all URLs
      console.log('🚀 Starting comprehensive Nuclei HIPAA security scan...');
      const nucleiVulns = await this.nucleiClient.scanMultipleUrls(finalUrls, {
        timeout: config.timeout || 30000,
        tags: [
          'ssl',
          'tls',
          'headers',
          'privacy',
          'disclosure',
          'auth',
          'session',
          'encryption',
          'compliance',
        ],
        severity: ['critical', 'high', 'medium', 'low'],
      });

      console.log(`🔍 Nuclei found ${nucleiVulns.length} vulnerabilities`);

      // Convert Nuclei vulnerabilities to standard format
      nucleiVulns.forEach((vuln) => {
        vulnerabilities.push({
          id: `${vuln.id}-${vuln.type}`,
          type: vuln.type,
          severity: vuln.severity,
          location: vuln.location,
          description: vuln.description,
          evidence: vuln.evidence || {},
          remediationGuidance: vuln.remediationGuidance,
          // Optional properties
          cweId: vuln.tags?.includes('cwe')
            ? parseInt(vuln.tags.find((t) => t.startsWith('cwe-'))?.substring(4) || '0')
            : undefined,
          owaspCategory: vuln.tags?.find((t) => t.startsWith('owasp-')) || undefined,
        });
      });

      console.log(`🔍 Found ${vulnerabilities.length} total vulnerabilities across all URLs`);
      return vulnerabilities;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Nuclei vulnerability scanning failed:', {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        targetUrl: config.targetUrl,
        timestamp: new Date().toISOString(),
      });

      // Log detailed error information for debugging
      console.log('🔍 Error details:', {
        nucleiPath: this.nucleiClient ? 'initialized' : 'not initialized',
        configDetails: {
          maxPages: config.maxPages,
          timeout: config.timeout,
          enableVulnerabilityScanning: config.enableVulnerabilityScanning,
        },
        urlsToScanCount: urlsToScan.length,
      });

      console.log('🔄 Falling back to basic vulnerability checks...');
      return await this.runBasicVulnerabilityChecks(urlsToScan[0]); // Fallback to first URL only
    }
  }

  private async runBasicVulnerabilityChecks(targetUrl: string): Promise<VulnerabilityResult[]> {
    const vulnerabilities: VulnerabilityResult[] = [];

    try {
      console.log('🔍 Running basic vulnerability checks...');

      // Check for common security issues
      const response = await fetch(targetUrl, {
        method: 'GET',
        headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
      });

      const headers = response.headers;
      const content = await response.text();

      // Check for missing security headers
      const securityHeaders = [
        'strict-transport-security',
        'x-frame-options',
        'x-content-type-options',
        'content-security-policy',
      ];

      securityHeaders.forEach((header) => {
        if (!headers.get(header)) {
          vulnerabilities.push({
            id: `missing-${header}`,
            type: 'Missing Security Header',
            severity: 'medium',
            location: targetUrl,
            description: `Missing ${header} security header`,
            evidence: { missingHeader: header },
            remediationGuidance: `Implement ${header} header for enhanced security`,
          });
        }
      });

      // Check for potential information disclosure
      if (content.includes('Server Error') || content.includes('Stack Trace')) {
        vulnerabilities.push({
          id: 'information-disclosure',
          type: 'Information Disclosure',
          severity: 'medium',
          location: targetUrl,
          description: 'Potential information disclosure in error messages',
          evidence: { contentCheck: 'Error information detected' },
          remediationGuidance: 'Implement proper error handling to avoid information disclosure',
        });
      }

      console.log(`🔍 Basic vulnerability check completed: ${vulnerabilities.length} issues found`);
      return vulnerabilities;
    } catch (error) {
      console.error('❌ Basic vulnerability checks failed:', error);
      return [];
    }
  }

  // Real HTTP-based test implementations
  private async runHTTPSTests(targetUrl: string): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      const url = new URL(targetUrl);

      // Test 1: HTTPS Protocol Check
      const isHttps = url.protocol === 'https:';
      const httpsTest: HipaaTestDetail | HipaaTestFailure = isHttps
        ? {
            testId: 'HTTPS-001',
            testName: 'HTTPS Protocol Enforcement',
            hipaaSection: '164.312(e)(1)',
            description: 'Verify that the website uses HTTPS protocol',
            category: 'technical',
            passed: true,
            evidence: 'HTTPS protocol detected',
            pagesTested: [targetUrl],
            timestamp: new Date(),
          }
        : {
            testId: 'HTTPS-001',
            testName: 'HTTPS Protocol Enforcement',
            hipaaSection: '164.312(e)(1)',
            description: 'Verify that the website uses HTTPS protocol',
            category: 'technical',
            passed: false,
            failureReason: 'Website uses HTTP instead of HTTPS',
            riskLevel: 'critical',
            failureEvidence: [
              {
                location: targetUrl,
                elementType: 'response',
                actualCode: `Protocol: ${url.protocol}`,
                expectedBehavior: 'Should use HTTPS protocol',
                context: 'URL protocol analysis',
              },
            ],
            recommendedAction: 'Implement HTTPS with valid SSL certificate',
            remediationPriority: 1,
            timestamp: new Date(),
          };

      results.push(httpsTest);

      // Test 2: SSL Certificate Validation
      if (url.protocol === 'https:') {
        try {
          const sslAnalysis = await this.sslAnalyzer.analyzeDomain(url.hostname, 443);

          const sslValid = sslAnalysis.isValid && sslAnalysis.hipaaCompliant;
          const sslTest: HipaaTestDetail | HipaaTestFailure = sslValid
            ? {
                testId: 'SSL-001',
                testName: 'SSL Certificate Validation',
                hipaaSection: '164.312(e)(1)',
                description: 'Verify SSL certificate is valid and secure',
                category: 'technical',
                passed: true,
                evidence: `SSL certificate is valid. Grade: ${sslAnalysis.grade}, TLS: ${sslAnalysis.tlsVersion}`,
                pagesTested: [url.hostname],
                timestamp: new Date(),
              }
            : {
                testId: 'SSL-001',
                testName: 'SSL Certificate Validation',
                hipaaSection: '164.312(e)(1)',
                description: 'Verify SSL certificate is valid and secure',
                category: 'technical',
                passed: false,
                failureReason: 'SSL certificate issues detected',
                riskLevel: sslAnalysis.vulnerabilities.some(
                  (v: SSLVulnerability) => v.severity === 'critical',
                )
                  ? 'critical'
                  : 'high',
                failureEvidence: sslAnalysis.vulnerabilities.map((vuln: SSLVulnerability) => ({
                  location: url.hostname,
                  elementType: 'response',
                  actualCode: vuln.description,
                  expectedBehavior: vuln.remediation,
                  context: `SSL Vulnerability: ${vuln.type}`,
                })),
                recommendedAction: 'Fix SSL certificate configuration and vulnerabilities',
                remediationPriority: 1,
                timestamp: new Date(),
              };

          results.push(sslTest);
        } catch (error) {
          console.log('SSL analysis failed, skipping SSL test');
        }
      }
    } catch (error) {
      console.error('HTTPS tests failed:', error);
    }

    return results;
  }

  private async runSecurityHeadersTests(
    targetUrl: string,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('🔍 Analyzing security headers...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(targetUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
      });

      clearTimeout(timeoutId);

      const headers = response.headers;
      const requiredHeaders = [
        'strict-transport-security',
        'x-frame-options',
        'x-content-type-options',
        'content-security-policy',
        'x-xss-protection',
      ];

      const missingHeaders: string[] = [];
      const presentHeaders: { name: string; value: string }[] = [];

      requiredHeaders.forEach((header) => {
        const value = headers.get(header);
        if (value) {
          presentHeaders.push({ name: header, value });
        } else {
          missingHeaders.push(header);
        }
      });

      console.log(
        `   📊 Security headers analysis: ${presentHeaders.length}/${requiredHeaders.length} present`,
      );
      if (presentHeaders.length > 0) {
        console.log(
          `   ✅ Present headers:`,
          presentHeaders.map((h) => h.name),
        );
      }
      if (missingHeaders.length > 0) {
        console.log(`   ❌ Missing headers:`, missingHeaders);
      }

      const headersValid = missingHeaders.length === 0;
      const headerTest: HipaaTestDetail | HipaaTestFailure = headersValid
        ? {
            testId: 'HEADERS-001',
            testName: 'Security Headers Analysis',
            hipaaSection: '164.312(e)(2)',
            description: 'Verify essential security headers are present',
            category: 'technical',
            passed: true,
            evidence: `All ${requiredHeaders.length} security headers present: ${presentHeaders.map((h) => `${h.name}=${h.value}`).join(', ')}`,
            pagesTested: [targetUrl],
            timestamp: new Date(),
          }
        : {
            testId: 'HEADERS-001',
            testName: 'Security Headers Analysis',
            hipaaSection: '164.312(e)(2)',
            description: 'Verify essential security headers are present',
            category: 'technical',
            passed: false,
            failureReason: `Missing ${missingHeaders.length} of ${requiredHeaders.length} required security headers`,
            riskLevel: missingHeaders.includes('strict-transport-security') ? 'high' : 'medium',
            failureEvidence: missingHeaders.map((header) => ({
              location: targetUrl,
              elementType: 'header',
              actualCode: `Missing header: ${header}`,
              expectedBehavior: `${header} header should be present`,
              context: 'HTTP response headers',
            })),
            recommendedAction: `Implement missing security headers: ${missingHeaders.join(', ')}`,
            remediationPriority: 2,
            timestamp: new Date(),
          };

      results.push(headerTest);
    } catch (error) {
      console.error(
        '❌ Security headers test failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );

      // Add a failed test result for the error
      const errorTest: HipaaTestFailure = {
        testId: 'HEADERS-ERROR',
        testName: 'Security Headers Analysis',
        hipaaSection: '164.312(e)(2)',
        description: 'Verify essential security headers are present',
        category: 'technical',
        passed: false,
        failureReason: 'Unable to analyze security headers due to connection error',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Successful HTTP response for header analysis',
            context: 'Security headers test',
          },
        ],
        recommendedAction: 'Verify target URL is accessible and try again',
        remediationPriority: 3,
        timestamp: new Date(),
      };
      results.push(errorTest);
    }

    return results;
  }

  private async runPrivacyPolicyTests(
    targetUrl: string,
    _pagesToScan: string[],
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('📋 Checking for privacy policy...');

      const privacyPages = ['/privacy', '/privacy-policy', '/privacy.html', '/legal/privacy'];
      let privacyPageFound = false;
      let foundPage = '';

      for (const page of privacyPages) {
        try {
          const response = await fetch(`${targetUrl}${page}`, {
            method: 'HEAD',
            headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
          });

          if (response.status === 200) {
            privacyPageFound = true;
            foundPage = page;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      const privacyTest: HipaaTestDetail | HipaaTestFailure = privacyPageFound
        ? {
            testId: 'PRIVACY-001',
            testName: 'Privacy Policy Presence',
            hipaaSection: '164.520',
            description: 'Verify that a privacy policy is accessible',
            category: 'administrative',
            passed: true,
            evidence: `Privacy policy found at: ${foundPage}`,
            pagesTested: [foundPage],
            timestamp: new Date(),
          }
        : {
            testId: 'PRIVACY-001',
            testName: 'Privacy Policy Presence',
            hipaaSection: '164.520',
            description: 'Verify that a privacy policy is accessible',
            category: 'administrative',
            passed: false,
            failureReason: 'No privacy policy page found',
            riskLevel: 'high',
            failureEvidence: [
              {
                location: targetUrl,
                elementType: 'html',
                actualCode: 'No privacy policy page detected',
                expectedBehavior:
                  'Should have accessible privacy policy at /privacy or similar URL',
                context: 'Privacy policy accessibility check',
              },
            ],
            recommendedAction: 'Create and publish a comprehensive privacy policy',
            remediationPriority: 1,
            timestamp: new Date(),
          };

      results.push(privacyTest);
    } catch (error) {
      console.error('Privacy policy test failed:', error);
    }

    return results;
  }

  private async runAccessControlTests(
    targetUrl: string,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('🔐 Testing access controls...');

      const protectedEndpoints = ['/admin', '/dashboard', '/account', '/api/users', '/management'];
      const unprotectedEndpoints: string[] = [];

      for (const endpoint of protectedEndpoints) {
        try {
          const response = await fetch(`${targetUrl}${endpoint}`, {
            method: 'GET',
            headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
          });

          // If we get 200 without authentication, it's unprotected
          if (response.status === 200) {
            unprotectedEndpoints.push(endpoint);
          }
        } catch (error) {
          // Endpoint doesn't exist or is inaccessible
          continue;
        }
      }

      const accessControlValid = unprotectedEndpoints.length === 0;
      const accessTest: HipaaTestDetail | HipaaTestFailure = accessControlValid
        ? {
            testId: 'ACCESS-001',
            testName: 'Access Control Verification',
            hipaaSection: '164.312(a)(1)',
            description: 'Verify that administrative endpoints require authentication',
            category: 'technical',
            passed: true,
            evidence: 'All tested administrative endpoints are properly protected',
            pagesTested: protectedEndpoints,
            timestamp: new Date(),
          }
        : {
            testId: 'ACCESS-001',
            testName: 'Access Control Verification',
            hipaaSection: '164.312(a)(1)',
            description: 'Verify that administrative endpoints require authentication',
            category: 'technical',
            passed: false,
            failureReason: `${unprotectedEndpoints.length} unprotected administrative endpoints found`,
            riskLevel: 'critical',
            failureEvidence: unprotectedEndpoints.map((endpoint) => ({
              location: `${targetUrl}${endpoint}`,
              elementType: 'response',
              actualCode: 'HTTP 200 - Accessible without authentication',
              expectedBehavior: 'Should require authentication (401/403 response)',
              context: 'Access control verification',
            })),
            recommendedAction: 'Implement authentication for administrative endpoints',
            remediationPriority: 1,
            timestamp: new Date(),
          };

      results.push(accessTest);
    } catch (error) {
      console.error('Access control test failed:', error);
    }

    return results;
  }

  private async runContentSecurityTests(
    targetUrl: string,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('📄 Analyzing content security...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      const response = await fetch(targetUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
      });

      clearTimeout(timeoutId);
      const content = await response.text();

      console.log(`   📊 Content analysis: ${content.length} characters retrieved`);

      // Check for potential ePHI exposure
      const ephiPatterns = [
        { name: 'SSN', pattern: /\b\d{3}-\d{2}-\d{4}\b/g, risk: 'critical' },
        {
          name: 'Email',
          pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
          risk: 'medium',
        },
        { name: 'Phone', pattern: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, risk: 'medium' },
        { name: 'Patient ID', pattern: /\bpatient\s+id\b/gi, risk: 'high' },
        { name: 'Medical Record', pattern: /\bmedical\s+record\b/gi, risk: 'high' },
        { name: 'DOB', pattern: /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, risk: 'high' },
        {
          name: 'Credit Card',
          pattern: /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,
          risk: 'critical',
        },
      ];

      let ephiFound = false;
      const foundPatterns: { name: string; count: number; risk: string; samples: string[] }[] = [];

      ephiPatterns.forEach(({ name, pattern, risk }) => {
        const matches = content.match(pattern);
        if (matches && matches.length > 0) {
          ephiFound = true;
          foundPatterns.push({
            name,
            count: matches.length,
            risk,
            samples: matches.slice(0, 3), // First 3 matches as samples
          });
        }
      });

      if (foundPatterns.length > 0) {
        console.log(
          `   ⚠️ Potential ePHI patterns found:`,
          foundPatterns.map((p) => `${p.name}: ${p.count}`),
        );
      } else {
        console.log(`   ✅ No obvious ePHI patterns detected`);
      }

      const contentSecure = !ephiFound;
      const contentTest: HipaaTestDetail | HipaaTestFailure = contentSecure
        ? {
            testId: 'CONTENT-001',
            testName: 'ePHI Exposure Check',
            hipaaSection: '164.312(a)(2)(i)',
            description: 'Check for potential ePHI exposure in page content',
            category: 'technical',
            passed: true,
            evidence: `No obvious ePHI patterns detected in ${content.length} characters of content`,
            pagesTested: [targetUrl],
            timestamp: new Date(),
          }
        : {
            testId: 'CONTENT-001',
            testName: 'ePHI Exposure Check',
            hipaaSection: '164.312(a)(2)(i)',
            description: 'Check for potential ePHI exposure in page content',
            category: 'technical',
            passed: false,
            failureReason: `Potential ePHI exposure detected: ${foundPatterns.length} pattern types found`,
            riskLevel: (foundPatterns.some((p) => p.risk === 'critical')
              ? 'critical'
              : foundPatterns.some((p) => p.risk === 'high')
                ? 'high'
                : 'medium') as RiskLevel,
            failureEvidence: foundPatterns.map((pattern) => ({
              location: targetUrl,
              elementType: 'html',
              actualCode: `${pattern.name} pattern found ${pattern.count} times (samples: ${pattern.samples.join(', ')})`,
              expectedBehavior: 'ePHI should not be exposed in public content',
              context: `Content analysis for ${pattern.name} exposure (risk: ${pattern.risk})`,
            })),
            recommendedAction:
              'Review and remove any exposed ePHI from public pages. Implement data masking for sensitive information.',
            remediationPriority: foundPatterns.some((p) => p.risk === 'critical') ? 1 : 2,
            timestamp: new Date(),
          };

      results.push(contentTest);
    } catch (error) {
      console.error(
        '❌ Content security test failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );

      // Add a failed test result for the error
      const errorTest: HipaaTestFailure = {
        testId: 'CONTENT-ERROR',
        testName: 'ePHI Exposure Check',
        hipaaSection: '164.312(a)(2)(i)',
        description: 'Check for potential ePHI exposure in page content',
        category: 'technical',
        passed: false,
        failureReason: 'Unable to analyze content due to connection error',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Successful HTTP response for content analysis',
            context: 'Content security test',
          },
        ],
        recommendedAction: 'Verify target URL is accessible and try again',
        remediationPriority: 3,
        timestamp: new Date(),
      };
      results.push(errorTest);
    }

    return results;
  }

  private async runCookieSecurityTests(
    targetUrl: string,
    _config: HipaaSecurityScanConfig,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    try {
      console.log('🍪 Analyzing cookie security...');

      const response = await fetch(targetUrl, {
        method: 'GET',
        headers: { 'User-Agent': 'HIPAA-Security-Scanner/1.0' },
      });

      const setCookieHeaders = response.headers.get('set-cookie');

      if (!setCookieHeaders) {
        // No cookies set - this is actually good for security
        const cookieTest: HipaaTestDetail = {
          testId: 'COOKIE-001',
          testName: 'Cookie Security Analysis',
          hipaaSection: '164.312(a)(2)(i)',
          description: 'Verify that cookies are configured securely',
          category: 'technical',
          passed: true,
          evidence: 'No cookies detected - reduces security risk',
          pagesTested: [targetUrl],
          timestamp: new Date(),
        };
        results.push(cookieTest);
        return results;
      }

      // Analyze cookie security attributes
      const cookies = setCookieHeaders.split(',').map((cookie: string) => cookie.trim());
      const insecureCookies: string[] = [];
      const secureCookies: string[] = [];

      cookies.forEach((cookie: string) => {
        const hasSecure = cookie.toLowerCase().includes('secure');
        const hasHttpOnly = cookie.toLowerCase().includes('httponly');
        const hasSameSite = cookie.toLowerCase().includes('samesite');

        if (!hasSecure || !hasHttpOnly || !hasSameSite) {
          insecureCookies.push(cookie);
        } else {
          secureCookies.push(cookie);
        }
      });

      const cookiesSecure = insecureCookies.length === 0;
      const cookieTest: HipaaTestDetail | HipaaTestFailure = cookiesSecure
        ? {
            testId: 'COOKIE-001',
            testName: 'Cookie Security Analysis',
            hipaaSection: '164.312(a)(2)(i)',
            description: 'Verify that cookies are configured securely',
            category: 'technical',
            passed: true,
            evidence: `All ${cookies.length} cookies are properly secured with Secure, HttpOnly, and SameSite attributes`,
            pagesTested: [targetUrl],
            timestamp: new Date(),
          }
        : {
            testId: 'COOKIE-001',
            testName: 'Cookie Security Analysis',
            hipaaSection: '164.312(a)(2)(i)',
            description: 'Verify that cookies are configured securely',
            category: 'technical',
            passed: false,
            failureReason: `${insecureCookies.length} insecure cookies detected`,
            riskLevel: 'medium',
            failureEvidence: insecureCookies.map((cookie) => ({
              location: targetUrl,
              elementType: 'header',
              actualCode: `Insecure cookie: ${cookie.substring(0, 100)}...`,
              expectedBehavior: 'Cookies should have Secure, HttpOnly, and SameSite attributes',
              context: 'Cookie security analysis',
            })),
            recommendedAction:
              'Configure all cookies with Secure, HttpOnly, and SameSite attributes',
            remediationPriority: 2,
            timestamp: new Date(),
          };

      results.push(cookieTest);
    } catch (error) {
      console.error('Cookie security test failed:', error);
    }

    return results;
  }
}
