import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries from the 'users' table
  await knex('users').del();

  // Inserts seed entries
  await knex('users').insert([
    {
      // id will be generated by default by pgcrypto gen_random_uuid()
      keycloak_id: '00000000-0000-0000-0000-000000000001', // Example UUID
      email: '<EMAIL>',
      // created_at and updated_at will use defaultTo(knex.fn.now())
    },
    {
      keycloak_id: '00000000-0000-0000-0000-000000000002', // Example UUID
      email: '<EMAIL>',
    },
  ]);

  console.log('Seeded development users successfully.');
}
