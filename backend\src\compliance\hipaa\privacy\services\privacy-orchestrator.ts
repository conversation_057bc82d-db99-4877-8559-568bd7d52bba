// backend/src/compliance/hipaa/privacy/services/privacy-orchestrator.ts

/**
 * HIPAA Privacy Policy Orchestrator
 * Coordinates comprehensive HIPAA compliance scanning with 3-level analysis
 */

import {
  HipaaScanResult,
  HipaaCheckResult,
  HipaaScanOptions,
  HipaaScanSummary,
  HipaaRecommendation,
  HipaaSeverity,
  HipaaCheckCategory,
  HipaaFindingType,
  CheckOptions,
} from '../types';

// Import individual check functions
import { checkPrivacyPolicyPresence } from '../checks/privacy-policy-presence';
import { checkHipaaSpecificContent } from '../checks/hipaa-specific-content';
import { checkContactInformation } from '../checks/contact-information';
import { calculateImprovedOverallScore, ImprovedScoringOptions } from '../utils/improved-scoring';

/**
 * Main orchestrator class for comprehensive HIPAA compliance scanning
 */
export class HipaaPrivacyPolicyOrchestrator {
  private readonly maxConcurrentChecks = 3;
  private readonly defaultTimeout = 30000; // 30 seconds

  /**
   * Perform comprehensive HIPAA compliance scan
   * @param targetUrl - The URL to scan for HIPAA compliance
   * @param options - Configuration options for the scan
   * @returns Promise<HipaaScanResult> - Complete scan results
   */
  async performComprehensiveScan(
    targetUrl: string,
    options: HipaaScanOptions = {},
  ): Promise<HipaaScanResult> {
    const startTime = Date.now();
    const scanId = this.generateScanId();

    console.log(`HipaaOrchestrator: Starting comprehensive scan for ${targetUrl} (ID: ${scanId})`);

    // Initialize result structure
    const result: HipaaScanResult = {
      targetUrl,
      timestamp: new Date().toISOString(),
      overallScore: 0,
      overallPassed: false,
      summary: {
        totalChecks: 0,
        passedChecks: 0,
        failedChecks: 0,
        criticalIssues: 0,
        highIssues: 0,
        mediumIssues: 0,
        lowIssues: 0,
        overallScore: 0,
        complianceLevel: 'non_compliant',
        riskLevel: 'critical',
        analysisLevelsUsed: [],
      },
      checks: [],
      recommendations: [],
      metadata: {
        version: '2.0',
        processingTime: 0,
        checksPerformed: 0,
        analysisLevelsUsed: [],
        cacheHits: 0,
        errors: [],
        warnings: [],
        userAgent: options.userAgent || 'ComplyChecker-HIPAA/2.0',
        scanOptions: options,
      },
    };

    try {
      // Step 1: Define and execute checks
      const checkDefinitions = this.defineChecks(options);
      result.summary.totalChecks = checkDefinitions.length;
      result.metadata.checksPerformed = checkDefinitions.length;

      console.log(`HipaaOrchestrator: Executing ${checkDefinitions.length} checks`);

      // Step 2: Execute checks with concurrency control
      const checkResults = await this.executeChecksWithConcurrency(
        checkDefinitions,
        targetUrl,
        options,
      );

      result.checks = checkResults;

      // Step 3: Calculate summary statistics with improved scoring
      console.log('📊 [Frontend Result Generation] Calculating summary statistics...');

      // Use improved scoring system
      const industryType = this.detectIndustryType(targetUrl);
      const improvedScoringOptions: ImprovedScoringOptions = {
        industryType,
        confidenceWeighting: false, // Start conservative
        debugLogging: true,
      };

      const improvedScoring = calculateImprovedOverallScore(checkResults, improvedScoringOptions);

      // Apply improved scoring results
      result.overallScore = improvedScoring.overallScore;
      result.overallPassed = improvedScoring.passed;

      // Calculate basic summary statistics
      const basicSummary = this.calculateBasicSummary(checkResults);
      result.summary = {
        ...basicSummary,
        overallScore: improvedScoring.overallScore,
        complianceLevel: improvedScoring.complianceLevel,
        riskLevel: this.calculateRiskLevel(basicSummary, improvedScoring.overallScore),
        analysisLevelsUsed: [],
      };

      console.log('✅ [Frontend Result Generation] Improved scoring applied:', {
        industryType,
        oldScoringWouldBe: basicSummary.overallScore,
        newOverallScore: result.overallScore,
        overallPassed: result.overallPassed,
        complianceLevel: improvedScoring.complianceLevel,
        totalChecks: result.summary.totalChecks,
        passedChecks: result.summary.passedChecks,
        failedChecks: result.summary.failedChecks,
        scoringBreakdown: improvedScoring.breakdown.map((b) => ({
          checkId: b.checkId,
          weight: b.weight,
          contribution: b.contribution.toFixed(1),
        })),
      });

      // Step 4: Determine analysis levels used
      console.log('🔍 [Frontend Result Generation] Extracting analysis levels...');
      const levelsUsed = this.extractAnalysisLevels(checkResults);
      result.summary.analysisLevelsUsed = levelsUsed;
      result.metadata.analysisLevelsUsed = levelsUsed;
      console.log('✅ [Frontend Result Generation] Analysis levels extracted:', levelsUsed);

      // Step 5: Generate recommendations
      console.log('💡 [Frontend Result Generation] Generating recommendations...');
      result.recommendations = this.generateRecommendations(checkResults);
      console.log('✅ [Frontend Result Generation] Recommendations generated:', {
        count: result.recommendations.length,
        priorities: result.recommendations.map((r) => ({ title: r.title, priority: r.priority })),
      });

      // Step 6: Compliance level already set by improved scoring
      console.log(
        '✅ [Frontend Result Generation] Compliance level from improved scoring:',
        result.summary.complianceLevel,
      );

      console.log(
        `🎯 [Frontend Result Generation] Enhanced HIPAA scan completed with ${result.overallScore}% compliance score`,
      );
    } catch (error) {
      console.error(`HipaaOrchestrator: Scan failed for ${targetUrl}:`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      result.metadata.errors.push(errorMessage);

      // Create error check result
      const errorCheck: HipaaCheckResult = {
        checkId: 'HIPAA-ERROR-001',
        name: 'Scan Execution Error',
        category: HipaaCheckCategory.HIPAA_SPECIFIC,
        passed: false,
        severity: HipaaSeverity.CRITICAL,
        confidence: 100,
        description: 'Critical error occurred during HIPAA compliance scan',
        details: {
          summary: `Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          findings: [
            {
              type: HipaaFindingType.ERROR,
              location: 'Scan execution',
              content: error instanceof Error ? error.message : 'Unknown error',
              severity: HipaaSeverity.CRITICAL,
              message: 'Scan execution failed',
              suggestion: 'Verify URL accessibility and try again',
              context: error instanceof Error ? error.name : 'Error',
              confidence: 100,
            },
          ],
          metrics: { processingTime: 0, contentLength: 0 },
          context: {
            url: targetUrl,
            pageTitle: '',
            lastModified: '',
            contentType: '',
            language: '',
          },
        },
        remediation: {
          priority: 'critical',
          effort: 'minimal',
          steps: ['Verify URL is accessible', 'Check network connectivity', 'Try scan again'],
          resources: [],
          timeline: 'Immediate',
        },
        evidence: [],
        metadata: {
          checkVersion: '2.0',
          processingTime: 0,
          analysisLevels: [],
          warnings: [],
        },
      };

      result.checks = [errorCheck];
      result.summary.totalChecks = 1;
      result.summary.failedChecks = 1;
      result.summary.criticalIssues = 1;
      result.summary.riskLevel = 'critical';
    }

    // Final processing
    const processingTime = Date.now() - startTime;
    result.metadata.processingTime = processingTime;

    console.log('🎯 [Frontend Result Generation] Final enhanced HIPAA result generated:', {
      scanId: scanId,
      targetUrl: result.targetUrl,
      overallScore: result.overallScore,
      overallPassed: result.overallPassed,
      complianceLevel: result.summary.complianceLevel,
      processingTime: processingTime + 'ms',
      checksExecuted: result.checks.length,
      analysisLevelsUsed: result.summary.analysisLevelsUsed,
      recommendationsGenerated: result.recommendations.length,
      issuesSummary: {
        critical: result.summary.criticalIssues,
        high: result.summary.highIssues,
        medium: result.summary.mediumIssues,
        low: result.summary.lowIssues,
      },
      levelBreakdown: result.checks.map((check) => ({
        checkId: check.checkId,
        name: check.name,
        passed: check.passed,
        score: check.overallScore,
        levelsUsed: check.metadata?.analysisLevels || [],
      })),
    });

    console.log(
      `🎯 [Frontend Result Generation] Enhanced HIPAA scan ${scanId} completed in ${processingTime}ms`,
    );

    return result;
  }

  /**
   * Define the checks to be performed
   */
  private defineChecks(options: HipaaScanOptions): CheckDefinition[] {
    const checks: CheckDefinition[] = [
      {
        id: 'privacy-policy-presence',
        name: 'Privacy Policy Presence',
        function: checkPrivacyPolicyPresence,
        priority: 1,
        timeout: options.timeout || this.defaultTimeout,
        required: true,
      },
      {
        id: 'hipaa-specific-content',
        name: 'HIPAA-Specific Content Analysis',
        function: checkHipaaSpecificContent,
        priority: 2,
        timeout: (options.timeout || this.defaultTimeout) * 3, // Longer timeout for 3-level analysis
        required: true,
      },
      {
        id: 'contact-information',
        name: 'Contact Information Validation',
        function: checkContactInformation,
        priority: 3,
        timeout: options.timeout || this.defaultTimeout,
        required: true,
      },
    ];

    // Filter checks based on options
    return checks.filter((check) => {
      if (
        !check.required &&
        options.enableLevel3 === false &&
        check.id === 'hipaa-specific-content'
      ) {
        return false;
      }
      return true;
    });
  }

  /**
   * Execute checks with concurrency control
   */
  private async executeChecksWithConcurrency(
    checkDefinitions: CheckDefinition[],
    targetUrl: string,
    options: HipaaScanOptions,
  ): Promise<HipaaCheckResult[]> {
    const results: HipaaCheckResult[] = [];
    const errors: (string | { message: string; timestamp: string; context: string })[] = [];

    // Sort checks by priority
    const sortedChecks = [...checkDefinitions].sort((a, b) => a.priority - b.priority);

    // Execute checks in batches to control concurrency
    for (let i = 0; i < sortedChecks.length; i += this.maxConcurrentChecks) {
      const batch = sortedChecks.slice(i, i + this.maxConcurrentChecks);

      console.log(
        `HipaaOrchestrator: Executing batch ${Math.floor(i / this.maxConcurrentChecks) + 1} with ${batch.length} checks`,
      );

      const batchPromises = batch.map(async (checkDef) => {
        try {
          console.log(`HipaaOrchestrator: Starting check ${checkDef.id}`);

          const checkOptions = {
            ...options,
            timeout: checkDef.timeout,
          };

          const result = await this.executeWithTimeout(
            checkDef.function(targetUrl, checkOptions),
            checkDef.timeout,
          );

          console.log(
            `HipaaOrchestrator: Completed check ${checkDef.id} - ${result.passed ? 'PASSED' : 'FAILED'}`,
          );
          return result;
        } catch (error) {
          console.error(`HipaaOrchestrator: Check ${checkDef.id} failed:`, error);
          errors.push(
            `${checkDef.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );

          // Return error result
          return this.createErrorCheckResult(
            checkDef,
            error instanceof Error ? error : new Error('Unknown error'),
          );
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    // Log any errors to metadata
    if (errors.length > 0) {
      console.warn(`HipaaOrchestrator: ${errors.length} checks failed:`, errors);
    }

    return results;
  }

  /**
   * Execute a promise with timeout
   */
  private async executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Check timed out after ${timeout}ms`)), timeout);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Create error check result
   */
  private createErrorCheckResult(checkDef: CheckDefinition, error: Error): HipaaCheckResult {
    return {
      checkId: `${checkDef.id.toUpperCase()}-ERROR`,
      name: `${checkDef.name} (Error)`,
      category: HipaaCheckCategory.HIPAA_SPECIFIC,
      passed: false,
      severity: HipaaSeverity.CRITICAL,
      confidence: 0,
      description: `Error occurred during ${checkDef.name}`,
      details: {
        summary: `Check failed: ${error.message}`,
        findings: [
          {
            type: HipaaFindingType.ERROR,
            location: 'Check execution',
            content: error.message,
            severity: HipaaSeverity.CRITICAL,
            message: `${checkDef.name} failed to execute`,
            suggestion: 'Review error details and retry',
            context: error.name || 'Unknown error',
            confidence: 100,
          },
        ],
        metrics: { processingTime: 0, contentLength: 0 },
        context: { url: '', pageTitle: '', lastModified: '', contentType: '', language: '' },
      },
      remediation: {
        priority: 'critical',
        effort: 'minimal',
        steps: ['Review error details', 'Verify system configuration', 'Retry check'],
        resources: [],
        timeline: 'Immediate',
      },
      evidence: [],
      metadata: {
        checkVersion: '2.0',
        processingTime: 0,
        analysisLevels: [],
        warnings: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }

  /**
   * Detect industry type based on URL for improved scoring
   */
  private detectIndustryType(url: string): 'healthcare' | 'general' {
    const healthcareIndicators = [
      'health',
      'medical',
      'hospital',
      'clinic',
      'doctor',
      'physician',
      'patient',
      'healthcare',
      'medicine',
      'therapy',
      'dental',
      'pharmacy',
      'telemedicine',
      'telehealth',
      'ehr',
      'emr',
      'hipaa',
    ];

    const urlLower = url.toLowerCase();
    const hasHealthcareIndicator = healthcareIndicators.some((indicator) =>
      urlLower.includes(indicator),
    );

    console.log(
      `🏥 [Industry Detection] URL: ${url} -> ${hasHealthcareIndicator ? 'healthcare' : 'general'}`,
    );
    return hasHealthcareIndicator ? 'healthcare' : 'general';
  }

  /**
   * Calculate basic summary statistics (without improved scoring)
   */
  private calculateBasicSummary(checkResults: HipaaCheckResult[]): HipaaScanSummary {
    const summary: HipaaScanSummary = {
      totalChecks: checkResults.length,
      passedChecks: 0,
      failedChecks: 0,
      criticalIssues: 0,
      highIssues: 0,
      mediumIssues: 0,
      lowIssues: 0,
      overallScore: 0,
      complianceLevel: 'non_compliant',
      riskLevel: 'critical', // Will be calculated later
      analysisLevelsUsed: [],
    };

    let totalScore = 0;
    let scoreCount = 0;

    checkResults.forEach((check) => {
      if (check.passed) {
        summary.passedChecks++;
      } else {
        summary.failedChecks++;
      }

      // Count issues by severity
      switch (check.severity) {
        case HipaaSeverity.CRITICAL:
          summary.criticalIssues++;
          break;
        case HipaaSeverity.HIGH:
          summary.highIssues++;
          break;
        case HipaaSeverity.MEDIUM:
          summary.mediumIssues++;
          break;
        case HipaaSeverity.LOW:
          summary.lowIssues++;
          break;
      }

      // Calculate weighted score
      if (check.overallScore !== undefined) {
        totalScore += check.overallScore;
        scoreCount++;
      } else if (check.confidence > 0) {
        totalScore += check.passed ? check.confidence : 100 - check.confidence;
        scoreCount++;
      }
    });

    // Calculate overall score
    summary.overallScore = scoreCount > 0 ? Math.round(totalScore / scoreCount) : 0;

    return summary;
  }

  /**
   * Calculate risk level based on score ranges, aligned with risk-weighted scoring
   */
  private calculateRiskLevel(
    summary: HipaaScanSummary,
    overallScore: number,
  ): 'critical' | 'high' | 'medium' | 'low' {
    // If there are critical issues, ensure appropriate risk level
    if (summary.criticalIssues > 0) {
      return 'critical';
    }

    // Use score-based risk levels that align with our weighted scoring
    if (overallScore <= 30) return 'critical'; // 0-30%: Critical Risk
    if (overallScore <= 60) return 'high'; // 31-60%: High Risk
    if (overallScore <= 80) return 'medium'; // 61-80%: Medium Risk
    return 'low'; // 81-100%: Low Risk
  }

  /**
   * Extract analysis levels used
   */
  private extractAnalysisLevels(checkResults: HipaaCheckResult[]): number[] {
    const levels = new Set<number>();

    checkResults.forEach((check) => {
      if (check.metadata?.analysisLevels) {
        check.metadata.analysisLevels.forEach((level) => levels.add(level));
      }
    });

    return Array.from(levels).sort();
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(checkResults: HipaaCheckResult[]): HipaaRecommendation[] {
    const recommendations: HipaaRecommendation[] = [];
    let recommendationId = 1;

    // Extract AI-generated recommendations from Level 3 analysis
    checkResults.forEach((check) => {
      // Extract AI recommendations from Level 3 results
      if (check.levelResults?.level3?.recommendations) {
        check.levelResults.level3.recommendations.forEach((aiRec) => {
          const recommendation: HipaaRecommendation = {
            id: `AI-REC-${recommendationId.toString().padStart(3, '0')}`,
            priority: aiRec.priority,
            title: aiRec.title,
            description: aiRec.description,
            category: check.category,
            effort: aiRec.effort,
            impact: aiRec.impact,
            timeline: this.mapEffortToTimeline(aiRec.effort),
            resources: [
              {
                title: 'Implementation Guide',
                url: '#',
                type: 'guide',
                description: aiRec.implementation,
              },
            ],
            relatedChecks: [check.checkId],
          };

          recommendations.push(recommendation);
          recommendationId++;
        });
      }

      // Also include traditional remediation-based recommendations for failed checks
      if (!check.passed && check.remediation) {
        const recommendation: HipaaRecommendation = {
          id: `REM-${recommendationId.toString().padStart(3, '0')}`,
          priority: this.mapPriorityToNumber(check.remediation.priority),
          title: `Address ${check.name} Issues`,
          description: check.details.summary || `Resolve issues found in ${check.name}`,
          category: check.category,
          effort: check.remediation.effort,
          impact: this.mapSeverityToImpact(check.severity),
          timeline: check.remediation.timeline,
          resources: check.remediation.resources || [],
          relatedChecks: [check.checkId],
        };

        recommendations.push(recommendation);
        recommendationId++;
      }
    });

    // Sort by priority (lower number = higher priority)
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Determine compliance level based on score
   */
  private determineComplianceLevel(
    score: number,
  ): 'compliant' | 'mostly_compliant' | 'partially_compliant' | 'non_compliant' {
    if (score >= 90) return 'compliant';
    if (score >= 70) return 'mostly_compliant';
    if (score >= 50) return 'partially_compliant';
    return 'non_compliant';
  }

  /**
   * Generate unique scan ID
   */
  private generateScanId(): string {
    return `hipaa-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Map priority string to number
   */
  private mapPriorityToNumber(priority: string): number {
    const priorityMap: Record<string, number> = {
      critical: 1,
      high: 2,
      medium: 3,
      low: 4,
    };
    return priorityMap[priority] || 3;
  }

  /**
   * Map severity to impact
   */
  private mapSeverityToImpact(severity: HipaaSeverity): 'low' | 'medium' | 'high' {
    switch (severity) {
      case HipaaSeverity.CRITICAL:
      case HipaaSeverity.HIGH:
        return 'high';
      case HipaaSeverity.MEDIUM:
        return 'medium';
      case HipaaSeverity.LOW:
      case HipaaSeverity.INFO:
        return 'low';
      default:
        return 'medium';
    }
  }

  /**
   * Map effort level to timeline estimate
   */
  private mapEffortToTimeline(effort: string): string {
    const timelineMap: Record<string, string> = {
      minimal: '1-2 hours',
      moderate: '4-8 hours',
      significant: '1-2 days',
      extensive: '3-5 days',
    };
    return timelineMap[effort] || '2-4 hours';
  }
}

// Helper interface
interface CheckDefinition {
  id: string;
  name: string;
  function: (url: string, options: CheckOptions) => Promise<HipaaCheckResult>;
  priority: number;
  timeout: number;
  required: boolean;
}
