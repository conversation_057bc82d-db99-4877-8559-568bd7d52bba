// backend/src/compliance/hipaa/privacy/services/scoring-service.ts

import knex from '../../../../lib/db';
import { calculateImprovedOverallScore } from '../utils/improved-scoring';
import { HipaaDatabase } from '../database/hipaa-privacy-db';

export interface MigrationOptions {
  batchSize?: number;
  dryRun?: boolean;
  industryDetection?: boolean;
  preserveLegacyScores?: boolean;
  logProgress?: boolean;
}

export interface MigrationResult {
  totalScans: number;
  successfulMigrations: number;
  failedMigrations: number;
  averageScoreChange: number;
  significantChanges: Array<{
    scanId: string;
    oldScore: number;
    newScore: number;
    percentChange: number;
  }>;
  errors: string[];
}

/**
 * Service for migrating existing HIPAA scans to improved scoring system
 */
export class ScoringMigrationService {
  /**
   * Migrate all existing HIPAA scans to new scoring system
   */
  static async migrateAllScans(options: MigrationOptions = {}): Promise<MigrationResult> {
    const {
      batchSize = 100,
      dryRun = false,
      industryDetection = true,
      preserveLegacyScores = true,
      logProgress = true,
    } = options;

    const result: MigrationResult = {
      totalScans: 0,
      successfulMigrations: 0,
      failedMigrations: 0,
      averageScoreChange: 0,
      significantChanges: [],
      errors: [],
    };

    try {
      // Get total count for progress tracking
      const countResult = await knex('hipaa_scans').count('id as total');
      result.totalScans = parseInt(countResult[0].total as string);

      if (logProgress) {
        console.log(`🔄 [Migration] Starting migration of ${result.totalScans} HIPAA scans`);
      }

      // Process in batches
      let offset = 0;
      let totalScoreChange = 0;

      while (offset < result.totalScans) {
        const batch = await knex('hipaa_scans').select('*').limit(batchSize).offset(offset);

        for (const scan of batch) {
          try {
            const migrationResult = await this.migrateSingleScan(scan.id, {
              dryRun,
              industryDetection,
              preserveLegacyScores,
            });

            if (migrationResult.success) {
              result.successfulMigrations++;
              totalScoreChange += migrationResult.scoreChange || 0;

              // Track significant changes (>10% difference)
              if (Math.abs(migrationResult.percentChange || 0) > 10) {
                result.significantChanges.push({
                  scanId: scan.id,
                  oldScore: migrationResult.oldScore || 0,
                  newScore: migrationResult.newScore || 0,
                  percentChange: migrationResult.percentChange || 0,
                });
              }
            } else {
              result.failedMigrations++;
              if (migrationResult.error) {
                result.errors.push(`Scan ${scan.id}: ${migrationResult.error}`);
              }
            }
          } catch (error) {
            result.failedMigrations++;
            result.errors.push(
              `Scan ${scan.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
          }
        }

        offset += batchSize;

        if (logProgress && offset % (batchSize * 10) === 0) {
          console.log(`🔄 [Migration] Processed ${offset}/${result.totalScans} scans`);
        }
      }

      // Calculate average score change
      result.averageScoreChange =
        result.successfulMigrations > 0 ? totalScoreChange / result.successfulMigrations : 0;

      if (logProgress) {
        console.log(
          `✅ [Migration] Complete: ${result.successfulMigrations} successful, ${result.failedMigrations} failed`,
        );
        console.log(
          `📊 [Migration] Average score change: ${result.averageScoreChange.toFixed(2)}%`,
        );
        console.log(`⚠️ [Migration] Significant changes: ${result.significantChanges.length}`);
      }
    } catch (error) {
      result.errors.push(
        `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }

    return result;
  }

  /**
   * Migrate a single scan to new scoring system
   */
  static async migrateSingleScan(
    scanId: string,
    options: Omit<MigrationOptions, 'batchSize'> = {},
  ): Promise<{
    success: boolean;
    oldScore?: number;
    newScore?: number;
    scoreChange?: number;
    percentChange?: number;
    error?: string;
  }> {
    const { dryRun = false, industryDetection = true, preserveLegacyScores = true } = options;

    try {
      // Get existing scan data
      const scanData = await HipaaDatabase.getScanResult(scanId);
      if (!scanData) {
        return { success: false, error: 'Scan not found' };
      }

      const oldScore = parseFloat(scanData.overallScore.toString());

      // Detect industry type if enabled
      const industryType = industryDetection
        ? await this.detectIndustryType(scanData.targetUrl)
        : 'general';

      // Calculate new score
      const newScoringResult = calculateImprovedOverallScore(scanData.checks, {
        industryType,
        confidenceWeighting: false, // Start conservative
        debugLogging: false,
      });

      const scoreChange = newScoringResult.overallScore - oldScore;
      const percentChange = oldScore > 0 ? (scoreChange / oldScore) * 100 : 0;

      if (!dryRun) {
        // Update database with new scoring
        await this.updateScanWithNewScoring(scanId, {
          newScore: newScoringResult.overallScore,
          newComplianceLevel: newScoringResult.complianceLevel,
          newPassed: newScoringResult.passed,
          industryType,
          scoringMetadata: {
            scoringVersion: '2.0',
            checkWeights: newScoringResult.breakdown.reduce(
              (acc, check) => {
                acc[check.checkId] = check.weight;
                return acc;
              },
              {} as Record<string, number>,
            ),
            checkContributions: newScoringResult.breakdown.reduce(
              (acc, check) => {
                acc[check.checkId] = check.contribution;
                return acc;
              },
              {} as Record<string, number>,
            ),
            legacyScore: preserveLegacyScores ? oldScore : null,
            scoreDifference: scoreChange,
            rawWeightedScore: newScoringResult.weightedScore,
          },
        });
      }

      return {
        success: true,
        oldScore,
        newScore: newScoringResult.overallScore,
        scoreChange,
        percentChange,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Detect industry type based on URL and content analysis
   */
  private static async detectIndustryType(url: string): Promise<'healthcare' | 'general'> {
    // Simple heuristics for industry detection
    const healthcareIndicators = [
      'health',
      'medical',
      'hospital',
      'clinic',
      'doctor',
      'physician',
      'patient',
      'healthcare',
      'medicine',
      'therapy',
      'dental',
      'pharmacy',
    ];

    const urlLower = url.toLowerCase();
    const hasHealthcareIndicator = healthcareIndicators.some((indicator) =>
      urlLower.includes(indicator),
    );

    return hasHealthcareIndicator ? 'healthcare' : 'general';
  }

  /**
   * Update scan record with new scoring data
   */
  private static async updateScanWithNewScoring(
    scanId: string,
    data: {
      newScore: number;
      newComplianceLevel: string;
      newPassed: boolean;
      industryType: string;
      scoringMetadata: {
        scoringVersion: string;
        checkWeights: Record<string, number>;
        checkContributions: Record<string, number>;
        legacyScore: number | null;
        scoreDifference: number;
        rawWeightedScore: number;
      };
    },
  ): Promise<void> {
    const trx = await knex.transaction();

    try {
      // Update main scan record
      await trx('hipaa_scans')
        .where('id', scanId)
        .update({
          overall_score: data.newScore.toFixed(2),
          compliance_level: data.newComplianceLevel,
          overall_passed: data.newPassed,
          scoring_version: '2.0',
          industry_type: data.industryType,
          raw_weighted_score: data.scoringMetadata.rawWeightedScore.toFixed(2),
          updated_at: knex.fn.now(),
        });

      // Insert scoring metadata
      await trx('hipaa_scoring_metadata').insert({
        hipaa_scan_id: scanId,
        scoring_version: '2.0',
        scoring_method: 'weighted_checks',
        industry_type: data.industryType,
        thresholds_used: JSON.stringify({
          industryType: data.industryType,
          passingThreshold: data.industryType === 'healthcare' ? 75 : 60,
        }),
        check_weights: JSON.stringify(data.scoringMetadata.checkWeights),
        check_contributions: JSON.stringify(data.scoringMetadata.checkContributions),
        level_weights: JSON.stringify({
          level1: 0.3,
          level2: 0.35,
          level3: 0.35,
        }),
        raw_weighted_score: data.scoringMetadata.rawWeightedScore.toFixed(2),
        confidence_adjusted: false,
        legacy_score: data.scoringMetadata.legacyScore?.toFixed(2) || null,
        score_difference: data.scoringMetadata.scoreDifference.toFixed(2),
        migration_notes: 'Migrated from v1.0 to v2.0 scoring system',
      });

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   * Generate migration report
   */
  static async generateMigrationReport(result: MigrationResult): Promise<string> {
    const report = `
# HIPAA Scoring Migration Report

## Summary
- **Total Scans**: ${result.totalScans}
- **Successful Migrations**: ${result.successfulMigrations}
- **Failed Migrations**: ${result.failedMigrations}
- **Success Rate**: ${((result.successfulMigrations / result.totalScans) * 100).toFixed(1)}%

## Score Changes
- **Average Score Change**: ${result.averageScoreChange.toFixed(2)}%
- **Significant Changes (>10%)**: ${result.significantChanges.length}

## Significant Changes Detail
${result.significantChanges
  .map(
    (change) =>
      `- Scan ${change.scanId}: ${change.oldScore}% → ${change.newScore}% (${change.percentChange.toFixed(1)}%)`,
  )
  .join('\n')}

## Errors
${result.errors.length > 0 ? result.errors.map((error) => `- ${error}`).join('\n') : 'No errors occurred'}

## Recommendations
${this.generateRecommendations(result)}
    `.trim();

    return report;
  }

  private static generateRecommendations(result: MigrationResult): string {
    const recommendations: string[] = [];

    if (result.failedMigrations > 0) {
      recommendations.push('- Review and fix failed migrations before proceeding');
    }

    if (result.significantChanges.length > result.totalScans * 0.1) {
      recommendations.push('- High number of significant changes - validate new scoring logic');
    }

    if (Math.abs(result.averageScoreChange) > 5) {
      recommendations.push('- Average score change >5% - consider gradual rollout');
    }

    if (recommendations.length === 0) {
      recommendations.push('- Migration completed successfully - safe to deploy');
    }

    return recommendations.join('\n');
  }
}
