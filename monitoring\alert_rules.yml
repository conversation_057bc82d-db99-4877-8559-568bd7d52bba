# Prometheus Alert Rules for HIPAA Dashboard
# Comprehensive alerting for production monitoring

groups:
  # Application Health Alerts
  - name: application_health
    rules:
      - alert: ApplicationDown
        expr: up{job=~"hipaa-frontend|hipaa-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "{{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

  # System Resource Alerts
  - name: system_resources
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} for {{ $labels.mountpoint }}"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Disk space critically low"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} for {{ $labels.mountpoint }}"

  # Database Alerts
  - name: database_health
    rules:
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database connections are at {{ $value | humanizePercentage }} of maximum"

      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Database slow queries detected"
          description: "Database query efficiency is low: {{ $value | humanizePercentage }}"

  # HIPAA Compliance Alerts
  - name: hipaa_compliance
    rules:
      - alert: ComplianceScoreLow
        expr: hipaa_overall_compliance_score < 70
        for: 5m
        labels:
          severity: warning
          compliance: hipaa
        annotations:
          summary: "HIPAA compliance score is low"
          description: "Overall HIPAA compliance score is {{ $value }}%"

      - alert: CriticalComplianceIssue
        expr: hipaa_critical_issues_count > 0
        for: 1m
        labels:
          severity: critical
          compliance: hipaa
        annotations:
          summary: "Critical HIPAA compliance issues detected"
          description: "{{ $value }} critical HIPAA compliance issues found"

      - alert: ScanFailure
        expr: increase(hipaa_scan_failures_total[1h]) > 3
        for: 5m
        labels:
          severity: warning
          compliance: hipaa
        annotations:
          summary: "Multiple HIPAA scan failures"
          description: "{{ $value }} HIPAA scans have failed in the last hour"

      - alert: SecurityVulnerabilityHigh
        expr: hipaa_security_vulnerabilities{severity="high"} > 0
        for: 1m
        labels:
          severity: critical
          security: vulnerability
        annotations:
          summary: "High severity security vulnerabilities detected"
          description: "{{ $value }} high severity vulnerabilities found in security scan"

  # SSL Certificate Alerts
  - name: ssl_certificates
    rules:
      - alert: SSLCertificateExpiringSoon
        expr: ssl_certificate_expiry_seconds < 30 * 24 * 3600
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"

      - alert: SSLCertificateExpired
        expr: ssl_certificate_expiry_seconds < 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "SSL certificate expired"
          description: "SSL certificate for {{ $labels.instance }} has expired"

  # Security Alerts
  - name: security
    rules:
      - alert: UnauthorizedAccess
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          security: unauthorized
        annotations:
          summary: "High rate of unauthorized access attempts"
          description: "{{ $value }} unauthorized access attempts per second"

      - alert: SuspiciousActivity
        expr: rate(http_requests_total{status="403"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          security: suspicious
        annotations:
          summary: "Suspicious activity detected"
          description: "{{ $value }} forbidden requests per second"

      - alert: DDoSAttack
        expr: rate(http_requests_total[1m]) > 1000
        for: 1m
        labels:
          severity: critical
          security: ddos
        annotations:
          summary: "Potential DDoS attack detected"
          description: "{{ $value }} requests per second - potential DDoS attack"

  # Performance Alerts
  - name: performance
    rules:
      - alert: HighLatency
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
          performance: latency
        annotations:
          summary: "High latency detected"
          description: "99th percentile latency is {{ $value }}s"

      - alert: LowThroughput
        expr: rate(http_requests_total[5m]) < 1
        for: 10m
        labels:
          severity: warning
          performance: throughput
        annotations:
          summary: "Low throughput detected"
          description: "Request rate is {{ $value }} requests per second"

  # Business Logic Alerts
  - name: business_logic
    rules:
      - alert: NoRecentScans
        expr: time() - hipaa_last_scan_timestamp > 24 * 3600
        for: 1h
        labels:
          severity: warning
          business: scans
        annotations:
          summary: "No recent HIPAA scans"
          description: "No HIPAA scans have been performed in the last 24 hours"

      - alert: HighScanQueueLength
        expr: hipaa_scan_queue_length > 10
        for: 10m
        labels:
          severity: warning
          business: scans
        annotations:
          summary: "High scan queue length"
          description: "{{ $value }} scans are queued for processing"

      - alert: ScanProcessingStuck
        expr: hipaa_scan_processing_duration_seconds > 1800
        for: 5m
        labels:
          severity: critical
          business: scans
        annotations:
          summary: "Scan processing appears stuck"
          description: "A scan has been processing for {{ $value | humanizeDuration }}"
