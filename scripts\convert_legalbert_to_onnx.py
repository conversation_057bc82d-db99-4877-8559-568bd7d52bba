#!/usr/bin/env python3
"""
Convert LegalBERT models to ONNX format for Transformers.js compatibility
"""

import os
import torch
from transformers import AutoTokenizer, AutoModel
from optimum.onnxruntime import ORTModelForSequenceClassification
from optimum.exporters.onnx import main_export

def convert_legalbert_to_onnx():
    """Convert LegalBERT models to ONNX format"""
    
    # LegalBERT models to convert
    models_to_convert = [
        "nlpaueb/legal-bert-base-uncased",
        "law-ai/InLegalBERT",
        "zlucia/custom-legalbert"
    ]
    
    output_dir = "./onnx_models"
    os.makedirs(output_dir, exist_ok=True)
    
    for model_name in models_to_convert:
        try:
            print(f"🔄 Converting {model_name} to ONNX...")
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModel.from_pretrained(model_name)
            
            # Convert to ONNX
            model_output_dir = os.path.join(output_dir, model_name.replace("/", "_"))
            os.makedirs(model_output_dir, exist_ok=True)
            
            # Export to ONNX
            main_export(
                model_name_or_path=model_name,
                output=model_output_dir,
                task="text-classification",
                opset=11,
                optimize="O2",
                fp16=False
            )
            
            print(f"✅ Successfully converted {model_name}")
            print(f"📁 Output directory: {model_output_dir}")
            
        except Exception as e:
            print(f"❌ Failed to convert {model_name}: {e}")
            continue

if __name__ == "__main__":
    print("🚀 Starting LegalBERT to ONNX conversion...")
    convert_legalbert_to_onnx()
    print("🎉 Conversion process completed!")
