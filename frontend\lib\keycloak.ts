import keycloakDefaultImport, { KeycloakConfig, KeycloakInitOptions } from 'keycloak-js'; // Aliased to satisfy naming convention & Prettier

// Note: Replace with your actual Keycloak server URL, realm, and frontend client ID
// These should ideally be stored in environment variables for a real application
const keycloakConfig: KeycloakConfig = {
  url: process.env.NEXT_PUBLIC_KEYCLOAK_URL || 'http://localhost:8080/auth', // Ensure this is your Keycloak auth server URL
  realm: process.env.NEXT_PUBLIC_KEYCLOAK_REALM || 'complychecker', // Ensure this is your realm
  clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID_FRONTEND || 'complychecker-frontend', // Ensure this is your PUBLIC frontend client ID
};

let keycloakInstance: keycloakDefaultImport | null = null;

const getKeycloakInstance = (): keycloakDefaultImport => {
  if (typeof window !== 'undefined' && !keycloakInstance) {
    keycloakInstance = new keycloakDefaultImport(keycloakConfig);
  }
  return keycloakInstance as keycloakDefaultImport;
};

// Initialization options
const initOptions: KeycloakInitOptions = {
  onLoad: 'check-sso',
  silentCheckSsoRedirectUri:
    typeof window !== 'undefined' ? window.location.origin + '/silent-check-sso.html' : undefined,
  pkceMethod: 'S256',
  checkLoginIframe: false, // Disable iframe check for debugging
  enableLogging: true, // Enable Keycloak logging
};

export { getKeycloakInstance, initOptions };

// You will also need to create a `public/silent-check-sso.html` file for silent SSO checks.
// Content for silent-check-sso.html:
// <!DOCTYPE html>
// <html>
// <head>
//   <title>Keycloak Silent Check SSO</title>
// </head>
// <body>
//   <script>
//     parent.postMessage(location.href, location.origin);
//   </script>
// </body>
// </html>
