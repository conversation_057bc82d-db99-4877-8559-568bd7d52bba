# PostgreSQL Configuration
POSTGRES_USER=complyuser
POSTGRES_PASSWORD=complypassword
POSTGRES_DB=complychecker_dev
POSTGRES_PORT=5432
DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/${POSTGRES_DB}"

# Keycloak Configuration
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_PORT=8080
KEYCLOAK_REALM=complychecker
KEYCLOAK_CLIENT_ID_FRONTEND=complychecker-frontend
KEYCLOAK_URL=http://localhost:${KEYCLOAK_PORT}/auth

# MailHog Configuration
MAILHOG_SMTP_PORT=1025
MAILHOG_HTTP_PORT=8025
SMTP_HOST=mailhog # service name in docker-compose
SMTP_PORT=1025

# Backend Application Configuration
BACKEND_PORT=3001
FRONTEND_URL=http://localhost:3000

# Frontend Application Configuration (can also be in frontend/.env.local)
# NEXT_PUBLIC_KEYCLOAK_URL=${KEYCLOAK_URL}
# NEXT_PUBLIC_KEYCLOAK_REALM=${KEYCLOAK_REALM}
# NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID_FRONTEND}
# NEXT_PUBLIC_BACKEND_API_URL=http://localhost:${BACKEND_PORT}/api/v1

# HIPAA Security Configuration
HIPAA_SECURITY_ENABLED=true

# Nuclei Configuration (for vulnerability scanning)
NUCLEI_PATH=nuclei
NUCLEI_TEMPLATES_PATH=./nuclei-templates
NUCLEI_ENABLED=true

# HIPAA Scan Settings
HIPAA_SCAN_TIMEOUT=300000
HIPAA_MAX_PAGES=15
HIPAA_SCAN_DEPTH=2
HIPAA_ENABLE_VULN_SCAN=true
HIPAA_ENABLE_SSL_ANALYSIS=true
HIPAA_ENABLE_CONTENT_ANALYSIS=true

# SSL Labs API (optional - for enhanced SSL analysis)
SSL_LABS_API_URL=https://api.ssllabs.com/api/v3/

# Security scan scheduling
HIPAA_SCAN_SCHEDULE=0 22 15 * * 6
HIPAA_SCAN_ENABLED=true

# Enhanced HIPAA module (default: enabled)
ENABLE_ENHANCED_HIPAA=true

# Scanning behavior
HIPAA_USER_AGENT=HIPAA-Security-Scanner/1.0
HIPAA_MAX_CONCURRENT_REQUESTS=3
HIPAA_REQUEST_TIMEOUT=30000
HIPAA_RETRY_ATTEMPTS=3
