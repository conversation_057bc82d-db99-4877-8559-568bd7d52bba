/**
 * HIPAA Security Test Runner Example - Part 3 Implementation
 * This file demonstrates how to use the HIPAA Security Test Modules
 *
 * NOTE: This is an example file. For actual Jest tests, see the __tests__ directory.
 */

import {
  HipaaTestOrchestrator,
  SSLAnalyzer,
  ContentAnalyzer,
  ScannerConfigService,
} from '../index';
import { HipaaTestSuiteResult } from './hipaa-test-orchestrator';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';

export class HipaaSecurityTestRunner {
  private orchestrator: HipaaTestOrchestrator;
  private configService: ScannerConfigService;

  constructor() {
    this.configService = ScannerConfigService.getInstance();

    // Initialize services - now using Nuclei instead of ZAP
    // Get scanner config if needed in the future
    this.configService.getScannerConfig();

    // Create orchestrator (now uses Nuclei internally)
    this.orchestrator = new HipaaTestOrchestrator(
      new (require('../services/nuclei-client').NucleiClient)(),
      new SSLAnalyzer(),
      new ContentAnalyzer(),
    );
  }

  /**
   * Run a complete HIPAA security test suite
   */
  async runComprehensiveTest(targetUrl: string): Promise<void> {
    console.log('🔍 Starting HIPAA Security Compliance Test Suite');
    console.log(`🎯 Target: ${targetUrl}`);
    console.log('='.repeat(60));

    try {
      // Create test configuration
      const config = HipaaTestOrchestrator.createDefaultConfig(targetUrl);

      // Customize configuration for comprehensive testing
      config.protectedEndpoints = [
        '/admin',
        '/dashboard',
        '/account',
        '/api/users',
        '/api/patients',
        '/api/medical-records',
        '/logs',
        '/audit',
      ];

      config.pagesToScan = [
        '/',
        '/login',
        '/register',
        '/about',
        '/contact',
        '/dashboard',
        '/admin',
        '/privacy',
        '/terms',
      ];

      // Run the test suite
      const startTime = Date.now();
      const results = await this.orchestrator.runHipaaTestSuite(config);
      const executionTime = Date.now() - startTime;

      // Display results
      this.displayResults(results, executionTime);
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      throw error;
    }
  }

  /**
   * Run individual test categories
   */
  async runTechnicalSafeguardsOnly(targetUrl: string): Promise<void> {
    console.log('🔧 Running Technical Safeguards Tests Only');

    const config = HipaaTestOrchestrator.createDefaultConfig(targetUrl);
    config.testCategories = ['technical'];

    const results = await this.orchestrator.runHipaaTestSuite(config);
    this.displayResults(results);
  }

  /**
   * Display comprehensive test results
   */
  private displayResults(results: HipaaTestSuiteResult, executionTime?: number): void {
    console.log('\n📊 HIPAA Security Test Results');
    console.log('='.repeat(60));

    // Overall results
    console.log(`🎯 Overall Score: ${results.overallScore}%`);
    console.log(`⚠️  Risk Level: ${results.riskLevel.toUpperCase()}`);
    console.log(`✅ Tests Passed: ${results.passedTests}/${results.totalTests}`);

    if (executionTime) {
      console.log(`⏱️  Execution Time: ${executionTime}ms`);
    }

    console.log('\n📋 Category Breakdown:');
    console.log(
      `  Technical Safeguards: ${results.technicalSafeguards.score}% (${results.technicalSafeguards.passedTests}/${results.technicalSafeguards.totalTests})`,
    );
    console.log(
      `  Administrative: ${results.administrativeSafeguards.score}% (${results.administrativeSafeguards.passedTests}/${results.administrativeSafeguards.totalTests})`,
    );
    console.log(
      `  Organizational: ${results.organizationalSafeguards.score}% (${results.organizationalSafeguards.passedTests}/${results.organizationalSafeguards.totalTests})`,
    );

    // Issue summary
    console.log('\n🚨 Issues by Severity:');
    console.log(`  Critical: ${results.criticalIssues}`);
    console.log(`  High: ${results.highIssues}`);
    console.log(`  Medium: ${results.mediumIssues}`);
    console.log(`  Low: ${results.lowIssues}`);

    // Failed tests details
    if (results.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      const failedTests = results.allTestResults.filter(
        (test): test is HipaaTestFailure => !test.passed,
      );

      failedTests.forEach((test: HipaaTestFailure, index: number) => {
        console.log(`\n  ${index + 1}. ${test.testName} (${test.hipaaSection})`);
        console.log(`     Risk: ${test.riskLevel} | Priority: ${test.remediationPriority}`);
        console.log(`     Reason: ${test.failureReason}`);
        console.log(`     Action: ${test.recommendedAction}`);

        if (test.failureEvidence && test.failureEvidence.length > 0) {
          console.log(`     Evidence: ${test.failureEvidence.length} items`);
          test.failureEvidence
            .slice(0, 2)
            .forEach((evidence: FailureEvidence, evidenceIndex: number) => {
              console.log(`       ${evidenceIndex + 1}. ${evidence.location}`);
              console.log(`          Expected: ${evidence.expectedBehavior}`);
              console.log(`          Context: ${evidence.context}`);
            });
        }
      });
    }

    // Passed tests summary
    if (results.passedTests > 0) {
      console.log('\n✅ Passed Tests Summary:');
      const passedTests = results.allTestResults.filter(
        (test): test is HipaaTestDetail => test.passed,
      );
      passedTests.forEach((test: HipaaTestDetail) => {
        console.log(`  ✓ ${test.testName} (${test.hipaaSection})`);
      });
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 HIPAA Security Test Suite Complete!');
  }

  /**
   * Generate a simple compliance report
   */
  async generateComplianceReport(targetUrl: string): Promise<string> {
    const config = HipaaTestOrchestrator.createDefaultConfig(targetUrl);
    const results = await this.orchestrator.runHipaaTestSuite(config);

    const report = `
HIPAA Security Compliance Report
Generated: ${new Date().toISOString()}
Target: ${targetUrl}

OVERALL COMPLIANCE SCORE: ${results.overallScore}%
RISK LEVEL: ${results.riskLevel.toUpperCase()}

CATEGORY SCORES:
- Technical Safeguards: ${results.technicalSafeguards.score}%
- Administrative Safeguards: ${results.administrativeSafeguards.score}%
- Organizational Safeguards: ${results.organizationalSafeguards.score}%

ISSUES SUMMARY:
- Critical Issues: ${results.criticalIssues}
- High Priority Issues: ${results.highIssues}
- Medium Priority Issues: ${results.mediumIssues}
- Low Priority Issues: ${results.lowIssues}

TESTS EXECUTED: ${results.totalTests}
TESTS PASSED: ${results.passedTests}
TESTS FAILED: ${results.failedTests}

${results.failedTests > 0 ? 'IMMEDIATE ACTION REQUIRED FOR FAILED TESTS' : 'ALL TESTS PASSED - GOOD COMPLIANCE STATUS'}
`;

    return report;
  }
}

// Example usage
export async function runHipaaTestExample(): Promise<void> {
  const runner = new HipaaSecurityTestRunner();

  // Example target URL (replace with actual URL)
  const targetUrl = 'https://example.com';

  try {
    console.log('🚀 Starting HIPAA Security Test Example');
    await runner.runComprehensiveTest(targetUrl);

    // Generate report
    const report = await runner.generateComplianceReport(targetUrl);
    console.log('\n📄 Compliance Report:');
    console.log(report);
  } catch (error) {
    console.error('❌ Example execution failed:', error);
  }
}

// Export for use in other files
export default HipaaSecurityTestRunner;

// If running this file directly
if (require.main === module) {
  runHipaaTestExample().catch(console.error);
}
