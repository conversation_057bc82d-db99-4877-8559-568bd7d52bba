import type { Knex } from 'knex';

/**
 * Enhanced HIPAA Analysis Schema Migration
 * Adds comprehensive support for 3-level analysis system with detailed findings,
 * remediation guidance, and metadata tracking
 */

export async function up(knex: Knex): Promise<void> {
  // Create HIPAA Scans table for detailed scan tracking
  await knex.schema.createTable('hipaa_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').references('id').inTable('scans').onDelete('CASCADE').notNullable();
    table.string('target_url').notNullable();
    table.integer('overall_score').notNullable(); // 0-100 compliance score
    table.boolean('overall_passed').notNullable();
    table.string('compliance_level').notNullable(); // compliant, mostly_compliant, partially_compliant, non_compliant
    table.jsonb('analysis_levels_used').notNullable(); // [1, 2, 3] - which levels were executed
    table.integer('processing_time_ms').notNullable();
    table.integer('total_checks').notNullable();
    table.integer('passed_checks').notNullable();
    table.integer('failed_checks').notNullable();
    table.integer('critical_issues').defaultTo(0);
    table.integer('high_issues').defaultTo(0);
    table.integer('medium_issues').defaultTo(0);
    table.integer('low_issues').defaultTo(0);
    table.jsonb('scan_options'); // Configuration used for the scan
    table.integer('cache_hits').defaultTo(0);
    table.jsonb('errors'); // Array of error messages
    table.jsonb('warnings'); // Array of warning messages
    table.string('user_agent');
    table.string('scan_version').defaultTo('2.0');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
    table.timestamp('completed_at');

    // Indexes for performance
    table.index(['scan_id']);
    table.index(['target_url']);
    table.index(['overall_score']);
    table.index(['compliance_level']);
    table.index(['created_at']);
  });

  // Create HIPAA Check Results table for individual check details
  await knex.schema.createTable('hipaa_check_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('hipaa_scan_id')
      .references('id')
      .inTable('hipaa_scans')
      .onDelete('CASCADE')
      .notNullable();
    table.string('check_id').notNullable(); // e.g., 'HIPAA-PP-001'
    table.string('name').notNullable();
    table.string('category').notNullable(); // presence, accessibility, content_structure, etc.
    table.boolean('passed').notNullable();
    table.string('severity').notNullable(); // critical, high, medium, low, info
    table.integer('confidence').notNullable(); // 0-100 confidence score
    table.text('description').notNullable();
    table.integer('overall_score'); // 0-100 combined score from all levels
    table.integer('processing_time_ms').notNullable();
    table.string('check_version').defaultTo('2.0');
    table.jsonb('analysis_levels_executed'); // [1, 2, 3] - which levels ran for this check
    table.jsonb('errors'); // Array of errors during check execution
    table.jsonb('warnings'); // Array of warnings during check execution
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['hipaa_scan_id']);
    table.index(['check_id']);
    table.index(['category']);
    table.index(['passed']);
    table.index(['severity']);
    table.index(['confidence']);
  });

  // Create HIPAA Findings table for detailed findings from each check
  await knex.schema.createTable('hipaa_findings', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('check_result_id')
      .references('id')
      .inTable('hipaa_check_results')
      .onDelete('CASCADE')
      .notNullable();
    table.string('type').notNullable(); // missing_content, exact_match, context_match, etc.
    table.string('location').notNullable(); // Position or section where finding was made
    table.text('content').notNullable(); // The actual content found or missing
    table.string('severity').notNullable(); // critical, high, medium, low, info
    table.text('message').notNullable(); // Human-readable description
    table.text('suggestion').notNullable(); // Remediation suggestion
    table.text('context'); // Surrounding context for better understanding
    table.integer('confidence'); // 0-100 confidence score for this finding
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['check_result_id']);
    table.index(['type']);
    table.index(['severity']);
  });

  // Create HIPAA Analysis Levels table for storing 3-level analysis results
  await knex.schema.createTable('hipaa_analysis_levels', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('check_result_id')
      .references('id')
      .inTable('hipaa_check_results')
      .onDelete('CASCADE')
      .notNullable();
    table.integer('level').notNullable(); // 1, 2, or 3
    table.string('method').notNullable(); // 'Basic Phrase Matching', 'NLP with Compromise.js', 'AI Analysis with DistilBERT'
    table.integer('score').notNullable(); // 0-100 score for this level
    table.integer('confidence').notNullable(); // 0-100 confidence for this level
    table.integer('processing_time_ms').notNullable();
    table.integer('found_patterns'); // Level 1: number of patterns found
    table.integer('total_patterns'); // Level 1: total patterns checked
    table.jsonb('entities'); // Level 2: extracted entities (people, orgs, dates, etc.)
    table.jsonb('privacy_statements'); // Level 2: found privacy statements
    table.jsonb('rights_statements'); // Level 2: found rights statements
    table.jsonb('compliance_gaps'); // Level 3: identified compliance gaps
    table.jsonb('risk_factors'); // Level 3: identified risk factors
    table.jsonb('ai_recommendations'); // Level 3: AI-generated recommendations
    table.jsonb('level_findings'); // Detailed findings specific to this level
    table.text('error_message'); // Error message if level failed
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['check_result_id']);
    table.index(['level']);
    table.index(['score']);
  });

  // Create HIPAA Remediation table for detailed remediation guidance
  await knex.schema.createTable('hipaa_remediation', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('check_result_id')
      .references('id')
      .inTable('hipaa_check_results')
      .onDelete('CASCADE')
      .notNullable();
    table.string('priority').notNullable(); // critical, high, medium, low
    table.string('effort').notNullable(); // minimal, moderate, significant, extensive
    table.jsonb('steps').notNullable(); // Array of remediation steps
    table.jsonb('resources').notNullable(); // Array of helpful resources
    table.string('timeline').notNullable(); // Estimated timeline for remediation
    table.string('estimated_cost'); // Optional cost estimate
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['check_result_id']);
    table.index(['priority']);
    table.index(['effort']);
  });

  // Create HIPAA Evidence table for storing evidence collected during analysis
  await knex.schema.createTable('hipaa_evidence', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('check_result_id')
      .references('id')
      .inTable('hipaa_check_results')
      .onDelete('CASCADE')
      .notNullable();
    table.string('type').notNullable(); // text_excerpt, link, screenshot, metadata
    table.text('content').notNullable(); // The actual evidence content
    table.string('location').notNullable(); // Where the evidence was found
    table.integer('relevance').notNullable(); // 0-100 relevance score
    table.timestamp('evidence_timestamp').defaultTo(knex.fn.now()).notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['check_result_id']);
    table.index(['type']);
    table.index(['relevance']);
  });

  // Create HIPAA Recommendations table for high-level scan recommendations
  await knex.schema.createTable('hipaa_recommendations', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('hipaa_scan_id')
      .references('id')
      .inTable('hipaa_scans')
      .onDelete('CASCADE')
      .notNullable();
    table.string('recommendation_id').notNullable(); // Unique identifier for the recommendation
    table.integer('priority').notNullable(); // 1-10 priority ranking
    table.string('title').notNullable();
    table.text('description').notNullable();
    table.string('category').notNullable(); // presence, accessibility, content_structure, etc.
    table.string('effort').notNullable(); // minimal, moderate, significant, extensive
    table.string('impact').notNullable(); // low, medium, high
    table.string('timeline').notNullable();
    table.jsonb('resources').notNullable(); // Array of helpful resources
    table.jsonb('related_checks').notNullable(); // Array of related check IDs
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['hipaa_scan_id']);
    table.index(['priority']);
    table.index(['category']);
    table.index(['effort']);
    table.index(['impact']);
  });

  // Create HIPAA Content Analysis table for storing content analysis results
  await knex.schema.createTable('hipaa_content_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table
      .uuid('hipaa_scan_id')
      .references('id')
      .inTable('hipaa_scans')
      .onDelete('CASCADE')
      .notNullable();
    table.string('content_url').notNullable();
    table.string('content_type'); // text/html, application/pdf, etc.
    table.string('language'); // en, es, fr, etc.
    table.integer('content_length').notNullable();
    table.integer('sections_found').notNullable();
    table.integer('patterns_matched').notNullable();
    table.integer('entities_extracted').notNullable();
    table.decimal('readability_score', 5, 2); // Flesch reading ease score
    table.decimal('organization_score', 5, 2); // 0-100 organization quality score
    table.boolean('has_proper_headings').defaultTo(false);
    table.jsonb('heading_levels'); // Array of heading levels found
    table.decimal('average_section_length', 8, 2);
    table.jsonb('privacy_policy_links'); // Array of discovered privacy policy links
    table.jsonb('structure_analysis'); // Detailed structure analysis results
    table.string('page_title');
    table.timestamp('last_modified'); // When the content was last modified
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();

    // Indexes for performance
    table.index(['hipaa_scan_id']);
    table.index(['content_url']);
    table.index(['readability_score']);
    table.index(['organization_score']);
  });
}

export async function down(knex: Knex): Promise<void> {
  // Drop tables in reverse order of creation due to foreign key constraints
  await knex.schema.dropTableIfExists('hipaa_content_analysis');
  await knex.schema.dropTableIfExists('hipaa_recommendations');
  await knex.schema.dropTableIfExists('hipaa_evidence');
  await knex.schema.dropTableIfExists('hipaa_remediation');
  await knex.schema.dropTableIfExists('hipaa_analysis_levels');
  await knex.schema.dropTableIfExists('hipaa_findings');
  await knex.schema.dropTableIfExists('hipaa_check_results');
  await knex.schema.dropTableIfExists('hipaa_scans');
}
