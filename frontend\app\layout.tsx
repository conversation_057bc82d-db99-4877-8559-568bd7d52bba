import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './../styles/globals.css'; // Adjusted path
import './../styles/design-system.css'; // Import design system
import { AuthProvider } from '../context/AuthContext'; // Import AuthProvider
import { ThemeProvider } from '@/components/layout/theme-provider'; // Import ThemeProvider
import { ToastProvider } from '@/components/ui/Toast'; // Import ToastProvider
import { ThemeEnforcer } from '@/components/layout/theme-enforcer'; // Import ThemeEnforcer

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Comply Checker',
  description: 'SaaS MVP for compliance scanning',
};

/**
 * Root layout for the application.
 * @param {object} props - The props object.
 * @param {React.ReactNode} props.children - The children to render.
 * @returns {JSX.Element} The root layout component.
 */
export default function RootLayout({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider>
          <ThemeEnforcer>
            <ToastProvider>
              <AuthProvider>{children}</AuthProvider>
            </ToastProvider>
          </ThemeEnforcer>
        </ThemeProvider>
      </body>
    </html>
  );
}
