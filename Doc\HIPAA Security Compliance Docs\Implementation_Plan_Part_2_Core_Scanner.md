# HIPAA Security Compliance Implementation Plan - Part 2: Core Security Scanner

## 🎯 Overview
This is Part 2 of the comprehensive HIPAA Security Compliance implementation plan. This part covers the core security scanner implementation, OWASP ZAP integration, and foundational scanning services.

## 📋 Prerequisites
- ✅ Part 1 completed (Project setup and infrastructure)
- ✅ OWASP ZAP Docker container running
- ✅ Database schema migrated
- ✅ Core types and constants defined

## 🏗️ Phase 2.1: OWASP ZAP Integration Service

### 2.1.1 Create ZAP Client Service

Create `backend/src/compliance/hipaa/security/services/zap-client.ts`:
```typescript
import axios, { AxiosInstance } from 'axios';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export interface ZapScanOptions {
  targetUrl: string;
  maxDepth: number;
  maxPages: number;
  timeout: number;
  includePages?: string[];
  excludePages?: string[];
}

export interface ZapScanResult {
  scanId: string;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  alerts: ZapAlert[];
  spiderResults: ZapSpiderResult[];
}

export interface ZapAlert {
  alertId: string;
  name: string;
  riskLevel: string;
  confidence: string;
  description: string;
  solution: string;
  reference: string;
  instances: ZapAlertInstance[];
}

export interface ZapAlertInstance {
  uri: string;
  method: string;
  param: string;
  evidence: string;
  otherInfo: string;
}

export interface ZapSpiderResult {
  url: string;
  statusCode: number;
  responseTime: number;
  contentLength: number;
  contentType: string;
}

export class ZapClient {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor(zapProxyUrl: string, apiKey?: string) {
    this.baseUrl = zapProxyUrl;
    this.client = axios.create({
      baseURL: `${zapProxyUrl}/JSON`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (apiKey) {
      this.client.defaults.params = { zapapiformat: 'JSON', apikey: apiKey };
    }
  }

  async startSpider(options: ZapScanOptions): Promise<string> {
    try {
      const response = await this.client.get('/spider/action/scan/', {
        params: {
          url: options.targetUrl,
          maxChildren: options.maxPages,
          recurse: true,
          contextName: '',
          subtreeOnly: false,
        },
      });

      if (response.data?.scan) {
        return response.data.scan;
      }
      throw new Error('Failed to start spider scan');
    } catch (error) {
      throw new Error(`ZAP Spider start failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSpiderStatus(scanId: string): Promise<{ status: string; progress: number }> {
    try {
      const response = await this.client.get('/spider/view/status/', {
        params: { scanId },
      });

      return {
        status: response.data?.status || 'unknown',
        progress: parseInt(response.data?.status || '0', 10),
      };
    } catch (error) {
      throw new Error(`Failed to get spider status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSpiderResults(scanId: string): Promise<ZapSpiderResult[]> {
    try {
      const response = await this.client.get('/spider/view/results/', {
        params: { scanId },
      });

      return response.data?.results || [];
    } catch (error) {
      throw new Error(`Failed to get spider results: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async startActiveScan(targetUrl: string): Promise<string> {
    try {
      const response = await this.client.get('/ascan/action/scan/', {
        params: {
          url: targetUrl,
          recurse: true,
          inScopeOnly: false,
          scanPolicyName: '',
          method: '',
          postData: '',
        },
      });

      if (response.data?.scan) {
        return response.data.scan;
      }
      throw new Error('Failed to start active scan');
    } catch (error) {
      throw new Error(`ZAP Active scan start failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getActiveScanStatus(scanId: string): Promise<{ status: string; progress: number }> {
    try {
      const response = await this.client.get('/ascan/view/status/', {
        params: { scanId },
      });

      return {
        status: response.data?.status || 'unknown',
        progress: parseInt(response.data?.status || '0', 10),
      };
    } catch (error) {
      throw new Error(`Failed to get active scan status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getAlerts(baseUrl?: string): Promise<ZapAlert[]> {
    try {
      const response = await this.client.get('/core/view/alerts/', {
        params: baseUrl ? { baseurl: baseUrl } : {},
      });

      return response.data?.alerts || [];
    } catch (error) {
      throw new Error(`Failed to get alerts: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async accessUrl(url: string): Promise<{ statusCode: number; responseHeaders: Record<string, string>; body: string }> {
    try {
      const response = await this.client.get('/core/action/accessUrl/', {
        params: { url },
      });

      // Get the response details
      const messagesResponse = await this.client.get('/core/view/messages/', {
        params: { baseurl: url, start: 0, count: 1 },
      });

      const message = messagesResponse.data?.messages?.[0];
      if (message) {
        return {
          statusCode: parseInt(message.responseHeader?.split(' ')[1] || '0', 10),
          responseHeaders: this.parseHeaders(message.responseHeader || ''),
          body: message.responseBody || '',
        };
      }

      return { statusCode: 0, responseHeaders: {}, body: '' };
    } catch (error) {
      throw new Error(`Failed to access URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseHeaders(headerString: string): Record<string, string> {
    const headers: Record<string, string> = {};
    const lines = headerString.split('\n');
    
    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim().toLowerCase();
        const value = line.substring(colonIndex + 1).trim();
        headers[key] = value;
      }
    }
    
    return headers;
  }

  async shutdown(): Promise<void> {
    try {
      await this.client.get('/core/action/shutdown/');
    } catch (error) {
      // Ignore shutdown errors as ZAP might already be down
    }
  }
}
```

### 2.1.2 Create SSL/TLS Analysis Service

Create `backend/src/compliance/hipaa/security/services/ssl-analyzer.ts`:
```typescript
import * as sslChecker from 'ssl-checker';
import { createConnection } from 'tls';
import { URL } from 'url';

export interface SSLAnalysisResult {
  isValid: boolean;
  daysRemaining: number;
  issuer: string;
  subject: string;
  tlsVersion: string;
  cipherSuite: string;
  keyExchange: string;
  serverSignature: string;
  hipaaCompliant: boolean;
  vulnerabilities: SSLVulnerability[];
  grade: string;
}

export interface SSLVulnerability {
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  remediation: string;
}

export class SSLAnalyzer {
  async analyzeDomain(hostname: string, port: number = 443): Promise<SSLAnalysisResult> {
    try {
      // Basic SSL certificate check
      const certInfo = await sslChecker(hostname, { method: 'GET', port });
      
      // Advanced TLS configuration check
      const tlsInfo = await this.checkTLSConfiguration(hostname, port);
      
      // Vulnerability assessment
      const vulnerabilities = await this.assessVulnerabilities(hostname, port, tlsInfo);
      
      // HIPAA compliance check
      const hipaaCompliant = this.assessHIPAACompliance(certInfo, tlsInfo, vulnerabilities);
      
      // Calculate grade
      const grade = this.calculateSSLGrade(certInfo, tlsInfo, vulnerabilities);

      return {
        isValid: certInfo.valid,
        daysRemaining: certInfo.daysRemaining,
        issuer: certInfo.issuer,
        subject: certInfo.subject,
        tlsVersion: tlsInfo.version,
        cipherSuite: tlsInfo.cipher,
        keyExchange: tlsInfo.keyExchange,
        serverSignature: tlsInfo.serverSignature,
        hipaaCompliant,
        vulnerabilities,
        grade,
      };
    } catch (error) {
      throw new Error(`SSL analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async checkTLSConfiguration(hostname: string, port: number): Promise<{
    version: string;
    cipher: string;
    keyExchange: string;
    serverSignature: string;
    supportedProtocols: string[];
  }> {
    return new Promise((resolve, reject) => {
      const socket = createConnection({
        host: hostname,
        port,
        secureProtocol: 'TLSv1_2_method', // Start with TLS 1.2
      });

      socket.on('secureConnect', () => {
        const cipher = socket.getCipher();
        const protocol = socket.getProtocol();
        const peerCertificate = socket.getPeerCertificate();

        resolve({
          version: protocol || 'unknown',
          cipher: cipher?.name || 'unknown',
          keyExchange: cipher?.version || 'unknown',
          serverSignature: peerCertificate?.fingerprint || 'unknown',
          supportedProtocols: [protocol || 'unknown'],
        });

        socket.destroy();
      });

      socket.on('error', (error) => {
        reject(error);
      });

      socket.setTimeout(10000, () => {
        socket.destroy();
        reject(new Error('TLS connection timeout'));
      });
    });
  }

  private async assessVulnerabilities(
    hostname: string,
    port: number,
    tlsInfo: { version: string; cipher: string }
  ): Promise<SSLVulnerability[]> {
    const vulnerabilities: SSLVulnerability[] = [];

    // Check for weak TLS versions
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) {
      vulnerabilities.push({
        type: 'weak_tls_version',
        severity: 'high',
        description: `Weak TLS version detected: ${tlsInfo.version}`,
        remediation: 'Upgrade to TLS 1.2 or higher',
      });
    }

    // Check for weak ciphers
    const weakCiphers = ['RC4', 'DES', '3DES', 'MD5'];
    if (weakCiphers.some(weak => tlsInfo.cipher.includes(weak))) {
      vulnerabilities.push({
        type: 'weak_cipher',
        severity: 'medium',
        description: `Weak cipher suite detected: ${tlsInfo.cipher}`,
        remediation: 'Configure strong cipher suites (AES-256, ChaCha20)',
      });
    }

    return vulnerabilities;
  }

  private assessHIPAACompliance(
    certInfo: any,
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[]
  ): boolean {
    // HIPAA requires valid certificates
    if (!certInfo.valid) return false;
    
    // Certificate should not expire soon
    if (certInfo.daysRemaining < 30) return false;
    
    // Must use TLS 1.2 or higher
    if (!tlsInfo.version.includes('1.2') && !tlsInfo.version.includes('1.3')) return false;
    
    // No critical or high vulnerabilities
    const criticalVulns = vulnerabilities.filter(v => v.severity === 'critical' || v.severity === 'high');
    if (criticalVulns.length > 0) return false;
    
    // Must use strong encryption
    if (!tlsInfo.cipher.includes('AES') && !tlsInfo.cipher.includes('ChaCha20')) return false;

    return true;
  }

  private calculateSSLGrade(
    certInfo: any,
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[]
  ): string {
    let score = 100;

    // Deduct for invalid certificate
    if (!certInfo.valid) score -= 50;
    
    // Deduct for expiring certificate
    if (certInfo.daysRemaining < 30) score -= 20;
    
    // Deduct for weak TLS version
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) score -= 30;
    
    // Deduct for vulnerabilities
    vulnerabilities.forEach(vuln => {
      switch (vuln.severity) {
        case 'critical': score -= 40; break;
        case 'high': score -= 20; break;
        case 'medium': score -= 10; break;
        case 'low': score -= 5; break;
      }
    });

    // Convert score to grade
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
}
```

### 2.1.3 Create Content Analysis Service

Create `backend/src/compliance/hipaa/security/services/content-analyzer.ts`:
```typescript
import { parse } from 'node-html-parser';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export interface ContentAnalysisResult {
  hasEPHI: boolean;
  ephiMatches: EPHIMatch[];
  securityHeaders: SecurityHeaderResult[];
  formAnalysis: FormAnalysisResult[];
  scriptAnalysis: ScriptAnalysisResult;
  cookieAnalysis: CookieAnalysisResult[];
}

export interface EPHIMatch {
  pattern: string;
  match: string;
  location: string;
  context: string;
  lineNumber: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
}

export interface SecurityHeaderResult {
  header: string;
  present: boolean;
  value?: string;
  secure: boolean;
  recommendation?: string;
}

export interface FormAnalysisResult {
  formId: string;
  action: string;
  method: string;
  hasCSRFProtection: boolean;
  hasSSLAction: boolean;
  sensitiveFields: string[];
  securityIssues: string[];
}

export interface ScriptAnalysisResult {
  externalScripts: string[];
  inlineScripts: number;
  potentialXSSVulns: string[];
  securityIssues: string[];
}

export interface CookieAnalysisResult {
  name: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: string | null;
  domain: string;
  path: string;
  securityIssues: string[];
}

export class ContentAnalyzer {
  analyzeContent(
    html: string,
    url: string,
    responseHeaders: Record<string, string>
  ): ContentAnalysisResult {
    const document = parse(html);
    
    return {
      hasEPHI: this.detectEPHI(html).length > 0,
      ephiMatches: this.detectEPHI(html),
      securityHeaders: this.analyzeSecurityHeaders(responseHeaders),
      formAnalysis: this.analyzeForms(document, url),
      scriptAnalysis: this.analyzeScripts(document),
      cookieAnalysis: this.analyzeCookies(responseHeaders),
    };
  }

  private detectEPHI(content: string): EPHIMatch[] {
    const matches: EPHIMatch[] = [];
    const lines = content.split('\n');

    HIPAA_SECURITY_CONSTANTS.EPHI_PATTERNS.forEach((pattern, index) => {
      lines.forEach((line, lineNumber) => {
        const match = pattern.exec(line);
        if (match) {
          matches.push({
            pattern: pattern.source,
            match: match[0],
            location: `Line ${lineNumber + 1}`,
            context: this.getContext(lines, lineNumber, 2),
            lineNumber: lineNumber + 1,
            riskLevel: this.assessEPHIRisk(match[0]),
          });
        }
      });
    });

    return matches;
  }

  private getContext(lines: string[], lineNumber: number, contextSize: number): string {
    const start = Math.max(0, lineNumber - contextSize);
    const end = Math.min(lines.length, lineNumber + contextSize + 1);
    return lines.slice(start, end).join('\n');
  }

  private assessEPHIRisk(match: string): 'critical' | 'high' | 'medium' | 'low' {
    // SSN patterns are critical
    if (/\d{3}-\d{2}-\d{4}/.test(match)) return 'critical';
    
    // Medical record numbers are high risk
    if (/medical\s+record|mrn/i.test(match)) return 'high';
    
    // Patient IDs are medium risk
    if (/patient\s+id/i.test(match)) return 'medium';
    
    return 'low';
  }

  private analyzeSecurityHeaders(headers: Record<string, string>): SecurityHeaderResult[] {
    const results: SecurityHeaderResult[] = [];

    HIPAA_SECURITY_CONSTANTS.SECURITY_HEADERS.forEach(headerName => {
      const headerValue = headers[headerName];
      const present = !!headerValue;
      
      results.push({
        header: headerName,
        present,
        value: headerValue,
        secure: present ? this.isSecureHeaderValue(headerName, headerValue) : false,
        recommendation: present ? undefined : this.getHeaderRecommendation(headerName),
      });
    });

    return results;
  }

  private isSecureHeaderValue(headerName: string, value: string): boolean {
    switch (headerName) {
      case 'strict-transport-security':
        return value.includes('max-age=') && parseInt(value.match(/max-age=(\d+)/)?.[1] || '0') >= 31536000;
      case 'content-security-policy':
        return !value.includes("'unsafe-inline'") && !value.includes("'unsafe-eval'");
      case 'x-frame-options':
        return value.toLowerCase() === 'deny' || value.toLowerCase() === 'sameorigin';
      case 'x-content-type-options':
        return value.toLowerCase() === 'nosniff';
      case 'x-xss-protection':
        return value === '1; mode=block';
      default:
        return true;
    }
  }

  private getHeaderRecommendation(headerName: string): string {
    const recommendations: Record<string, string> = {
      'strict-transport-security': 'Add HSTS header: Strict-Transport-Security: max-age=31536000; includeSubDomains',
      'content-security-policy': 'Add CSP header to prevent XSS attacks',
      'x-frame-options': 'Add X-Frame-Options: DENY to prevent clickjacking',
      'x-content-type-options': 'Add X-Content-Type-Options: nosniff',
      'x-xss-protection': 'Add X-XSS-Protection: 1; mode=block',
      'referrer-policy': 'Add Referrer-Policy: strict-origin-when-cross-origin',
    };
    
    return recommendations[headerName] || `Add ${headerName} header for security`;
  }

  private analyzeForms(document: any, baseUrl: string): FormAnalysisResult[] {
    const forms = document.querySelectorAll('form');
    const results: FormAnalysisResult[] = [];

    forms.forEach((form: any, index: number) => {
      const action = form.getAttribute('action') || '';
      const method = form.getAttribute('method') || 'GET';
      const formId = form.getAttribute('id') || `form-${index}`;
      
      // Check for CSRF protection
      const csrfToken = form.querySelector('input[name*="csrf"], input[name*="token"]');
      
      // Check if action uses HTTPS
      const hasSSLAction = action.startsWith('https://') || action.startsWith('/') || action === '';
      
      // Find sensitive fields
      const sensitiveFields = this.findSensitiveFields(form);
      
      // Identify security issues
      const securityIssues: string[] = [];
      if (!csrfToken) securityIssues.push('Missing CSRF protection');
      if (!hasSSLAction && action.startsWith('http://')) securityIssues.push('Form submits over HTTP');
      if (method.toUpperCase() === 'GET' && sensitiveFields.length > 0) {
        securityIssues.push('Sensitive data submitted via GET method');
      }

      results.push({
        formId,
        action,
        method: method.toUpperCase(),
        hasCSRFProtection: !!csrfToken,
        hasSSLAction,
        sensitiveFields,
        securityIssues,
      });
    });

    return results;
  }

  private findSensitiveFields(form: any): string[] {
    const sensitivePatterns = [
      'password', 'ssn', 'social', 'credit', 'card', 'cvv', 'pin',
      'medical', 'health', 'patient', 'diagnosis', 'treatment'
    ];
    
    const inputs = form.querySelectorAll('input, textarea, select');
    const sensitiveFields: string[] = [];
    
    inputs.forEach((input: any) => {
      const name = input.getAttribute('name') || '';
      const id = input.getAttribute('id') || '';
      const type = input.getAttribute('type') || '';
      
      if (type === 'password' || 
          sensitivePatterns.some(pattern => 
            name.toLowerCase().includes(pattern) || 
            id.toLowerCase().includes(pattern)
          )) {
        sensitiveFields.push(name || id || type);
      }
    });
    
    return sensitiveFields;
  }

  private analyzeScripts(document: any): ScriptAnalysisResult {
    const scripts = document.querySelectorAll('script');
    const externalScripts: string[] = [];
    let inlineScripts = 0;
    const potentialXSSVulns: string[] = [];
    const securityIssues: string[] = [];

    scripts.forEach((script: any) => {
      const src = script.getAttribute('src');
      
      if (src) {
        externalScripts.push(src);
        
        // Check for insecure external scripts
        if (src.startsWith('http://')) {
          securityIssues.push(`Insecure external script: ${src}`);
        }
      } else {
        inlineScripts++;
        
        // Check for potential XSS vulnerabilities in inline scripts
        const content = script.innerHTML;
        if (content.includes('eval(') || content.includes('innerHTML') || content.includes('document.write')) {
          potentialXSSVulns.push('Potentially dangerous inline script detected');
        }
      }
    });

    return {
      externalScripts,
      inlineScripts,
      potentialXSSVulns,
      securityIssues,
    };
  }

  private analyzeCookies(headers: Record<string, string>): CookieAnalysisResult[] {
    const setCookieHeaders = Object.entries(headers)
      .filter(([key]) => key.toLowerCase() === 'set-cookie')
      .map(([, value]) => value);

    const results: CookieAnalysisResult[] = [];

    setCookieHeaders.forEach(cookieHeader => {
      const cookies = Array.isArray(cookieHeader) ? cookieHeader : [cookieHeader];
      
      cookies.forEach(cookie => {
        const parsed = this.parseCookie(cookie);
        results.push(parsed);
      });
    });

    return results;
  }

  private parseCookie(cookieString: string): CookieAnalysisResult {
    const parts = cookieString.split(';').map(part => part.trim());
    const [nameValue] = parts;
    const [name] = nameValue.split('=');
    
    const attributes = parts.slice(1).reduce((acc, part) => {
      const [key, value] = part.split('=');
      acc[key.toLowerCase()] = value || true;
      return acc;
    }, {} as Record<string, any>);

    const secure = 'secure' in attributes;
    const httpOnly = 'httponly' in attributes;
    const sameSite = attributes.samesite || null;
    const domain = attributes.domain || '';
    const path = attributes.path || '/';

    const securityIssues: string[] = [];
    if (!secure) securityIssues.push('Cookie not marked as Secure');
    if (!httpOnly) securityIssues.push('Cookie not marked as HttpOnly');
    if (!sameSite) securityIssues.push('Cookie missing SameSite attribute');

    return {
      name,
      secure,
      httpOnly,
      sameSite,
      domain,
      path,
      securityIssues,
    };
  }
}
```

## ✅ Part 2 Completion Checklist

- [ ] ZAP Client service implemented with full API integration
- [ ] SSL/TLS Analyzer service created with vulnerability assessment
- [ ] Content Analyzer service built with ePHI detection
- [ ] All services include comprehensive error handling
- [ ] TypeScript interfaces strictly typed (no `any[]` usage)
- [ ] Security considerations built into all components

## 🔄 Next Steps

Once Part 2 is complete, proceed to:
- **Part 3**: HIPAA Test Modules Development
- **Part 4**: Frontend Integration and Results Display
- **Part 5**: CI/CD Integration and Deployment

## 🚨 Critical Implementation Notes

1. **Error Handling**: All services include comprehensive try-catch blocks
2. **Type Safety**: Strict TypeScript typing throughout
3. **Security**: No sensitive data logged or exposed
4. **Performance**: Timeouts and resource limits implemented
5. **Modularity**: Each service is independent and testable
