// backend/src/compliance/wcag/page-title-check.spec.ts
import { checkPageTitlePresence } from './page-title-check';
import { WcagCheckResult, PageTitleCheckDetails } from './types';

// Mock global fetch unconditionally
global.fetch = jest.fn();

describe('checkPageTitlePresence', () => {
  const targetUrl = 'https://example.com/wcag-test';

  beforeEach(() => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockClear();
  });

  it('should pass if a valid, non-empty title is present', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><head><title>Valid Page Title</title></head><body></body></html>',
    } as Response);
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(true);
    expect(result.name).toBe('Page Title Presence');
    expect(result.description).toBe('Page has a title: "Valid Page Title".');
    expect((result.details as PageTitleCheckDetails).foundTitle).toBe('Valid Page Title');
  });

  it('should fail if no title tag is present', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><head></head><body></body></html>',
    } as Response);
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toBe('Page is missing a <title> tag.');
    expect((result.details as PageTitleCheckDetails).foundTitle).toBeNull();
  });

  it('should fail if the title tag is empty', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><head><title></title></head><body></body></html>',
    } as Response);
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toBe('Page has an empty <title> tag.');
    expect((result.details as PageTitleCheckDetails).foundTitle).toBe('');
  });

  it('should fail if the title tag contains only whitespace', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><head><title>   </title></head><body></body></html>',
    } as Response);
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toBe('Page has an empty <title> tag.'); // trim() makes whitespace-only effectively empty
    expect((result.details as PageTitleCheckDetails).foundTitle).toBe('');
  });

  it('should handle fetch errors gracefully', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockRejectedValueOnce(
      new Error('Network Error'),
    );
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toContain('Failed to retrieve page content');
    expect(result.description).toContain('Network Error');
    expect((result.details as PageTitleCheckDetails).foundTitle).toBeNull();
  });

  it('should handle non-ok HTTP responses gracefully', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
      text: async () => 'Page not found',
    } as Response);
    const result: WcagCheckResult = await checkPageTitlePresence(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toContain('Failed to retrieve page content');
    expect(result.description).toContain('Status: 404 Not Found');
    expect((result.details as PageTitleCheckDetails).foundTitle).toBeNull();
  });
});
