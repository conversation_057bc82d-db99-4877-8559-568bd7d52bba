<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Automated WCAG Compliance Testing and Best Coding Practices: A Comprehensive Analysis

Web accessibility ensures digital content is usable by people with disabilities. The Web Content Accessibility Guidelines (WCAG) provide a structured framework for developers to create accessible websites. This report examines automated tools for checking WCAG compliance and outlines best coding practices to ensure accessibility standards are met throughout the development lifecycle.

## Understanding WCAG Compliance and Testability

WCAG was developed as a cooperatively created standard for HTML coding best practices, designed to meet accessibility needs across various disabilities[^7]. One of the primary goals of WCAG 2.0 was testability-ensuring compliance could be verified either through machine testing or reliable human evaluation[^2]. However, research shows that testability presents significant challenges.

In an educational experiment, only 8 out of 25 level-A success criteria could be considered reliably human testable by novice evaluators[^2]. This highlights the critical role automated testing plays in accessibility validation, while also underscoring its limitations. The evolution from WCAG 2.0 to 2.1 has introduced additional success criteria addressing mobile accessibility, cognitive disabilities, and low vision requirements, necessitating more sophisticated testing approaches[^10].

Accessibility benefits extend beyond compliance, providing advantages to:

- People with blindness and low vision
- Users with color-blindness
- People with hearing impairments
- Individuals with motor impairments
- Users with cognitive impairments
- Older adults with changing abilities due to aging[^7]

## Types of Automated Testing Tools for WCAG Compliance

Multiple categories of automated accessibility testing tools exist, each with unique capabilities and implementation methods:

### JavaScript-Based APIs and Libraries

The Axe JavaScript Accessibility API exemplifies this category, offering a comprehensive solution for analyzing web content. It returns a JSON object listing accessibility violations with detailed context[^3]. Key benefits include:

- Browser compatibility across modern browsers
- Integration with existing testing infrastructure
- Local execution without third-party server dependencies
- Multi-level iframe testing capabilities
- Detailed reporting of both violations and passing elements[^3]

The API implementation follows a straightforward process:

1. Loading the page in a testing system
2. Optionally configuring JavaScript API parameters
3. Calling the analyze function
4. Either asserting against results or saving them for later processing
5. Repeating for any non-rendered content after making it visible[^3]

### Command-Line and Node.js Tools

Pa11y represents another approach, functioning as a command-line or Node.js-based automated accessibility testing tool[^4]. Its versatility allows implementation through simple terminal commands:

```
pa11y https://example.com
```

Or through JavaScript integration:

```javascript
const pa11y = require('pa11y');
pa11y('https://example.com').then((results) => {
  // Use the results
});
```

Pa11y returns structured results containing information about:

- Page URL and document title
- Detailed issue listings with code references
- Context information showing the problematic HTML
- Descriptive messages explaining the violations
- Element selectors for precise identification
- Issue type categorization and severity codes[^4]

### Web-Based Evaluation Tools

Research has employed web-based tools like Access Monitor and AChecker to evaluate website accessibility against WCAG 2.0 and 2.1 guidelines[^1]. These platforms typically offer:

- User-friendly interfaces for non-technical stakeholders
- Comprehensive reporting capabilities
- Issue prioritization based on severity
- Remediation suggestions for identified problems

### Continuous Integration Tools

For development teams implementing accessibility throughout their workflow, GitLab CI/CD and similar platforms enable automated accessibility testing as part of the build process[^5]. This approach allows teams to:

- Identify accessibility issues before deployment
- Prevent regressions in accessibility compliance
- Track accessibility metrics over time
- Establish quality gates based on accessibility standards

### Unit Testing Frameworks for Accessibility

Specialized unit testing approaches maintain accessibility features throughout development iterations. Tools like Jest and Puppeteer can create automated tests that verify web component accessibility[^6]. The benefits include:

- Prevention of accessibility regressions
- Integration with existing unit testing workflows
- Simulation of keyboard and screen reader interactions
- Consistent validation across development cycles

## What Can Be Automatically Tested for WCAG Compliance

Automated tools excel at checking specific aspects of WCAG compliance but have inherent limitations. Understanding this distinction is crucial for implementing effective testing strategies.

### Automatically Testable Criteria

Based on the research, the following aspects of web accessibility can typically be evaluated using automated tools:

#### Text Alternatives

- Verification that images have alt text
- Detection of non-text content lacking text equivalents[^7]
- Identification of images within links missing descriptive text[^4]

For example, Pa11y can detect issues like:

```
Img element is the only content of the link, but is missing alt text.
The alt text should describe the purpose of the link.
```

#### Structural Semantics

- Validation of correct content structure markup[^7]
- Detection of semantically inappropriate element usage[^8]
- Verification of heading hierarchy and document structure

#### Color and Contrast

- Analysis of text and background color combinations
- Verification of sufficient contrast ratios
- Detection of information conveyed solely through color

#### Programmatically Determined Structure

- Checking that information and structure can be programmatically determined
- Verifying markup is used correctly rather than for presentation purposes
- Ensuring interactive elements are properly identified[^7]

#### Text Resizing Capabilities

- Testing if text can be resized to 200% without loss of content or functionality[^7]

#### Keyboard Navigation

- Detection of keyboard traps
- Verification that all interactive elements are keyboard accessible
- Checking for proper focus management and indicators[^7]

### Limitations of Automated Testing

Despite their utility, automated tools have significant constraints:

#### Subjective Evaluation Requirements

Many accessibility criteria require human judgment and contextual understanding that automated tools cannot provide[^2]. These include:

- Appropriateness of alt text descriptions
- Meaningful sequence of content
- Purpose of custom controls and widgets
- Clarity of error messages and instructions

#### Context Sensitivity Issues

Automated tools often struggle with:

- Understanding the context in which elements are used
- Determining if semantic elements are employed appropriately[^8]
- Interpreting the purpose of custom interactions

#### Dynamic Content and Complex Interactions

Current automated tools have limitations when evaluating:

- State changes in dynamic applications
- Custom widgets and complex user interfaces
- Screen reader compatibility with custom components

## Best Coding Practices for WCAG Compliance

Implementing accessibility from the beginning of development is more efficient than retrofitting existing code. The following practices help ensure WCAG compliance:

### Semantic HTML as the Foundation

Structural, semantic HTML forms the cornerstone of accessibility practice[^8]. When screen readers or assistive devices scan a page, they interpret the Document Object Model (DOM) structure, not styles or JavaScript[^8]. Key principles include:

#### Use Appropriate HTML Elements

- Employ elements for their intended semantic purpose
- Choose headers (`<h1>` through `<h6>`) for proper document structure
- Implement lists (`<ul>`, `<ol>`) for grouped content
- Use tables (`<table>`) only for tabular data
- Utilize HTML5 semantic elements like `<main>`, `<nav>`, `<article>`, and `<section>`

#### Avoid Non-Semantic Elements for Interactive Components

"Never use a div or span for a button when you could use a semantically meaningful button element."[^8] This principle applies to all interactive elements where semantic alternatives exist.

#### Create Logical Document Structure

Semantic structure allows assistive technologies to provide improved navigation options. Many screen readers offer functionality that enables users to navigate by headings or links, making properly structured content essential[^8].

### Implementing ARIA When HTML Semantics Are Insufficient

ARIA (Accessible Rich Internet Applications) provides additional accessibility semantics when HTML alone is insufficient[^9]:

#### ARIA Landmarks for Page Structure

Properly implemented ARIA landmarks help assistive technology users understand page layout and navigate efficiently between sections[^9]. This includes roles such as:

- `banner` for header content
- `navigation` for navigation menus
- `main` for primary content
- `complementary` for supporting content

#### Providing Accessible Names and Descriptions

"Providing elements with accessible names and, where appropriate, accessible descriptions is one of the most important responsibilities authors have when developing accessible web experiences."[^9] This ensures users of assistive technology understand the purpose of each element.

#### Implementing Proper Roles, States, and Properties

For custom widgets and interactive elements, ARIA roles, states, and properties communicate purpose and current state to assistive technologies:

- Roles define what an element is or does
- States describe current conditions (expanded, selected, disabled)
- Properties provide additional information about features and relationships

### Forms and Interactive Content Accessibility

Forms present unique accessibility challenges that require specific implementation techniques:

#### Labeling and Instructions

- Ensure all form components have descriptive labels[^7]
- Use properly associated `<label>` elements with form controls
- Provide clear instructions for completing fields
- Group related form elements using `<fieldset>` and `<legend>`

#### Keyboard Accessibility

- Verify all interactive elements are keyboard operable
- Implement logical tab order following visual layout
- Avoid keyboard traps where focus cannot move away from an element[^7]
- Provide visible focus indicators for all interactive elements

#### Error Handling

- Display clear error messages when validation fails
- Programmatically associate error messages with corresponding form controls
- Provide suggestions for correction when possible

### Visual Design Considerations for Accessibility

Visual design significantly impacts accessibility for users with visual impairments:

#### Color and Contrast Requirements

- Maintain sufficient contrast between text and background
- Never rely solely on color to convey meaning or distinguish elements
- Provide additional indicators beyond color for important information

#### Text Sizing and Spacing

- Design layouts that accommodate text resizing up to 200% without loss of functionality[^7]
- Ensure adequate spacing between lines and paragraphs
- Allow content to reflow rather than requiring horizontal scrolling

#### Responsive and Adaptable Design

- Create layouts that adapt to different screen sizes and zoom levels
- Ensure content maintains usability across different display configurations

### Unit Testing for Accessibility Maintenance

Implementing accessibility-focused unit tests prevents regression of accessibility features over time:

#### Automated Accessibility Unit Testing

Unit tests can verify accessibility properties remain intact despite code changes. This approach helps prevent situations where "an inexperienced developer accidentally removes some of the accessibility features of your website"[^6].

#### Integration into Development Workflow

Incorporating accessibility testing into continuous integration ensures:

- Tests run every time component code changes
- Accessibility barriers are caught before code is merged
- Teams maintain accessibility standards throughout development[^6]

## Implementation Strategies for WCAG Compliance

For effective implementation of accessibility standards:

### Continuous Integration and Delivery

Incorporating accessibility testing into CI/CD pipelines helps maintain standards throughout development:

- Integrate tools like Pa11y, Axe, or custom testing scripts into automated build processes
- Establish accessibility thresholds that must be met before code deployment
- Generate regular reports on accessibility compliance status
- Track improvements and regressions over time

### Balanced Testing Approach

A comprehensive accessibility strategy combines automated and manual testing:

#### Initial Screening with Automated Tools

Automated tools provide efficient first-pass testing to identify obvious issues across:

- Missing alt text
- Color contrast problems
- Keyboard accessibility issues
- Improper heading structures
- Missing form labels

#### Follow-up Manual Testing

Human evaluation addresses aspects where automated tools fall short:

- Appropriateness of alternative text
- Meaningful content sequence
- Logical reading order
- Screen reader compatibility
- User experience with assistive technologies

### Developer Education and Resources

Building team knowledge is crucial for sustainable accessibility implementation:

- Provide focused accessibility training for developers
- Create clear guidelines and checklists for common scenarios
- Maintain internal accessibility documentation and resources
- Foster a culture that prioritizes inclusive design principles

## Conclusion

Automated tools play a crucial role in checking WCAG compliance but must be part of a broader accessibility strategy that includes manual testing and best coding practices. The combination of semantic HTML, appropriate ARIA implementation, and thorough testing procedures creates more inclusive digital experiences.

As web technologies evolve, so must accessibility testing approaches. The transition from WCAG 2.0 to 2.1 has already expanded requirements, and future versions will likely address emerging technologies. Organizations that integrate accessibility throughout their development lifecycle not only meet compliance standards but create better experiences for all users.

While automated tools continue to advance in their capabilities, the human element of accessibility evaluation remains essential. Many aspects of accessible design require contextual understanding and user empathy that machines cannot yet provide. A balanced approach combining technological solutions with human expertise represents the most effective path to truly accessible web experiences.

<div style="text-align: center">⁂</div>

[^1]: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC9521853/

[^2]: https://www.semanticscholar.org/paper/3d54d7a98f759c96e09d0d764e0e0e5cb1727cc6

[^3]: https://www.deque.com/axe/core-documentation/api-documentation/

[^4]: https://github.com/pa11y/pa11y

[^5]: https://docs.gitlab.com/ee/ci/testing/accessibility_testing.html

[^6]: https://knowbility.org/programs/accessu-2024/the-case-for-accessible-unit-testing

[^7]: https://www.accessibilityoz.com/factsheets/coding/coding-accessibility-principles/

[^8]: http://web-accessibility.carnegiemuseums.org/foundations/semantic/

[^9]: https://www.w3.org/WAI/ARIA/apg/

[^10]: https://www.semanticscholar.org/paper/8990daf854c369022cc2ef55230b1daabce9361b

[^11]: https://www.semanticscholar.org/paper/dbab8f4884d32bffb169fed0029cd99a925ebe06

[^12]: https://github.com/dequelabs/axe-core

[^13]: https://accessibility.deque.com/role-of-automated-accessibility-in-cicd

[^14]: https://www.semanticscholar.org/paper/9d2be2b151c1e6bd64e4a89175016dfd7c508a50

[^15]: https://opensource.com/article/23/2/automated-accessibility-testing

[^16]: https://www.semanticscholar.org/paper/40e25ff2baf1be5a6a6fd26d75272ba72787f45c

[^17]: https://www.section508.gov/develop/incorporating-accessibility-conformance/

[^18]: https://www.semanticscholar.org/paper/83766c92cac14be38953e34af4ac741784bf2bba

[^19]: https://testguild.com/accessibility-testing-tools-automation/

[^20]: https://wave.webaim.org

[^21]: https://www.semanticscholar.org/paper/2779ffd36a8e1152eddd54b9145560e6c26258f1

[^22]: https://www.semanticscholar.org/paper/688c8b3fea0618350d03c319080db8d095278a9c

[^23]: https://www.semanticscholar.org/paper/2a5c1b98dc5cece3d5f6c02297b48631442a80c7

[^24]: https://www.semanticscholar.org/paper/c3d4b02dfced2965e56e3219ab6805e006ed8a04

[^25]: https://arxiv.org/pdf/2006.14245.pdf

[^26]: https://www.browserstack.com/guide/automate-accessibility-testing

[^27]: https://www.deque.com/axe/

[^28]: https://usabilitygeek.com/10-free-web-based-web-site-accessibility-evaluation-tools/

[^29]: https://www.mabl.com/accessibility-testing

[^30]: https://accessibe.com/accessscan

[^31]: https://ckeditor.com/blog/automated-accessibility-testing/

[^32]: https://www.browserstack.com/guide/wcag-ada-testing-tools

[^33]: https://www.semanticscholar.org/paper/086f748d2c92e912793a6f1c2527abe6788502f7

[^34]: https://www.semanticscholar.org/paper/dbff10523416c970e2e8c4a8d05d3f5b7c9ba73f

[^35]: https://pubmed.ncbi.nlm.nih.gov/36368325/

[^36]: https://www.semanticscholar.org/paper/569a276e1ab7acc0163418a829dfbf40281bb624

[^37]: https://arxiv.org/ftp/arxiv/papers/1503/1503.05493.pdf

[^38]: https://arxiv.org/pdf/2105.12928.pdf

[^39]: https://arxiv.org/ftp/arxiv/papers/1907/1907.02906.pdf

[^40]: https://arxiv.org/html/2403.17479v1

[^41]: https://www.w3.org/WAI/WCAG21/Understanding/understanding-act-rules

[^42]: https://www.w3.org/WAI/WCAG21/Understanding/understanding-techniques

[^43]: https://www.boia.org/blog/what-programmatically-determined-means-for-accessibility

[^44]: https://webaim.org/blog/wcag-2-testability/

[^45]: https://www.semanticscholar.org/paper/1237f967286baf8323a84e585d2a5e949cd7cbb3

[^46]: https://www.semanticscholar.org/paper/59988fe50a6bb760eabfbb1181753081253c9398

[^47]: https://arxiv.org/abs/2308.15152

[^48]: https://www.semanticscholar.org/paper/76f5d5f7c56947fe15a940e31b31298e7e9d2dc9

[^49]: https://web.dev/articles/lighthouse-ci

[^50]: https://axe-api.com

[^51]: https://github.com/pa11y/pa11y-ci

[^52]: https://github.com/GoogleChrome/lighthouse

[^53]: https://www.semanticscholar.org/paper/5c81c438967a0706ff7c98c8d0169490047db6ad

[^54]: https://www.semanticscholar.org/paper/2a6656caebe71c6dddf6c0eb5a418f06504daf3c

[^55]: https://www.semanticscholar.org/paper/8b00d0e17c5472a288066eeac4da4c6770a2b3c1

[^56]: https://www.semanticscholar.org/paper/736edbc23f2faa02a7bb392be9eeb13a4fb0c11d

[^57]: https://arxiv.org/pdf/2501.06424.pdf

[^58]: http://arxiv.org/pdf/1905.01825.pdf

[^59]: https://arxiv.org/pdf/1811.04122.pdf

[^60]: https://arxiv.org/html/2503.21947v1

[^61]: https://rtcamp.com/handbook/developing-for-block-editor-and-site-editor/accessibility-best-practices/

[^62]: https://www.tpgi.com/how-to-efficiently-include-accessibility-testing-through-continuous-integration/

[^63]: https://blog.magicpod.com/automating-accessibility-testing-in-your-ci/cd-pipelines-with-axe

[^64]: https://www.testingxperts.com/blog/accessibility-testing/

[^65]: https://www.youtube.com/watch?v=bn1XJSjc_qM

[^66]: https://www.youtube.com/watch?v=95yAXh7KBjo

[^67]: https://www.audioeye.com/post/automated-accessibility-testing-tools/

[^68]: https://github.com/w3c/wai-eval-tools

[^69]: https://chromewebstore.google.com/detail/browserstack-accessibilit/fmkhjeeeojocenbconhndpiohohajokn

[^70]: https://www.semanticscholar.org/paper/da72c73f1d5c8d5a0ee7687ae1c0e2371a474a52

[^71]: https://www.semanticscholar.org/paper/3d54d7a98f759c96e09d0d764e0e0e5cb1727cc6

[^72]: https://www.semanticscholar.org/paper/fdeb0f8d4fae16892dff09d5e4a10f8008630eac

[^73]: https://www.semanticscholar.org/paper/71b2ab5bd8e9960f7f7b53d88b0cd1ccd5c4d2c8

[^74]: https://www.w3.org/WAI/WCAG21/Understanding/status-messages.html

[^75]: https://www.w3.org/WAI/WCAG22/quickref/

[^76]: https://www.whoisaccessible.com/guidelines/wcag/

[^77]: https://www.w3.org/WAI/WCAG21/Understanding/info-and-relationships.html

[^78]: https://accessibleweb.com/question-answer/how-many-wcag-success-criteria-are-there/

[^79]: https://accessibleculture.org/articles/2011/01/programmatically-determined/

[^80]: https://www.semanticscholar.org/paper/e815fccedd6e08be669dc7ae4041d1536bad8060

[^81]: https://www.semanticscholar.org/paper/5f88530aaf9203f006e9d61edcc8a6d439c58a95

[^82]: https://www.semanticscholar.org/paper/568320b6c83beb1de9968d29ebcd08883575d113

[^83]: https://www.semanticscholar.org/paper/c51ea3693caba0bf1181580f4a8a9329ec4b6226

[^84]: https://arxiv.org/pdf/2103.08778.pdf

[^85]: https://arxiv.org/html/2501.03572

[^86]: https://arxiv.org/pdf/2501.16601.pdf

[^87]: https://tetralogical.com/blog/2024/08/09/design-patterns-wcag/

[^88]: https://developer.mozilla.org/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG

[^89]: https://accessiblyapp.com/blog/semantic-html/

[^90]: https://www.w3.org/TR/WCAG20-TECHS/aria

[^91]: https://top5accessibility.com/blog/mastering-aria-attributes-clearer-coding-for-better-web-accessibility/

[^92]: https://blog.usablenet.com/5-wcag-best-practices-for-maintaining-web-accessibility

[^93]: https://www.semanticscholar.org/paper/84152e60f4c3e60916d6a70c1c8501d91081f41b

[^94]: https://www.semanticscholar.org/paper/dbab8f4884d32bffb169fed0029cd99a925ebe06

[^95]: https://www.semanticscholar.org/paper/6b13e514bd26f8cb6d79c91c49de9892289fc890

[^96]: https://www.semanticscholar.org/paper/c38ddfe61187f506720b9fcb51441ecdc14bca1c

[^97]: https://arxiv.org/abs/2304.12671

[^98]: https://ialabs.ie/what-is-the-difference-between-wcag-a-aa-and-aaa/

[^99]: https://tetralogical.com/blog/2021/11/26/understanding-wcag-level-aaa/

[^100]: https://accessibility.huit.harvard.edu/auto-tools-testing

[^101]: https://www.semanticscholar.org/paper/d1e35fe6e1438819f0bc985cbfb6ae7fe3e01f48

[^102]: https://www.semanticscholar.org/paper/4fd145bbfebe16950976e411ee99e6e3ba8d3ab7

[^103]: https://pubmed.ncbi.nlm.nih.gov/22902168/

[^104]: https://www.semanticscholar.org/paper/a7be844ffaacc8f1f8d439abd6f9ca7bc7036286

[^105]: https://www.w3.org/WAI/standards-guidelines/aria/

[^106]: https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/Accessibility/WAI-ARIA_basics

[^107]: https://www.boia.org/blog/5-tips-for-using-aria-to-improve-web-accessibility

[^108]: https://www.accessibility-developer-guide.com/knowledge/aria/bad-practices/

[^109]: https://www.semanticscholar.org/paper/80d5bec2e7c22d648d4f2d1732106d94e84ebacd

[^110]: https://arxiv.org/pdf/2107.06799.pdf

[^111]: http://arxiv.org/pdf/2304.07591.pdf

[^112]: https://arxiv.org/pdf/2401.16450.pdf

[^113]: https://arxiv.org/abs/2203.07201

[^114]: http://arxiv.org/pdf/2309.10167.pdf

[^115]: https://www.w3.org/WAI/test-evaluate/tools/list/

[^116]: https://www.browserstack.com/guide/accessibility-automation-tools

[^117]: https://top5accessibility.com/top-5-accessibility-audit-solutions/

[^118]: https://www.semanticscholar.org/paper/907308f9e6d75e2f5a3aa45eab47c86bacee8825

[^119]: https://www.semanticscholar.org/paper/a2e02c59757d5c2eed74280dd98946a4b266fa32

[^120]: https://www.semanticscholar.org/paper/d43098cd5b4637b2007193593fa09e5716dc98be

[^121]: https://arxiv.org/pdf/2312.02992.pdf

[^122]: https://arxiv.org/pdf/2306.10039.pdf

[^123]: http://arxiv.org/pdf/2401.10357.pdf

[^124]: https://www.w3.org/TR/WCAG21/

[^125]: https://www.w3.org/TR/UNDERSTANDING-WCAG20/conformance.html

[^126]: https://www.levelaccess.com/blog/wcag-2-2-aa-summary-and-checklist-for-website-owners/

[^127]: https://www.davidmacd.com/blog/what-are-WCAG-success-criteria.html

[^128]: https://pressbooks.library.torontomu.ca/iwacc/chapter/wcag-guidelines-and-success-criteria/

[^129]: https://w3c.github.io/wcag21/understanding/conformance

[^130]: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4547611/

[^131]: https://arxiv.org/abs/2403.01216

[^132]: https://www.semanticscholar.org/paper/ff019bf00fbfbccffba519bb41cabf6bcd6b50f3

[^133]: https://www.semanticscholar.org/paper/de19a1bdffe42894dabd1139bd5e071cae0873b9

[^134]: https://www.semanticscholar.org/paper/e16b23311a9d1b5fa3a8c737c76b4998b98b76e0

[^135]: https://arxiv.org/abs/2409.03797

[^136]: https://www.deque.com/axe/core-documentation/

[^137]: https://github.com/dequelabs/axe-core/blob/develop/doc/developer-guide.md

[^138]: https://www.npmjs.com/package/@axe-core/react

[^139]: https://www.semanticscholar.org/paper/646675e934f69d64961c4ccba0ac54daec2b4fdb

[^140]: https://www.semanticscholar.org/paper/c045decf85ce5ccf65fd80571f4a3b58fc4d2e38

[^141]: https://www.semanticscholar.org/paper/13601a74e3a23c23abb5fa792323da8473693c31

[^142]: https://www.semanticscholar.org/paper/284c05a789fa5da4b4d04d2f09c3eb2c0b85d152

[^143]: https://www.semanticscholar.org/paper/13f3d5487817df0cf4ed300dbe21ce338c508293

[^144]: https://www.semanticscholar.org/paper/7212768308e089abc6ac13723a81981c0810adf4

[^145]: https://arxiv.org/pdf/1805.04473.pdf

[^146]: https://arxiv.org/pdf/2110.14097.pdf

[^147]: https://arxiv.org/pdf/2211.07782.pdf

[^148]: http://arxiv.org/pdf/2401.17606.pdf

[^149]: https://arxiv.org/pdf/2102.06666.pdf

[^150]: https://arxiv.org/pdf/2410.00623.pdf

[^151]: https://sdettech.com/accessibility-testing-in-continuous-integration-and-delivery-ci-cd-pipelines/

[^152]: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC9521853/

[^153]: https://www.semanticscholar.org/paper/8990daf854c369022cc2ef55230b1daabce9361b

[^154]: https://www.semanticscholar.org/paper/9d2be2b151c1e6bd64e4a89175016dfd7c508a50

[^155]: https://www.semanticscholar.org/paper/40e25ff2baf1be5a6a6fd26d75272ba72787f45c

[^156]: https://www.semanticscholar.org/paper/83766c92cac14be38953e34af4ac741784bf2bba

[^157]: https://testguild.com/accessibility-testing-tools-automation/

[^158]: https://wave.webaim.org

[^159]: https://www.lambdatest.com/blog/automated-accessibilty-testing-tools/

[^160]: https://www.semanticscholar.org/paper/1306c666936364066aae2ab82dd69677d88dd01e

[^161]: https://www.semanticscholar.org/paper/6908bbab60e99d477b72ebbaf4003bd71a251dc3

[^162]: https://www.w3.org/WAI/WCAG21/Understanding/conformance

[^163]: https://www.levelaccess.com/blog/wcag-2-1-exploring-new-success-criteria/

[^164]: https://www.w3.org/WAI/standards-guidelines/wcag/new-in-21/

[^165]: https://www.w3.org/WAI/GL/wiki/WCAG_2.1_Success_Criteria

[^166]: https://techblog.topdesk.com/accessibility/success-criteria-of-wcag-2-1-summarized/

[^167]: https://www.semanticscholar.org/paper/685c7ce4495e6ed87915e9c5985ee98b0af8a427

[^168]: https://www.semanticscholar.org/paper/9f1d5659df807799ef0c447fce2cff647b797888

[^169]: https://arxiv.org/abs/2312.02992

[^170]: https://pubmed.ncbi.nlm.nih.gov/38451234/

[^171]: https://arxiv.org/abs/2405.18232

[^172]: https://www.semanticscholar.org/paper/12be354db627726d40186f50ee0f742f3e8d633c

[^173]: https://arxiv.org/pdf/2305.00695.pdf

[^174]: https://arxiv.org/pdf/2401.00451.pdf

[^175]: https://www.w3.org/WAI/standards-guidelines/wcag/

[^176]: https://www.wcag.com/developers/

[^177]: https://www.freecodecamp.org/news/web-accessibility-best-practices/

[^178]: https://www.semanticscholar.org/paper/c464dbaf62c5d73c82b104e967bfe60290c6c3f4

[^179]: http://arxiv.org/pdf/1710.07899.pdf

[^180]: https://www.w3.org/WAI/WCAG21/Understanding/understanding-act-rules.html

[^181]: https://www.webyes.com/blogs/can-automated-tools-make-website-fully-wcag-compliant/

[^182]: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC11783340/

[^183]: https://www.semanticscholar.org/paper/b748590dc23bb3cc7135bbefb82a2fb6af8a361e

[^184]: https://www.semanticscholar.org/paper/7bc17220dd5c736e9b3720e0911aa9988989c36c

[^185]: https://www.semanticscholar.org/paper/ede22560329fa7132666a1d7aa3494f5f577b8a2

[^186]: https://www.semanticscholar.org/paper/d9cc9f75302613c0454424678bed3d303fee20c9

[^187]: https://arxiv.org/pdf/2412.16256.pdf

[^188]: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA

[^189]: https://wai-aria-practices.netlify.app/aria-practices/

[^190]: https://www.accessibilitychecker.org/blog/aria-accessibility/

[^191]: https://webflow.com/blog/how-to-use-aria

[^192]: https://contextqa.com/useful-resource/fix-web-accessibility-issues/

[^193]: https://www.csun.edu/universal-design-center/aria-best-practices

[^194]: https://digitalaccessibility.uchicago.edu/training/training-into-to-aria/
