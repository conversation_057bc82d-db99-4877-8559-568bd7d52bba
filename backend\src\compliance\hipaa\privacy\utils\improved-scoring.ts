// backend/src/compliance/hipaa/privacy/utils/improved-scoring.ts

import { HipaaCheckResult } from '../types';
import { CHECK_WEIGHTS, INDUSTRY_THRESHOLDS } from '../constants';

/**
 * Improved scoring system for HIPAA compliance
 * Addresses mathematical inconsistencies and implements proper weighting
 */

export interface ImprovedScoringOptions {
  industryType?: 'healthcare' | 'general';
  confidenceWeighting?: boolean;
  debugLogging?: boolean;
}

export interface ScoringResult {
  overallScore: number;
  weightedScore: number;
  complianceLevel: 'compliant' | 'mostly_compliant' | 'partially_compliant' | 'non_compliant';
  passed: boolean;
  confidence: number;
  breakdown: {
    checkId: string;
    name: string;
    score: number | null;
    weight: number;
    contribution: number;
    passed: boolean;
    confidence: number;
  }[];
  debugInfo?: {
    industryType: string;
    thresholds: Record<string, number>;
    confidenceWeighting: boolean;
    calculations: {
      totalWeightedScore: number;
      totalWeight: number;
      totalConfidence: number;
      confidenceCount: number;
      averageConfidence: number;
    };
  };
}

/**
 * Calculate improved overall HIPAA compliance score
 */
export function calculateImprovedOverallScore(
  checks: HipaaCheckResult[],
  options: ImprovedScoringOptions = {},
): ScoringResult {
  const { industryType = 'general', confidenceWeighting = false, debugLogging = false } = options;

  const thresholds = INDUSTRY_THRESHOLDS[industryType];
  const breakdown: ScoringResult['breakdown'] = [];

  let totalWeightedScore = 0;
  let totalWeight = 0;
  let totalConfidence = 0;
  let confidenceCount = 0;

  // Process each check with proper weighting
  checks.forEach((check) => {
    const weight = CHECK_WEIGHTS[check.checkId as keyof typeof CHECK_WEIGHTS] || 0;

    // Handle different score scenarios
    let score = 0;
    if (check.overallScore !== null && check.overallScore !== undefined) {
      score = check.overallScore;
    } else if (check.passed) {
      // If no score but passed, use confidence as score
      score = check.confidence || 100;
    } else {
      // If failed and no score, use 0
      score = 0;
    }

    const confidence = check.confidence || 50;

    // Apply confidence weighting if enabled
    const adjustedScore = confidenceWeighting
      ? (score * confidence) / 100 + 50 * (1 - confidence / 100)
      : score;

    const contribution = adjustedScore * weight;

    breakdown.push({
      checkId: check.checkId,
      name: check.name,
      score: check.overallScore ?? null, // Convert undefined to null
      weight,
      contribution,
      passed: check.passed,
      confidence,
    });

    totalWeightedScore += contribution;
    totalWeight += weight;
    totalConfidence += confidence;
    confidenceCount++;
  });

  // Calculate final scores
  const weightedScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
  const overallScore = Math.round(weightedScore);
  const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;

  // Determine compliance level and pass/fail
  const passed = overallScore >= thresholds.passing;
  const complianceLevel = determineComplianceLevel(overallScore, thresholds);

  const result: ScoringResult = {
    overallScore,
    weightedScore,
    complianceLevel,
    passed,
    confidence: Math.round(averageConfidence),
    breakdown,
  };

  // Add debug information if requested
  if (debugLogging) {
    result.debugInfo = {
      industryType,
      thresholds,
      confidenceWeighting,
      calculations: {
        totalWeightedScore,
        totalWeight,
        totalConfidence,
        confidenceCount,
        averageConfidence,
      },
    };

    console.log('🔢 [Improved Scoring] Calculation details:', result.debugInfo);
  }

  return result;
}

/**
 * Determine compliance level based on score and industry thresholds
 * Returns compliance levels that match the existing HipaaScanSummary type
 */
function determineComplianceLevel(
  score: number,
  thresholds: typeof INDUSTRY_THRESHOLDS.general | typeof INDUSTRY_THRESHOLDS.healthcare,
): 'compliant' | 'mostly_compliant' | 'partially_compliant' | 'non_compliant' {
  if (score >= thresholds.excellent) return 'compliant'; // Excellent -> compliant
  if (score >= thresholds.good) return 'mostly_compliant'; // Good -> mostly_compliant
  if (score >= thresholds.passing) return 'mostly_compliant'; // Passing -> mostly_compliant
  if (score >= thresholds.critical) return 'partially_compliant';
  return 'non_compliant';
}

/**
 * Calculate confidence-adjusted score for individual checks
 */
export function calculateConfidenceAdjustedScore(rawScore: number, confidence: number): number {
  // High confidence scores stay close to raw score
  // Low confidence scores trend toward middle (50%)
  return (rawScore * confidence) / 100 + 50 * (1 - confidence / 100);
}

/**
 * Validate scoring inputs and provide recommendations
 */
export function validateScoringInputs(checks: HipaaCheckResult[]): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check for required checks
  const requiredChecks = Object.keys(CHECK_WEIGHTS);
  const providedChecks = checks.map((c) => c.checkId);

  requiredChecks.forEach((required) => {
    if (!providedChecks.includes(required)) {
      issues.push(`Missing required check: ${required}`);
    }
  });

  // Check for score validity
  checks.forEach((check) => {
    if (
      check.overallScore !== null &&
      check.overallScore !== undefined &&
      (check.overallScore < 0 || check.overallScore > 100)
    ) {
      issues.push(`Invalid score for ${check.checkId}: ${check.overallScore}`);
    }

    if (check.confidence < 0 || check.confidence > 100) {
      issues.push(`Invalid confidence for ${check.checkId}: ${check.confidence}`);
    }
  });

  // Provide recommendations
  if (issues.length === 0) {
    recommendations.push('All scoring inputs are valid');
  } else {
    recommendations.push('Fix validation issues before calculating scores');
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}

/**
 * Compare old vs new scoring for migration analysis
 */
export function compareScoring(
  checks: HipaaCheckResult[],
  oldScore: number,
): {
  oldScore: number;
  newScore: number;
  difference: number;
  percentChange: number;
  recommendation: string;
} {
  const newResult = calculateImprovedOverallScore(checks, { debugLogging: true });
  const difference = newResult.overallScore - oldScore;
  const percentChange = oldScore > 0 ? (difference / oldScore) * 100 : 0;

  let recommendation = '';
  if (Math.abs(percentChange) < 5) {
    recommendation = 'Minimal change - safe to migrate';
  } else if (percentChange > 0) {
    recommendation = 'Score improved - review for accuracy';
  } else {
    recommendation = 'Score decreased - validate new logic';
  }

  return {
    oldScore,
    newScore: newResult.overallScore,
    difference,
    percentChange,
    recommendation,
  };
}
