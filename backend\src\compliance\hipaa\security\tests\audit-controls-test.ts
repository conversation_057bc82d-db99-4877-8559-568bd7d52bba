import { NucleiClient } from '../services/nuclei-client';
import { ContentAnalyzer } from '../services/content-analyzer';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export class AuditControlsTest {
  private nucleiClient: NucleiClient;
  private contentAnalyzer: ContentAnalyzer;

  constructor(nucleiClient: NucleiClient, contentAnalyzer: ContentAnalyzer) {
    this.nucleiClient = nucleiClient;
    this.contentAnalyzer = contentAnalyzer;
  }

  async runAuditControlsTests(targetUrl: string): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    // Test 1: Audit Log Endpoints
    results.push(await this.testAuditLogEndpoints(targetUrl));

    // Test 2: Audit Trail Indicators
    results.push(await this.testAuditTrailIndicators(targetUrl));

    // Test 3: Access Logging
    results.push(await this.testAccessLogging(targetUrl));

    return results;
  }

  private async testAuditLogEndpoints(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'audit-controls-endpoints';
    const testName = 'Audit Log Endpoints';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUDIT_CONTROLS;

    try {
      const auditEndpoints = [
        '/admin/logs',
        '/audit',
        '/logs',
        '/admin/audit',
        '/api/audit',
        '/api/logs',
      ];

      const auditIssues: FailureEvidence[] = [];
      const protectedAuditEndpoints: string[] = [];

      for (const endpoint of auditEndpoints) {
        try {
          const fullUrl = `${targetUrl}${endpoint}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          // Check if audit endpoints are properly protected
          const isProtected =
            response.statusCode === 401 ||
            response.statusCode === 403 ||
            response.responseHeaders.location?.includes('login');

          if (!isProtected && response.statusCode === 200) {
            // Check if response contains audit-related content
            const auditPatterns = [
              /audit.log|access.log|security.log/i,
              /timestamp.*user.*action/i,
              /log.entry|audit.entry/i,
              /\d{4}-\d{2}-\d{2}.*\d{2}:\d{2}:\d{2}/i, // timestamp pattern
            ];

            const hasAuditContent = auditPatterns.some((pattern) => pattern.test(response.body));

            if (hasAuditContent) {
              auditIssues.push({
                location: fullUrl,
                elementType: 'response',
                actualCode: `HTTP ${response.statusCode} - Audit content accessible`,
                expectedBehavior: 'Audit endpoints should require authentication',
                context: 'Audit logs accessible without authentication',
              });
            }
          } else {
            protectedAuditEndpoints.push(endpoint);
          }
        } catch (error) {
          // Network errors might indicate proper protection
          protectedAuditEndpoints.push(endpoint);
        }
      }

      if (auditIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify audit log endpoints are properly protected',
          category: 'technical',
          passed: false,
          failureReason: `${auditIssues.length} unprotected audit endpoints found`,
          riskLevel: 'high',
          failureEvidence: auditIssues,
          recommendedAction: 'Implement authentication and authorization for all audit endpoints',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify audit log endpoints are properly protected',
        category: 'technical',
        passed: true,
        evidence: `${protectedAuditEndpoints.length} audit endpoints properly protected`,
        pagesTested: protectedAuditEndpoints,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify audit log endpoints are properly protected',
        category: 'technical',
        passed: false,
        failureReason: `Audit endpoints test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Audit endpoints test should complete successfully',
            context: 'Audit endpoints test error',
          },
        ],
        recommendedAction: 'Review audit endpoint configuration and test setup',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testAuditTrailIndicators(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'audit-controls-trail-indicators';
    const testName = 'Audit Trail Indicators';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUDIT_CONTROLS;

    try {
      const testPages = ['/', '/login', '/dashboard', '/admin'];
      const auditIndicators: string[] = [];
      const missingAuditIndicators: FailureEvidence[] = [];

      for (const page of testPages) {
        try {
          const fullUrl = `${targetUrl}${page}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          if (response.statusCode === 200) {
            // Look for audit trail indicators in response headers
            const auditHeaders = ['x-request-id', 'x-trace-id', 'x-correlation-id', 'x-audit-id'];

            const hasAuditHeaders = auditHeaders.some(
              (header) =>
                response.responseHeaders[header] || response.responseHeaders[header.toLowerCase()],
            );

            // Look for audit indicators in HTML
            const auditPatterns = [
              /request.id|trace.id|correlation.id/i,
              /audit.trail|audit.log/i,
              /session.id.*\w{8,}/i,
            ];

            const hasAuditContent = auditPatterns.some((pattern) => pattern.test(response.body));

            if (hasAuditHeaders || hasAuditContent) {
              auditIndicators.push(page);
            } else {
              missingAuditIndicators.push({
                location: fullUrl,
                elementType: 'response',
                actualCode: 'No audit trail indicators found',
                expectedBehavior:
                  'Should include audit trail indicators (request ID, trace ID, etc.)',
                context: 'Missing audit trail indicators',
              });
            }
          }
        } catch (error) {
          // Page access error - continue
          continue;
        }
      }

      if (auditIndicators.length === 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Check for audit trail implementation indicators',
          category: 'technical',
          passed: false,
          failureReason: 'No audit trail indicators found',
          riskLevel: 'medium',
          failureEvidence: missingAuditIndicators,
          recommendedAction:
            'Implement audit trail indicators (request IDs, trace IDs, audit headers)',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for audit trail implementation indicators',
        category: 'technical',
        passed: true,
        evidence: `Audit trail indicators found on ${auditIndicators.length} pages`,
        pagesTested: auditIndicators,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for audit trail implementation indicators',
        category: 'technical',
        passed: false,
        failureReason: `Audit trail test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Audit trail test should complete successfully',
            context: 'Audit trail test error',
          },
        ],
        recommendedAction: 'Review audit trail configuration and test setup',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testAccessLogging(targetUrl: string): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'audit-controls-access-logging';
    const testName = 'Access Logging';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUDIT_CONTROLS;

    try {
      const sensitiveEndpoints = [
        '/admin',
        '/api/users',
        '/api/patients',
        '/api/medical-records',
        '/dashboard',
      ];

      const loggingIssues: FailureEvidence[] = [];
      const loggedEndpoints: string[] = [];

      for (const endpoint of sensitiveEndpoints) {
        try {
          const fullUrl = `${targetUrl}${endpoint}`;
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          // Check for logging indicators in response headers
          const loggingHeaders = ['x-request-id', 'x-trace-id', 'server', 'x-powered-by'];

          const hasLoggingIndicators = loggingHeaders.some(
            (header) =>
              response.responseHeaders[header] || response.responseHeaders[header.toLowerCase()],
          );

          // Check for server identification that suggests logging
          const serverHeader = response.responseHeaders.server || response.responseHeaders.Server;
          const hasServerInfo =
            serverHeader && !serverHeader.includes('nginx') && !serverHeader.includes('Apache');

          if (hasLoggingIndicators || hasServerInfo) {
            loggedEndpoints.push(endpoint);
          } else {
            loggingIssues.push({
              location: fullUrl,
              elementType: 'header',
              actualCode: 'No access logging indicators found',
              expectedBehavior: 'Should include access logging indicators',
              context: 'Missing access logging indicators for sensitive endpoint',
            });
          }
        } catch (error) {
          // Network errors might indicate proper protection
          loggedEndpoints.push(endpoint);
        }
      }

      // If no logging indicators found, it's not necessarily a failure
      // but we should recommend implementing proper logging
      if (loggedEndpoints.length === 0 && sensitiveEndpoints.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify access logging is implemented for sensitive endpoints',
          category: 'technical',
          passed: false,
          failureReason: 'No access logging indicators found for sensitive endpoints',
          riskLevel: 'medium',
          failureEvidence: loggingIssues,
          recommendedAction: 'Implement comprehensive access logging for all sensitive endpoints',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify access logging is implemented for sensitive endpoints',
        category: 'technical',
        passed: true,
        evidence: `Access logging indicators found for ${loggedEndpoints.length} sensitive endpoints`,
        pagesTested: loggedEndpoints,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify access logging is implemented for sensitive endpoints',
        category: 'technical',
        passed: false,
        failureReason: `Access logging test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Access logging test should complete successfully',
            context: 'Access logging test error',
          },
        ],
        recommendedAction: 'Review access logging configuration and test setup',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }
}
