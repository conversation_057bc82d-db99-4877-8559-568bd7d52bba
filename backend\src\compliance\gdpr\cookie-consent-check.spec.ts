// backend/src/compliance/gdpr/cookie-consent-check.spec.ts
import { jest } from '@jest/globals';
import { checkCookieConsent } from './cookie-consent-check';
import { GdprCheckResult } from './types';

// Mock global fetch unconditionally
global.fetch = jest.fn() as jest.MockedFunction<typeof global.fetch>;

describe('checkCookieConsent (Enhanced)', () => {
  const targetUrl = 'https://eugdprcompliant.com';

  beforeEach(() => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockClear();
  });

  it('should pass if a primary keyword is found', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () =>
        '<html><body>This website uses cookies for a better experience. Our cookie policy explains more.</body></html>',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(true);
    expect(result.name).toBe('Cookie Consent Mechanism Presence (Enhanced)');
    expect(result.description).toContain(
      'Cookie consent indicators suggest a mechanism is present',
    );
    expect(result.description).toContain('Primary keyword: "cookie policy"');
    expect(fetch).toHaveBeenCalledWith(targetUrl, {
      headers: { 'User-Agent': 'ComplyChecker/1.0' },
    });
  });

  it('should pass if an HTML pattern AND a choice text are found', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () =>
        '<html><body><div id="cookie-banner-main">Please accept all our tracking.</div></body></html>',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(true);
    expect(result.description).toContain('HTML pattern: "cookie-banner"');
    expect(result.description).toContain('Choice text: "accept"');
  });

  it('should pass if an HTML pattern AND a secondary keyword are found', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () =>
        '<html><body><div class="cookie-dialog">Manage your cookie preferences.</div></body></html>',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(true);
    expect(result.description).toContain('HTML pattern: "cookie-dialog"');
    expect(result.description).toContain('Secondary keyword: "cookie preferences"');
  });

  it('should fail if only an HTML pattern is found without choice text or secondary keyword', async () => {
    // This test simulates finding an HTML banner but no explicit consent choices or strong secondary keywords.
    const mockHtmlOnlyBanner = '<html><body><div id="cookie-banner">xyz</div></body></html>';
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => mockHtmlOnlyBanner,
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    console.log('Debug details for this test:', result.details); // Retain for debugging
    expect(result.passed).toBe(false);
    expect(result.description).toContain(
      'Could not confidently identify a cookie consent mechanism',
    );
    expect(result.description).toContain('HTML pattern: "cookie-banner"');
    expect(result.description).not.toContain('Choice text:');
    expect(result.description).not.toContain('Secondary keyword:');
    expect(result.description).not.toContain('Primary keyword:');
  });

  it('should fail if only a choice text is found without HTML pattern or primary keyword', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><body>You can accept all terms and conditions.</body></html>',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toContain(
      'Could not confidently identify a cookie consent mechanism',
    );
    expect(result.description).toContain('Choice text: "accept"');
    expect(result.description).not.toContain('HTML pattern:');
    expect(result.description).not.toContain('Primary keyword:');
  });

  it('should fail if no relevant indicators are found', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: true,
      text: async () => '<html><body>A completely unrelated webpage.</body></html>',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toContain('No specific indicators detected.');
  });

  it('should handle fetch errors gracefully', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockRejectedValueOnce(
      new Error('GDPR Fetch Failed'),
    );
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.name).toBe('Cookie Consent Mechanism Presence (Enhanced)');
    expect(result.description).toContain(
      `Failed to retrieve page content from ${targetUrl}. Error: GDPR Fetch Failed`,
    );
  });

  it('should handle non-ok HTTP responses gracefully', async () => {
    (global.fetch as jest.MockedFunction<typeof global.fetch>).mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Server Error',
      text: async () => 'Server Error',
    } as Response);
    const result: GdprCheckResult = await checkCookieConsent(targetUrl);
    expect(result.passed).toBe(false);
    expect(result.description).toContain(
      `Failed to retrieve page content from ${targetUrl}. Error: Failed to fetch ${targetUrl}. Status: 500 Server Error`,
    );
  });
});
