// D:\Web projects\Comply Checker\backend\src\types\index.ts

/**
 * Options for configuring an API call.
 * Used by the frontend apiCall utility.
 */
export interface ApiCallOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: unknown;
}

/**
 * Represents a finding identified during a compliance scan.
 */
export interface ComplianceFinding {
  id: string;
  scan_id: string;
  standard: string; // e.g., 'WCAG2.1', 'GDPR', 'HIPAA'
  check_id: string; // Specific check identifier, e.g., 'WCAG.Principle1.Guideline1_1.1_1_1'
  description: string; // Description of the finding or the check failed
  passed: boolean; // True if the check passed, false if it failed (is a finding)
  severity?: 'critical' | 'high' | 'medium' | 'low' | 'informational'; // Optional severity level
  details?: Record<string, unknown> | string; // Additional details, can be structured or a simple string
  remediation_suggestion?: string; // Suggestion on how to fix the issue
  created_at: string; // ISO 8601 date string
}

/**
 * Enhanced HIPAA analysis results from the 3-level system
 */
export interface HipaaAnalysisResult {
  overallScore: number;
  privacyPolicyPresent: boolean;
  privacyPolicyUrl?: string;
  contentAnalysisScore: number;
  contactInformationScore: number;

  // 3-level analysis breakdown
  analysisLevels: {
    level1: {
      score: number;
      method: 'pattern_matching';
      findings: string[];
      processingTime: number;
    };
    level2: {
      score: number;
      method: 'nlp_analysis';
      entities: {
        people: string[];
        organizations: string[];
        phoneNumbers: string[];
        emails: string[];
      };
      statements: {
        privacy: number;
        rights: number;
      };
      processingTime: number;
    };
    level3: {
      score: number;
      method: 'ai_analysis';
      complianceGaps: Array<{
        requirement: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        description: string;
      }>;
      recommendations: Array<{
        title: string;
        description: string;
        priority: 'LOW' | 'MEDIUM' | 'HIGH';
      }>;
      processingTime: number;
    };
  };

  // Key findings and recommendations
  findings: Array<{
    id: string;
    type: 'missing' | 'incomplete' | 'compliant' | 'non_compliant';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    description: string;
    recommendation?: string;
  }>;

  recommendations: Array<{
    id: string;
    title: string;
    description: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH';
    category: 'privacy_policy' | 'content' | 'contact' | 'technical';
  }>;

  // Performance metrics
  totalProcessingTime: number;
  analysisTimestamp: string;
}

/**
 * Represents a compliance scan performed on a URL.
 */
export interface Scan {
  id: string;
  url: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  standards_scanned?: string[]; // Array of standards like ['WCAG', 'GDPR']
  summary_report?: Record<string, unknown>; // High-level summary, structure TBD
  created_at: string; // ISO 8601 date string
  completed_at?: string; // ISO 8601 date string, present if status is 'completed' or 'failed'
  updated_at?: string; // ISO 8601 date string
  findings?: ComplianceFinding[]; // Array of findings associated with this scan

  // Enhanced results for specific standards
  hipaaResults?: HipaaAnalysisResult;
  gdprResults?: Record<string, unknown>; // Future GDPR results
  wcagResults?: Record<string, unknown>; // Future WCAG results
  adaResults?: Record<string, unknown>; // Future ADA results

  // Enhanced HIPAA results from new analysis system
  enhancedHipaaResults?: {
    targetUrl: string;
    overallScore: string;
    overallPassed: boolean;
    summary: {
      totalChecks: number;
      passedChecks: number;
      failedChecks: number;
      complianceLevel: string;
    };
    checksBreakdown: Array<{
      checkId: string;
      name: string;
      passed: boolean;
      score: string | null;
      confidence: string;
      analysisLevels: Array<{
        level: number;
        method: string;
        score: string | number;
        confidence: string | number;
        processingTime: number;
        findings?: Array<{
          type: string;
          content: string;
          context: string;
          confidence: number;
          interpretation?: string;
          entities?: string[];
        }>;
        foundPatterns?: number;
        totalPatterns?: number;
        entities?: {
          people?: string[];
          organizations?: string[];
          phoneNumbers?: string[];
          emails?: string[];
        };
        privacyStatements?: Array<{
          text: string;
          type: string;
          confidence: number;
          location: number;
        }>;
        rightsStatements?: Array<{
          text: string;
          rightType: string;
          confidence: number;
          location: number;
        }>;
        identifiedGaps?: Array<{
          requirement: string;
          severity: string;
          description: string;
        }>;
        riskFactors?: Array<{
          type: string;
          severity: string;
          description: string;
          recommendation: string;
        }>;
        recommendations?: Array<{
          title: string;
          description: string;
          priority: string;
        }>;
        [key: string]: unknown;
      }>;
    }>;
    // Alternative property name for backwards compatibility
    checks?: Array<{
      checkId: string;
      name: string;
      passed: boolean;
      score: string | null;
      confidence: string;
      analysisLevels: Array<{
        level: number;
        method: string;
        score: string | number;
        confidence: string | number;
        processingTime: number;
        findings?: Array<{
          type: string;
          content: string;
          context: string;
          confidence: number;
          interpretation?: string;
          entities?: string[];
        }>;
        foundPatterns?: number;
        totalPatterns?: number;
        entities?: {
          people?: string[];
          organizations?: string[];
          phoneNumbers?: string[];
          emails?: string[];
        };
        privacyStatements?: Array<{
          text: string;
          type: string;
          confidence: number;
          location: number;
        }>;
        rightsStatements?: Array<{
          text: string;
          rightType: string;
          confidence: number;
          location: number;
        }>;
        identifiedGaps?: Array<{
          requirement: string;
          severity: string;
          description: string;
        }>;
        riskFactors?: Array<{
          type: string;
          severity: string;
          description: string;
          recommendation: string;
        }>;
        recommendations?: Array<{
          title: string;
          description: string;
          priority: string;
        }>;
        [key: string]: unknown;
      }>;
    }>;
    // Recommendations array for backwards compatibility
    recommendations?: Array<{
      id: string;
      title: string;
      description: string;
      priority: number;
      [key: string]: unknown;
    }>;
    recommendationsCount: number;
    metadata: {
      processingTime: number;
      version: string;
      analysisLevelsUsed: number[];
    };
  };

  // Error information for failed scans
  error?: string;
}

/**
 * Represents the authenticated user's profile information.
 */
export interface AuthUser {
  id: string;
  email?: string;
  username?: string; // Keycloak often uses username
  firstName?: string;
  lastName?: string;
  emailVerified?: boolean;
  // Add other relevant user fields as needed, e.g., roles, groups
}

/**
 * Represents a user record from the database.
 */
export interface User {
  id: string; // Typically a UUID, primary key
  keycloak_id: string; // Keycloak subject ID
  email: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  // Add other fields from your 'users' table as necessary
}

/**
 * Represents a Scan object along with its associated compliance findings.
 */
export interface ScanWithFindings extends Scan {
  findings: ComplianceFinding[];
}

/**
 * Payload for creating a new compliance scan.
 */
export interface CreateScanPayload {
  url: string;
  standards: string[]; // Array of standards to scan against, e.g., ['WCAG', 'GDPR']
}

// Add other shared types here as they are identified.
