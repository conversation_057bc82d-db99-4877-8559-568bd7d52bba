{"name": "comply-checker-monorepo", "version": "0.1.0", "private": true, "description": "SaaS MVP for GDPR/CCPA, HIPAA, ADA, WCAG compliance scanning.", "scripts": {"install-all": "npm install -ws && npm install -w frontend && npm install -w backend", "dev:frontend": "npm run dev -w frontend", "dev:backend": "npm run dev -w backend", "build:frontend": "npm run build -w frontend", "build:backend": "npm run build -w backend", "lint": "npm run lint -w frontend && npm run lint -w backend", "lint:fix": "npm run lint:fix -w frontend && npm run lint:fix -w backend", "format": "prettier --write ./**/*.{ts,tsx,json,md}", "prepare": "husky install"}, "workspaces": ["frontend", "backend"], "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --cache --fix --max-warnings=0", "prettier --write"], "*.{json,md,css,scss}": ["prettier --write"]}, "devDependencies": {"@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-hooks": "^4.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "ts-jest": "^29.3.4", "typescript": "^5.0.0"}, "engines": {"node": ">=20.x"}, "dependencies": {"ts-node": "^10.9.2", "zod": "^3.25.50"}}