import { z } from 'zod';

// Helper for transforming string to number, as process.env values are strings
const portSchema = z.preprocess(
  (val) => (typeof val === 'string' ? parseInt(val, 10) : val),
  z.number().int().positive(),
);

const urlSchema = z.string().url({ message: 'Invalid URL' });

const isTestEnv = process.env.NODE_ENV === 'test';

const baseSchema = {
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // PostgreSQL Configuration
  POSTGRES_USER: z.string().min(1),
  POSTGRES_PASSWORD: z.string().min(1),
  POSTGRES_DB: z.string().min(1),
  POSTGRES_HOST: z.string().min(1).optional(), // Made optional, will default to 'localhost' later
  POSTGRES_PORT: portSchema,
  DATABASE_URL: z.string().min(1).optional(), // Will be constructed from other fields later

  // Backend Application Configuration
  BACKEND_PORT: portSchema,
  FRONTEND_URL: urlSchema,
  SESSION_SECRET: z.string().min(1),
};

const productionAndDevelopmentSchema = {
  ...baseSchema,
  // Keycloak Configuration
  KEYCLOAK_ADMIN_USER: z.string().min(1),
  KEYCLOAK_ADMIN_PASSWORD: z.string().min(1),
  KEYCLOAK_PORT: portSchema,
  KEYCLOAK_REALM: z.string().min(1),
  KEYCLOAK_CLIENT_ID_FRONTEND: z.string().min(1),
  KEYCLOAK_CLIENT_ID_BACKEND: z.string().min(1),
  KEYCLOAK_CLIENT_SECRET_BACKEND: z.string().min(1),
  KEYCLOAK_URL: z.string().min(1),

  // MailHog Configuration
  MAILHOG_SMTP_PORT: portSchema,
  MAILHOG_HTTP_PORT: portSchema,
  SMTP_HOST: z.string().min(1),
  SMTP_PORT: portSchema,

  // Frontend Application Configuration (NEXT_PUBLIC_ variables)
  NEXT_PUBLIC_KEYCLOAK_URL: urlSchema.optional(),
  NEXT_PUBLIC_KEYCLOAK_REALM: z.string().min(1).optional(),
  NEXT_PUBLIC_KEYCLOAK_CLIENT_ID: z.string().min(1).optional(),
  NEXT_PUBLIC_BACKEND_API_URL: urlSchema.optional(),
};

const testSchema = {
  ...baseSchema,
  POSTGRES_USER: baseSchema.POSTGRES_USER.default('testuser'),
  POSTGRES_PASSWORD: baseSchema.POSTGRES_PASSWORD.default('testpass'),
  POSTGRES_DB: baseSchema.POSTGRES_DB.default('testdb'),
  POSTGRES_HOST: baseSchema.POSTGRES_HOST.default('localhost'), // Default for tests
  POSTGRES_PORT: baseSchema.POSTGRES_PORT.default(5433), // Different port for test DB
  BACKEND_PORT: baseSchema.BACKEND_PORT.default(3001),
  FRONTEND_URL: baseSchema.FRONTEND_URL.default('http://localhost:3000'),
  SESSION_SECRET: baseSchema.SESSION_SECRET.default('test-session-secret'),

  // Optional/defaulted for test env
  KEYCLOAK_ADMIN_USER: z.string().min(1).optional().default('admin'),
  KEYCLOAK_ADMIN_PASSWORD: z.string().min(1).optional().default('admin'),
  KEYCLOAK_PORT: portSchema.optional().default(8088),
  KEYCLOAK_REALM: z.string().min(1).optional().default('testrealm'),
  KEYCLOAK_CLIENT_ID_FRONTEND: z.string().min(1).optional().default('test-frontend-client'),
  KEYCLOAK_CLIENT_ID_BACKEND: z.string().min(1).optional().default('test-backend-client'),
  KEYCLOAK_CLIENT_SECRET_BACKEND: z.string().min(1).optional().default('test-secret'),
  KEYCLOAK_URL: z.string().min(1).optional().default('http://localhost:8088/auth'),
  MAILHOG_SMTP_PORT: portSchema.optional().default(1026),
  MAILHOG_HTTP_PORT: portSchema.optional().default(8026),
  SMTP_HOST: z.string().min(1).optional().default('localhost'),
  SMTP_PORT: portSchema.optional().default(1026),
  NEXT_PUBLIC_KEYCLOAK_URL: urlSchema.optional().default('http://localhost:8088/auth'),
  NEXT_PUBLIC_KEYCLOAK_REALM: z.string().min(1).optional().default('testrealm'),
  NEXT_PUBLIC_KEYCLOAK_CLIENT_ID: z.string().min(1).optional().default('test-frontend-client'),
  NEXT_PUBLIC_BACKEND_API_URL: urlSchema.optional().default('http://localhost:3001/api/v1'),
};

const environmentSchema = z.object(isTestEnv ? testSchema : productionAndDevelopmentSchema);

let parsedEnv: z.infer<typeof environmentSchema>;

try {
  // This parsing is primarily for server-side (Node.js) environments.
  // For Next.js client-side, only NEXT_PUBLIC_ variables are exposed directly from process.env.
  parsedEnv = environmentSchema.parse(process.env);
} catch (error) {
  if (error instanceof z.ZodError) {
    console.error('🔴 Invalid environment variables:');
    error.errors.forEach((err) => {
      console.error(`  - Path: ${err.path.join('.')}, Message: ${err.message}`);
    });
    // In a production environment or critical setup, you should exit the process.
    // if (typeof process !== 'undefined' && process.exit) { process.exit(1); }
    throw new Error('Missing or invalid environment variables. Check console for details.');
  }
  // Re-throw other types of errors
  throw error;
}

// Log critical environment variables for debugging
console.log(`[ENV_DEBUG] NODE_ENV: ${parsedEnv.NODE_ENV}`);
console.log(`[ENV_DEBUG] isTestEnv: ${isTestEnv}`);
console.log(`[ENV_DEBUG] process.env.POSTGRES_HOST (raw): ${process.env.POSTGRES_HOST}`);
console.log(`[ENV_DEBUG] parsedEnv.POSTGRES_HOST (from Zod): ${parsedEnv.POSTGRES_HOST}`);

// Construct the DATABASE_URL from validated components to ensure it's fully resolved
const dbHost = parsedEnv.POSTGRES_HOST || (isTestEnv ? 'localhost' : 'db'); // Default to 'localhost' for test, 'db' for dev/prod (docker-compose service name)

console.log(`[ENV_DEBUG] dbHost chosen: ${dbHost}`);

const finalEnv = {
  ...parsedEnv,
  DATABASE_URL: `postgresql://${parsedEnv.POSTGRES_USER}:${parsedEnv.POSTGRES_PASSWORD}@${dbHost}:${parsedEnv.POSTGRES_PORT}/${parsedEnv.POSTGRES_DB}`,
};

// If in test, override specific values that might have been parsed from a lingering .env file
if (isTestEnv) {
  finalEnv.POSTGRES_USER = 'testuser';
  finalEnv.POSTGRES_PASSWORD = 'testpass';
  finalEnv.POSTGRES_DB = 'testdb';
  finalEnv.POSTGRES_PORT = 5433;
  finalEnv.DATABASE_URL = `postgresql://${finalEnv.POSTGRES_USER}:${finalEnv.POSTGRES_PASSWORD}@${dbHost}:${finalEnv.POSTGRES_PORT}/${finalEnv.POSTGRES_DB}`;
  finalEnv.NODE_ENV = 'test'; // Ensure NODE_ENV is 'test'
}

console.log(`[ENV_DEBUG] Constructed DATABASE_URL: ${finalEnv.DATABASE_URL}`);
export { finalEnv as env };

/*
Example Usage (e.g., in your backend's main entry point or config file):

import { env } from '../../lib/env'; // Adjust path as per your project structure

// Early in your application startup sequence:
console.log(`Application running in ${env.NODE_ENV} mode.`);
console.log('PostgreSQL Port:', env.POSTGRES_PORT);

// If any required environment variable is missing or invalid (according to the schema),
// the application will throw an error during the "env = environmentSchema.parse(process.env);" line,
// preventing it from starting with a bad configuration.
*/
