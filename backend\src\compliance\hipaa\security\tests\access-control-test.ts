import { NucleiClient } from '../services/nuclei-client';
import { ContentAnalyzer } from '../services/content-analyzer';
import { HipaaTestDetail, HipaaTestFailure, FailureEvidence } from '../types';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';

export interface AccessControlTestConfig {
  protectedEndpoints: string[];
  publicEndpoints: string[];
  timeout: number;
}

export class AccessControlTest {
  private nucleiClient: NucleiClient;
  private contentAnalyzer: ContentAnalyzer;

  constructor(nucleiClient: NucleiClient, contentAnalyzer: ContentAnalyzer) {
    this.nucleiClient = nucleiClient;
    this.contentAnalyzer = contentAnalyzer;
  }

  async runAccessControlTests(
    targetUrl: string,
    config: AccessControlTestConfig,
  ): Promise<(HipaaTestDetail | HipaaTestFailure)[]> {
    const results: (HipaaTestDetail | HipaaTestFailure)[] = [];

    // Test 1: Protected Endpoint Access Control
    results.push(await this.testProtectedEndpoints(targetUrl, config.protectedEndpoints));

    // Test 2: Role-Based Access Indicators
    results.push(await this.testRoleBasedAccess(targetUrl, config.protectedEndpoints));

    // Test 3: Session Management
    results.push(await this.testSessionManagement(targetUrl));

    return results;
  }

  private async testProtectedEndpoints(
    targetUrl: string,
    protectedEndpoints: string[],
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'access-control-protected-endpoints';
    const testName = 'Protected Endpoint Access Control';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL;

    try {
      const failedEndpoints: FailureEvidence[] = [];
      const passedEndpoints: string[] = [];

      for (const endpoint of protectedEndpoints) {
        const fullUrl = `${targetUrl}${endpoint}`;

        try {
          const response = await this.nucleiClient.fetchUrlContent(fullUrl);

          // Check if endpoint is properly protected
          const isProtected =
            response.statusCode === 401 ||
            response.statusCode === 403 ||
            response.responseHeaders.location?.includes('login');

          if (!isProtected) {
            // Check for ePHI exposure in unprotected response
            // Note: Content analysis could be used here to determine risk level based on ePHI presence

            failedEndpoints.push({
              location: fullUrl,
              elementType: 'response',
              actualCode: `HTTP ${response.statusCode}\n${response.body.substring(0, 500)}...`,
              expectedBehavior: 'Should return 401, 403, or redirect to login',
              context: `Endpoint accessible without authentication. Status: ${response.statusCode}`,
            });
          } else {
            passedEndpoints.push(endpoint);
          }
        } catch (error) {
          // Network errors might indicate proper blocking
          passedEndpoints.push(endpoint);
        }
      }

      if (failedEndpoints.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify that protected endpoints require proper authentication',
          category: 'technical',
          passed: false,
          failureReason: `${failedEndpoints.length} protected endpoints are accessible without authentication`,
          riskLevel: failedEndpoints.some((e) => e.context.includes('ePHI')) ? 'critical' : 'high',
          failureEvidence: failedEndpoints,
          recommendedAction: 'Implement authentication middleware for all protected endpoints',
          remediationPriority: 1,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify that protected endpoints require proper authentication',
        category: 'technical',
        passed: true,
        evidence: `All ${protectedEndpoints.length} protected endpoints properly require authentication`,
        pagesTested: protectedEndpoints,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify that protected endpoints require proper authentication',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testRoleBasedAccess(
    targetUrl: string,
    protectedEndpoints: string[],
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'access-control-role-based';
    const testName = 'Role-Based Access Control Indicators';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL;

    try {
      const roleIndicators: string[] = [];
      const missingRoleControls: FailureEvidence[] = [];

      for (const endpoint of protectedEndpoints) {
        const fullUrl = `${targetUrl}${endpoint}`;
        const response = await this.nucleiClient.fetchUrlContent(fullUrl);

        // Look for role-based access indicators in response
        const rolePatterns = [
          /role\s*[:=]\s*['"]?\w+['"]?/i,
          /permission\s*[:=]\s*['"]?\w+['"]?/i,
          /access\s*level\s*[:=]\s*['"]?\w+['"]?/i,
          /user\s*type\s*[:=]\s*['"]?\w+['"]?/i,
        ];

        const hasRoleIndicators = rolePatterns.some(
          (pattern) =>
            pattern.test(response.body) ||
            Object.values(response.responseHeaders).some((header) => pattern.test(header)),
        );

        if (hasRoleIndicators) {
          roleIndicators.push(endpoint);
        } else if (response.statusCode === 200) {
          missingRoleControls.push({
            location: fullUrl,
            elementType: 'response',
            actualCode: response.body.substring(0, 300),
            expectedBehavior: 'Should include role-based access indicators',
            context: 'No role-based access control indicators found',
          });
        }
      }

      if (roleIndicators.length === 0 && protectedEndpoints.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Check for role-based access control implementation indicators',
          category: 'technical',
          passed: false,
          failureReason: 'No role-based access control indicators found',
          riskLevel: 'medium',
          failureEvidence: missingRoleControls,
          recommendedAction: 'Implement role-based access controls with clear indicators',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for role-based access control implementation indicators',
        category: 'technical',
        passed: true,
        evidence: `Role-based access indicators found in ${roleIndicators.length} endpoints`,
        pagesTested: roleIndicators,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Check for role-based access control implementation indicators',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }

  private async testSessionManagement(
    targetUrl: string,
  ): Promise<HipaaTestDetail | HipaaTestFailure> {
    const testId = 'access-control-session-management';
    const testName = 'Session Management Security';
    const hipaaSection = HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL;

    try {
      const loginUrl = `${targetUrl}/login`;
      const response = await this.nucleiClient.fetchUrlContent(loginUrl);

      const contentAnalysis = this.contentAnalyzer.analyzeContent(
        response.body,
        loginUrl,
        response.responseHeaders,
      );

      const sessionIssues: FailureEvidence[] = [];

      // Check cookie security
      contentAnalysis.cookieAnalysis.forEach((cookie) => {
        if (cookie.securityIssues.length > 0) {
          sessionIssues.push({
            location: loginUrl,
            elementType: 'cookie',
            actualCode: `Cookie: ${cookie.name}; Secure: ${cookie.secure}; HttpOnly: ${cookie.httpOnly}; SameSite: ${cookie.sameSite}`,
            expectedBehavior: 'Cookies should be Secure, HttpOnly, and have SameSite attribute',
            context: `Cookie security issues: ${cookie.securityIssues.join(', ')}`,
          });
        }
      });

      if (sessionIssues.length > 0) {
        return {
          testId,
          testName,
          hipaaSection,
          description: 'Verify secure session management implementation',
          category: 'technical',
          passed: false,
          failureReason: `${sessionIssues.length} session security issues found`,
          riskLevel: 'medium',
          failureEvidence: sessionIssues,
          recommendedAction: 'Configure secure cookie attributes and session management',
          remediationPriority: 2,
          timestamp: new Date(),
        };
      }

      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify secure session management implementation',
        category: 'technical',
        passed: true,
        evidence: 'Session management appears to be properly configured',
        pagesTested: ['/login'],
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        testId,
        testName,
        hipaaSection,
        description: 'Verify secure session management implementation',
        category: 'technical',
        passed: false,
        failureReason: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: error instanceof Error ? error.message : 'Unknown error',
            expectedBehavior: 'Test should execute successfully',
            context: 'Test execution error',
          },
        ],
        recommendedAction: 'Review test configuration and target URL accessibility',
        remediationPriority: 3,
        timestamp: new Date(),
      };
    }
  }
}
