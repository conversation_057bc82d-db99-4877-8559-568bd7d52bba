# Production Environment Configuration for HIPAA Compliance Checker
# Copy this file to .env.production and update with your actual values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Node.js Environment
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Application URLs
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=complydb
POSTGRES_USER=complyuser
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Database URL (constructed from above values)
DATABASE_URL=************************************************************************/complydb

# Database Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_ACQUIRE_TIMEOUT=60000
DB_IDLE_TIMEOUT=30000
DB_SSL=false

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# =============================================================================
# AUTHENTICATION (KEYCLOAK)
# =============================================================================

# Keycloak Configuration
KEYCLOAK_URL=http://keycloak:8080
KEYCLOAK_REALM=comply-checker
KEYCLOAK_CLIENT_ID=comply-checker-backend
KEYCLOAK_CLIENT_SECRET=your_keycloak_client_secret_here

# Keycloak Admin
KEYCLOAK_ADMIN_PASSWORD=your_secure_keycloak_admin_password_here

# =============================================================================
# API SECURITY
# =============================================================================

# API Key for external access
API_KEY=your_secure_api_key_here

# JWT Secret (if using custom JWT)
JWT_SECRET=your_jwt_secret_here

# Session Secret
SESSION_SECRET=your_session_secret_here

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL=info
LOG_CONSOLE=true
LOG_FILE=true
LOG_DIRECTORY=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================================================
# MONITORING & METRICS
# =============================================================================

# Enable monitoring features
ENABLE_HEALTH_CHECK=true
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# Monitoring passwords
GRAFANA_PASSWORD=your_secure_grafana_password_here

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Compliance Analysis Features
FEATURE_HIPAA=true
FEATURE_GDPR=true
FEATURE_WCAG=true
FEATURE_ADA=true
FEATURE_ADVANCED_AI=true
FEATURE_CACHING=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Analysis Limits
MAX_CONCURRENT_ANALYSES=2
MAX_CONCURRENT_CHECKS=4
ANALYSIS_TIMEOUT=120000
NETWORK_TIMEOUT=30000

# Memory Management
MAX_MEMORY_USAGE_MB=4800
GC_THRESHOLD_MB=3200

# AI/NLP Settings
AI_MODEL_CONCURRENCY=1
NLP_BATCH_SIZE=10
CONTENT_CHUNK_SIZE=2048

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email Service (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# Webhook URLs (for alerts)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL Certificate paths (if using custom certificates)
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# Let's Encrypt settings
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_DOMAIN=your-domain.com

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limiting settings
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# CORS allowed origins (comma-separated)
CORS_ORIGINS=https://your-domain.com,https://app.your-domain.com

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================

# Deployment environment
DEPLOYMENT_ENV=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_TIMESTAMP=2024-01-01T00:00:00Z

# Health check URLs
HEALTH_CHECK_URL=http://localhost:3000/health
READINESS_CHECK_URL=http://localhost:3000/ready

# =============================================================================
# OPTIONAL: MONITORING STACK
# =============================================================================

# Enable monitoring stack (Prometheus + Grafana)
ENABLE_MONITORING=false

# Prometheus settings
PROMETHEUS_RETENTION=200h
PROMETHEUS_SCRAPE_INTERVAL=15s

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline'
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_FONT_SRC='self' https://fonts.gstatic.com
CSP_IMG_SRC='self' data: https:
CSP_CONNECT_SRC='self'

# =============================================================================
# DEVELOPMENT OVERRIDES (for staging)
# =============================================================================

# Uncomment for staging environment
# NODE_ENV=staging
# LOG_LEVEL=debug
# FEATURE_ADVANCED_AI=false
# MAX_CONCURRENT_ANALYSES=1
