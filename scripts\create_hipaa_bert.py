#!/usr/bin/env python3
"""
Fine-tune BERT specifically for HIPAA compliance analysis
"""

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from datasets import Dataset
import pandas as pd

def create_hipaa_training_data():
    """Create training dataset for HIPAA compliance"""
    
    # Sample HIPAA compliance training data
    # In production, this would be a larger dataset of privacy policies
    # labeled with compliance scores
    
    training_data = [
        {
            "text": "Notice of Privacy Practices. This notice describes how medical information about you may be used and disclosed and how you can get access to this information.",
            "label": 1,  # Compliant
            "score": 0.9
        },
        {
            "text": "We collect personal information when you visit our website.",
            "label": 0,  # Non-compliant (too vague)
            "score": 0.3
        },
        {
            "text": "Protected health information (PHI) will only be used for treatment, payment, and healthcare operations as permitted by HIPAA.",
            "label": 1,  # Compliant
            "score": 0.95
        },
        {
            "text": "You have the right to access, amend, and request an accounting of disclosures of your protected health information.",
            "label": 1,  # Compliant
            "score": 0.9
        },
        {
            "text": "We may share your information with third parties.",
            "label": 0,  # Non-compliant (too broad)
            "score": 0.2
        }
        # Add more training examples...
    ]
    
    return Dataset.from_pandas(pd.DataFrame(training_data))

def fine_tune_hipaa_bert():
    """Fine-tune BERT for HIPAA compliance classification"""
    
    print("🚀 Starting HIPAA-BERT fine-tuning...")
    
    # Load base model and tokenizer
    model_name = "bert-base-uncased"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, 
        num_labels=2  # Compliant/Non-compliant
    )
    
    # Prepare training data
    train_dataset = create_hipaa_training_data()
    
    def tokenize_function(examples):
        return tokenizer(
            examples["text"], 
            truncation=True, 
            padding=True, 
            max_length=512
        )
    
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./hipaa-bert",
        learning_rate=2e-5,
        per_device_train_batch_size=16,
        num_train_epochs=3,
        weight_decay=0.01,
        logging_dir="./logs",
        save_strategy="epoch",
        evaluation_strategy="epoch",
        load_best_model_at_end=True,
    )
    
    # Data collator
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # Train the model
    print("🔥 Training HIPAA-BERT...")
    trainer.train()
    
    # Save the model
    print("💾 Saving HIPAA-BERT...")
    trainer.save_model("./hipaa-bert-final")
    tokenizer.save_pretrained("./hipaa-bert-final")
    
    print("✅ HIPAA-BERT training completed!")
    print("📁 Model saved to: ./hipaa-bert-final")

if __name__ == "__main__":
    fine_tune_hipaa_bert()
