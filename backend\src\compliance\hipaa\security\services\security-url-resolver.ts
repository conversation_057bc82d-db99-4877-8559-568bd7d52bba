// backend/src/compliance/hipaa/security/services/security-url-resolver.ts

import axios, { AxiosInstance } from 'axios';
import * as https from 'https';
import { JSDOM } from 'jsdom';

/**
 * Security-focused link with metadata
 */
export interface SecurityLink {
  url: string;
  text: string;
  type:
    | 'privacy'
    | 'terms'
    | 'security'
    | 'login'
    | 'contact'
    | 'admin'
    | 'api'
    | 'important'
    | 'footer'
    | 'header';
  priority: 'high' | 'medium' | 'low';
  location: 'header' | 'footer' | 'main' | 'nav' | 'sidebar';
  accessible: boolean;
  format: 'html' | 'pdf' | 'doc' | 'txt' | 'json' | 'xml';
}

/**
 * Enhanced URL resolver for HIPAA security scanning
 * Focuses on finding security-relevant pages from headers, footers, and navigation
 */
export class SecurityURLResolver {
  private static httpClient: AxiosInstance;

  /**
   * Initialize the secure HTTP client
   */
  private static initializeHttpClient(): void {
    if (this.httpClient !== undefined) return;

    this.httpClient = axios.create({
      timeout: 30000,
      maxRedirects: 5,
      maxContentLength: 10 * 1024 * 1024, // 10MB limit
      httpsAgent: new https.Agent({
        rejectUnauthorized: true,
        secureProtocol: 'TLSv1_2_method',
      }),
      headers: {
        'User-Agent': 'ComplyChecker-HIPAA-Security/2.0 (+https://complychecker.com/security-bot)',
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });
  }

  /**
   * Find ALL internal links from homepage for ZAP scanning
   * This extracts every internal link from the homepage and provides them to ZAP
   */
  static async findAllInternalLinks(targetUrl: string, maxUrls: number = 50): Promise<string[]> {
    try {
      this.initializeHttpClient();
      console.log(`🔍 Extracting ALL internal links from homepage: ${targetUrl}`);

      const response = await this.httpClient.get(targetUrl);
      const html = response.data;

      const dom = new JSDOM(html);
      const document = dom.window.document;

      // Get ALL links from the page
      const allLinks = Array.from(document.querySelectorAll('a[href]'));
      const internalUrls = new Set<string>();

      // Always include the homepage itself
      internalUrls.add(targetUrl);

      console.log(`📄 Found ${allLinks.length} total links on homepage`);

      allLinks.forEach((link) => {
        const href = link.getAttribute('href');
        if (href && this.isInternalLink(href, targetUrl)) {
          const absoluteUrl = this.resolveUrl(href, targetUrl);
          if (absoluteUrl && this.isValidUrl(absoluteUrl)) {
            internalUrls.add(absoluteUrl);
          }
        }
      });

      const finalUrls = Array.from(internalUrls).slice(0, maxUrls);

      console.log(`📊 Extracted ${finalUrls.length} internal URLs for ZAP scanning`);
      console.log(
        `🔗 Sample URLs: ${finalUrls
          .slice(0, 5)
          .map((url) => new URL(url).pathname)
          .join(', ')}${finalUrls.length > 5 ? '...' : ''}`,
      );

      return finalUrls;
    } catch (error) {
      console.error('❌ Error extracting internal links:', error);
      return [targetUrl]; // Return at least the homepage
    }
  }

  /**
   * Find security-relevant URLs from a webpage
   * Enhanced to look at top menu areas and bottom sections
   */
  static async findSecurityRelevantUrls(
    targetUrl: string,
    maxUrls: number = 25,
  ): Promise<SecurityLink[]> {
    try {
      this.initializeHttpClient();
      console.log(`🔍 Discovering security-relevant URLs from: ${targetUrl}`);

      const response = await this.httpClient.get(targetUrl);
      const html = response.data;

      const dom = new JSDOM(html);
      const document = dom.window.document;

      const securityLinks: SecurityLink[] = [];

      // 1. Find TOP MENU/HEADER links (for login/auth pages)
      const topMenuSelectors = [
        'header',
        '.header',
        '#header',
        '.site-header',
        '.page-header',
        '.main-nav',
        '.primary-nav',
        '.top-nav',
        '.navbar',
        '.nav-bar',
        '.top-menu',
        '.main-menu',
        '.header-nav',
        '.header-menu',
        '.user-nav',
        '.account-nav',
        '.login-nav',
        '.auth-nav',
      ];

      for (const selector of topMenuSelectors) {
        const sectionLinks = this.findLinksInSelector(document, selector, targetUrl, 'header');
        securityLinks.push(...sectionLinks);
      }

      // 2. Find BOTTOM SECTION links (footer and bottom areas)
      const bottomSectionSelectors = [
        'footer',
        '.footer',
        '#footer',
        '.site-footer',
        '.page-footer',
        '.footer-links',
        '.footer-nav',
        '.footer-menu',
        '.bottom-nav',
        '.bottom-section',
        '.bottom-links',
        '.site-info',
        '.legal-links',
        '.bottom-menu',
        '.footer-content',
        '.footer-wrapper',
      ];

      for (const selector of bottomSectionSelectors) {
        const sectionLinks = this.findLinksInSelector(document, selector, targetUrl, 'footer');
        securityLinks.push(...sectionLinks);
      }

      // 3. Find navigation menu links
      const navLinks = this.findLinksInSection(document, 'nav', targetUrl);
      securityLinks.push(...navLinks);

      // 4. Look for specific security-related links anywhere on the page
      const specificLinks = this.findSpecificSecurityLinks(document, targetUrl);
      securityLinks.push(...specificLinks);

      // 5. Look for login/auth links specifically in common locations
      const authLinks = this.findAuthenticationLinks(document, targetUrl);
      securityLinks.push(...authLinks);

      // Remove duplicates and prioritize
      const uniqueLinks = this.deduplicateAndPrioritize(securityLinks);

      // Limit to maxUrls, prioritizing high-priority links
      const sortedLinks = uniqueLinks.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      const finalLinks = sortedLinks.slice(0, maxUrls);

      console.log(`📄 Found ${finalLinks.length} security-relevant URLs to scan`);
      console.log(
        `📊 Priority breakdown: High: ${finalLinks.filter((l) => l.priority === 'high').length}, Medium: ${finalLinks.filter((l) => l.priority === 'medium').length}, Low: ${finalLinks.filter((l) => l.priority === 'low').length}`,
      );

      return finalLinks;
    } catch (error) {
      console.error('❌ Error finding security-relevant URLs:', error);
      return [];
    }
  }

  /**
   * Find links in a specific HTML section (footer, header, nav)
   */
  private static findLinksInSection(
    document: Document,
    sectionType: string,
    baseUrl: string,
  ): SecurityLink[] {
    const sections = document.querySelectorAll(sectionType);
    const links: SecurityLink[] = [];

    sections.forEach((section) => {
      const sectionLinks = Array.from(section.querySelectorAll('a[href]'));

      sectionLinks.forEach((link) => {
        const href = link.getAttribute('href');
        const text = link.textContent?.trim() || '';

        if (href && text && this.isInternalLink(href, baseUrl)) {
          const absoluteUrl = this.resolveUrl(href, baseUrl);
          if (absoluteUrl) {
            const securityLink = this.createSecurityLink(
              absoluteUrl,
              text,
              sectionType as 'header' | 'footer' | 'nav' | 'main',
              href,
            );
            if (securityLink) {
              links.push(securityLink);
            }
          }
        }
      });
    });

    return links;
  }

  /**
   * Find links using CSS selectors
   */
  private static findLinksInSelector(
    document: Document,
    selector: string,
    baseUrl: string,
    location: 'header' | 'footer',
  ): SecurityLink[] {
    const links: SecurityLink[] = [];

    try {
      const elements = document.querySelectorAll(selector);

      elements.forEach((element) => {
        const sectionLinks = Array.from(element.querySelectorAll('a[href]'));

        sectionLinks.forEach((link) => {
          const href = link.getAttribute('href');
          const text = link.textContent?.trim() || '';

          if (href && text && this.isInternalLink(href, baseUrl)) {
            const absoluteUrl = this.resolveUrl(href, baseUrl);
            if (absoluteUrl) {
              const securityLink = this.createSecurityLink(absoluteUrl, text, location, href);
              if (securityLink) {
                links.push(securityLink);
              }
            }
          }
        });
      });
    } catch (error) {
      // Selector might not exist, continue
    }

    return links;
  }

  /**
   * Find authentication/login links specifically
   */
  private static findAuthenticationLinks(document: Document, baseUrl: string): SecurityLink[] {
    const links: SecurityLink[] = [];
    const allLinks = Array.from(document.querySelectorAll('a[href]'));

    // Authentication-specific patterns
    const authPatterns = [
      { pattern: /login|log\s*in|sign\s*in/i, type: 'login' as const, priority: 'high' as const },
      {
        pattern: /register|sign\s*up|create\s*account/i,
        type: 'login' as const,
        priority: 'high' as const,
      },
      { pattern: /account|profile|dashboard/i, type: 'login' as const, priority: 'high' as const },
      { pattern: /auth|authentication|oauth/i, type: 'login' as const, priority: 'high' as const },
      {
        pattern: /portal|patient\s*portal|member\s*portal/i,
        type: 'login' as const,
        priority: 'high' as const,
      },
      { pattern: /my\s*account|my\s*profile/i, type: 'login' as const, priority: 'high' as const },
    ];

    allLinks.forEach((link) => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';

      if (href && text && this.isInternalLink(href, baseUrl)) {
        const absoluteUrl = this.resolveUrl(href, baseUrl);
        if (absoluteUrl) {
          // Check text and href against auth patterns
          for (const { pattern, type, priority } of authPatterns) {
            if (pattern.test(text) || pattern.test(href)) {
              const securityLink: SecurityLink = {
                url: absoluteUrl,
                text,
                type,
                priority,
                location: 'main',
                accessible: true,
                format: this.detectLinkFormat(href),
              };
              links.push(securityLink);
              break; // Only match first pattern
            }
          }
        }
      }
    });

    return links;
  }

  /**
   * Validate if URL is properly formatted and accessible
   */
  private static isValidUrl(url: string): boolean {
    try {
      const parsed = new URL(url);

      // Skip certain file types that aren't useful for security scanning
      const skipExtensions = [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.svg',
        '.ico',
        '.css',
        '.js',
        '.woff',
        '.woff2',
        '.ttf',
      ];
      const pathname = parsed.pathname.toLowerCase();

      if (skipExtensions.some((ext) => pathname.endsWith(ext))) {
        return false;
      }

      // Skip anchor links to same page
      if (parsed.hash && !parsed.search && parsed.pathname === '/') {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Find specific security-related links anywhere on the page
   */
  private static findSpecificSecurityLinks(document: Document, baseUrl: string): SecurityLink[] {
    const links: SecurityLink[] = [];
    const allLinks = Array.from(document.querySelectorAll('a[href]'));

    // Security-related patterns
    const securityPatterns = [
      { pattern: /privacy\s*policy/i, type: 'privacy' as const, priority: 'high' as const },
      { pattern: /privacy\s*notice/i, type: 'privacy' as const, priority: 'high' as const },
      { pattern: /hipaa\s*notice/i, type: 'privacy' as const, priority: 'high' as const },
      {
        pattern: /terms\s*(of\s*service|and\s*conditions)/i,
        type: 'terms' as const,
        priority: 'high' as const,
      },
      { pattern: /security\s*policy/i, type: 'security' as const, priority: 'high' as const },
      { pattern: /login|sign\s*in/i, type: 'login' as const, priority: 'high' as const },
      { pattern: /contact\s*us/i, type: 'contact' as const, priority: 'medium' as const },
      { pattern: /admin|dashboard/i, type: 'admin' as const, priority: 'high' as const },
      { pattern: /api|developer/i, type: 'api' as const, priority: 'medium' as const },
      { pattern: /\bprivacy\b/i, type: 'privacy' as const, priority: 'medium' as const },
      { pattern: /\bterms\b/i, type: 'terms' as const, priority: 'medium' as const },
      { pattern: /\bsecurity\b/i, type: 'security' as const, priority: 'medium' as const },
    ];

    allLinks.forEach((link) => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';

      if (href && text && this.isInternalLink(href, baseUrl)) {
        const absoluteUrl = this.resolveUrl(href, baseUrl);
        if (absoluteUrl) {
          // Check text and href against patterns
          for (const { pattern, type, priority } of securityPatterns) {
            if (pattern.test(text) || pattern.test(href)) {
              const securityLink: SecurityLink = {
                url: absoluteUrl,
                text,
                type,
                priority,
                location: 'main',
                accessible: true,
                format: this.detectLinkFormat(href),
              };
              links.push(securityLink);
              break; // Only match first pattern
            }
          }
        }
      }
    });

    return links;
  }

  /**
   * Create a security link with proper classification
   */
  private static createSecurityLink(
    url: string,
    text: string,
    location: 'header' | 'footer' | 'nav' | 'main',
    href: string,
  ): SecurityLink | null {
    const type = this.classifyLinkType(text, href);
    const priority = this.determinePriority(type, location);

    return {
      url,
      text,
      type,
      priority,
      location,
      accessible: true,
      format: this.detectLinkFormat(href),
    };
  }

  /**
   * Classify link type based on text and href
   */
  private static classifyLinkType(text: string, href: string): SecurityLink['type'] {
    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    if (
      lowerText.includes('privacy') ||
      lowerHref.includes('privacy') ||
      lowerText.includes('hipaa')
    ) {
      return 'privacy';
    }
    if (lowerText.includes('terms') || lowerHref.includes('terms')) {
      return 'terms';
    }
    if (lowerText.includes('security') || lowerHref.includes('security')) {
      return 'security';
    }
    if (
      lowerText.includes('login') ||
      lowerText.includes('sign in') ||
      lowerHref.includes('login')
    ) {
      return 'login';
    }
    if (lowerText.includes('contact') || lowerHref.includes('contact')) {
      return 'contact';
    }
    if (
      lowerText.includes('admin') ||
      lowerText.includes('dashboard') ||
      lowerHref.includes('admin')
    ) {
      return 'admin';
    }
    if (lowerText.includes('api') || lowerHref.includes('api') || lowerText.includes('developer')) {
      return 'api';
    }

    // Footer/header links are generally important
    return 'important';
  }

  /**
   * Determine priority based on type and location
   */
  private static determinePriority(
    type: SecurityLink['type'],
    location: string,
  ): 'high' | 'medium' | 'low' {
    // High priority types
    if (['privacy', 'security', 'login', 'admin'].includes(type)) {
      return 'high';
    }

    // Medium priority types
    if (['terms', 'api', 'contact'].includes(type)) {
      return 'medium';
    }

    // Footer links are generally more important than header links
    if (location === 'footer') {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Remove duplicates and prioritize links
   */
  private static deduplicateAndPrioritize(links: SecurityLink[]): SecurityLink[] {
    const urlMap = new Map<string, SecurityLink>();

    links.forEach((link) => {
      const existing = urlMap.get(link.url);
      if (!existing || this.isHigherPriority(link, existing)) {
        urlMap.set(link.url, link);
      }
    });

    return Array.from(urlMap.values());
  }

  /**
   * Check if one link has higher priority than another
   */
  private static isHigherPriority(link1: SecurityLink, link2: SecurityLink): boolean {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[link1.priority] > priorityOrder[link2.priority];
  }

  /**
   * Check if a link is internal to the domain
   */
  private static isInternalLink(href: string, baseUrl: string): boolean {
    try {
      // Skip external links, mailto, tel, etc.
      if (href.startsWith('mailto:') || href.startsWith('tel:') || href.startsWith('javascript:')) {
        return false;
      }

      // If it's a relative link, it's internal
      if (!href.startsWith('http://') && !href.startsWith('https://')) {
        return true;
      }

      // Check if the domain matches
      const baseDomain = new URL(baseUrl).hostname;
      const linkDomain = new URL(href).hostname;

      return baseDomain === linkDomain;
    } catch {
      return false;
    }
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  private static resolveUrl(href: string, baseUrl: string): string | null {
    try {
      if (href.startsWith('http://') || href.startsWith('https://')) {
        return href;
      }

      const base = new URL(baseUrl);
      const resolved = new URL(href, base);
      return resolved.toString();
    } catch {
      return null;
    }
  }

  /**
   * Detect the format of a link
   */
  private static detectLinkFormat(href: string): SecurityLink['format'] {
    const lowerHref = href.toLowerCase();

    if (lowerHref.includes('.pdf')) return 'pdf';
    if (lowerHref.includes('.doc')) return 'doc';
    if (lowerHref.includes('.txt')) return 'txt';
    if (lowerHref.includes('/api/') || lowerHref.includes('.json')) return 'json';
    if (lowerHref.includes('.xml')) return 'xml';

    return 'html';
  }

  /**
   * Get a simple list of URLs for compatibility with existing systems
   */
  static async getSecurityUrls(targetUrl: string, maxUrls: number = 25): Promise<string[]> {
    const securityLinks = await this.findSecurityRelevantUrls(targetUrl, maxUrls);
    return securityLinks.map((link) => link.url);
  }
}
