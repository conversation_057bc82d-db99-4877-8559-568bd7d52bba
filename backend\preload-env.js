// D:\Web projects\Comply Checker\backend\preload-env.js
const dotenv = require('dotenv');
const path = require('path');

// Resolve the path to the root .env file from the 'backend' directory
const envPath = path.resolve(__dirname, '../.env');

// Load environment variables, overriding any existing ones from the process
const result = dotenv.config({ path: envPath, override: true });

if (result.error) {
  // Log an error if the .env file couldn't be loaded, but don't throw
  // as the application might still run if critical vars are set externally.
  console.error(
    `[Preload ENV] Warning: Could not load .env file from ${envPath}. Error: ${result.error.message}`,
  );
}
