// backend/src/compliance/hipaa/privacy/utils/content-analyzer.ts

import { JSDOM } from 'jsdom';
import { ContentSection, StructureAnalysis, PrivacyPolicyLink } from '../types';

/**
 * Content analysis utilities for HIPAA privacy policy processing
 * Prepares content for the 3-level analysis workflow
 */
export class ContentAnalyzer {
  /**
   * Extracts clean text from HTML content
   * Removes HTML tags, scripts, styles and returns readable text
   */
  static extractText(html: string): string {
    console.log('📄 [Content Extraction] Starting text extraction from HTML');
    console.log('📊 [Content Extraction] Original HTML length:', html.length);

    try {
      // Check for common security blocks or error pages
      const securityBlocks = [
        /incapsula/i,
        /cloudflare/i,
        /access denied/i,
        /blocked/i,
        /security check/i,
        /bot protection/i,
        /request unsuccessful/i,
        /incident id/i,
      ];

      const isBlocked = securityBlocks.some((pattern) => pattern.test(html));
      if (isBlocked) {
        console.warn('⚠️ [Content Extraction] Security block or error page detected');
        const fallbackText = html
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        console.log('🔄 [Content Extraction] Returning blocked content with warning:', {
          fallbackTextLength: fallbackText.length,
          isSecurityBlock: true,
        });

        return (
          fallbackText +
          ' [WARNING: Content may be blocked by security measures. This may indicate bot protection (Cloudflare, Incapsula, etc.) preventing automated access to privacy policy pages.]'
        );
      }

      const dom = new JSDOM(html);
      const document = dom.window.document;

      // Count elements before removal
      const totalElements = document.querySelectorAll('*').length;
      const scriptsAndStyles = document.querySelectorAll('script, style, noscript');
      console.log('🧹 [Content Extraction] Found elements to remove:', {
        totalElements,
        scriptsAndStyles: scriptsAndStyles.length,
      });

      // Remove script and style elements
      scriptsAndStyles.forEach((element) => element.remove());

      // Get text content and clean it up
      const rawTextContent = document.body?.textContent || document.textContent || '';
      console.log('📝 [Content Extraction] Raw text content length:', rawTextContent.length);

      // Clean up whitespace and normalize line breaks
      const cleanedText = rawTextContent
        .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
        .replace(/\n\s*\n/g, '\n') // Remove empty lines
        .trim();

      console.log('✅ [Content Extraction] Text extraction complete:', {
        originalHtmlLength: html.length,
        rawTextLength: rawTextContent.length,
        cleanedTextLength: cleanedText.length,
        compressionRatio:
          (((html.length - cleanedText.length) / html.length) * 100).toFixed(1) + '%',
        wordCount: cleanedText.split(/\s+/).length,
        lineCount: cleanedText.split('\n').length,
      });

      return cleanedText;
    } catch (error) {
      console.error('❌ [Content Extraction] Error extracting text from HTML:', error);
      const fallbackText = html
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      console.log('🔄 [Content Extraction] Using fallback extraction:', {
        fallbackTextLength: fallbackText.length,
      });

      return fallbackText;
    }
  }

  /**
   * Identifies different sections in the privacy policy content
   * Returns organized sections with titles and content
   */
  static findSections(content: string): ContentSection[] {
    const sections: ContentSection[] = [];

    // Split content by common section patterns
    const sectionPatterns = [
      /^(notice of privacy practices|privacy notice|hipaa privacy notice)/im,
      /^(your rights|individual rights|patient rights)/im,
      /^(uses? and disclosures?|how we use|sharing)/im,
      /^(contact|privacy officer|complaints?)/im,
      /^(effective date|last updated|revision)/im,
      /^(definitions?|what is|protected health)/im,
      /^(amendments?|changes|updates?)/im,
    ];

    const lines = content.split('\n');
    let currentSection: ContentSection | null = null;
    let sectionContent: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Check if this line starts a new section
      const isNewSection = sectionPatterns.some((pattern) => pattern.test(line));

      if (isNewSection) {
        // Save previous section if exists
        if (currentSection) {
          currentSection.content = sectionContent.join('\n').trim();
          sections.push(currentSection);
        }

        // Start new section
        currentSection = {
          title: line,
          content: '',
          startPosition: content.indexOf(line),
          endPosition: 0,
          level: this.getHeadingLevel(line),
          subsections: [],
        };
        sectionContent = [];
      } else if (currentSection) {
        sectionContent.push(line);
      }
    }

    // Add the last section
    if (currentSection) {
      currentSection.content = sectionContent.join('\n').trim();
      currentSection.endPosition = currentSection.startPosition + currentSection.content.length;
      sections.push(currentSection);
    }

    return sections;
  }

  /**
   * Analyzes the structure and organization of the content
   */
  static analyzeStructure(content: string): StructureAnalysis {
    const sections = this.findSections(content);
    const lines = content.split('\n').filter((line) => line.trim());
    const lineCount = lines.length;

    // Analyze headings
    const headingLevels = sections.map((section) => section.level);
    const hasProperHeadings = headingLevels.length > 0 && headingLevels.every((level) => level > 0);

    // Calculate organization score
    const organizationScore = this.calculateOrganizationScore(sections, lineCount);

    // Calculate readability score (basic)
    const readabilityScore = this.calculateBasicReadabilityScore(content);

    return {
      hasProperHeadings,
      headingLevels,
      sectionsCount: sections.length,
      averageSectionLength:
        sections.length > 0
          ? sections.reduce((sum, section) => sum + section.content.length, 0) / sections.length
          : 0,
      organizationScore,
      readabilityScore,
    };
  }

  /**
   * Extracts all links from HTML content
   */
  static extractLinks(html: string): PrivacyPolicyLink[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const links = Array.from(document.querySelectorAll('a[href]'));

      return links
        .map((link) => {
          const href = link.getAttribute('href') || '';
          const text = link.textContent?.trim() || '';

          return {
            url: href,
            text,
            type: this.classifyLinkType(text, href),
            accessible: true, // Will be validated separately
            format: this.detectLinkFormat(href),
          };
        })
        .filter((link) => link.url && link.text);
    } catch (error) {
      console.error('Error extracting links from HTML:', error);
      return [];
    }
  }

  /**
   * Determines the heading level of a text line
   */
  private static getHeadingLevel(text: string): number {
    // Check for markdown-style headings
    const markdownMatch = text.match(/^(#{1,6})\s/);
    if (markdownMatch) {
      return markdownMatch[1].length;
    }

    // Check for common heading patterns
    if (text.length < 100 && text.toUpperCase() === text) {
      return 1; // All caps, short text likely a heading
    }

    if (text.endsWith(':') && text.length < 50) {
      return 2; // Colon-ended, short text likely a subheading
    }

    return 0; // Not a heading
  }

  /**
   * Calculates organization score based on section structure
   */
  private static calculateOrganizationScore(
    sections: ContentSection[],
    _lineCount: number,
  ): number {
    let score = 0;

    // Points for having sections
    if (sections.length >= 3) score += 30;
    else if (sections.length >= 2) score += 20;
    else if (sections.length >= 1) score += 10;

    // Points for logical section order
    const expectedOrder = [/notice|header/i, /rights/i, /uses|disclosures/i, /contact/i];

    let orderScore = 0;
    for (let i = 0; i < Math.min(sections.length, expectedOrder.length); i++) {
      if (expectedOrder[i].test(sections[i].title)) {
        orderScore += 10;
      }
    }
    score += orderScore;

    // Points for proper heading hierarchy
    const headingLevels = sections.map((s) => s.level).filter((l) => l > 0);
    if (headingLevels.length > 0) {
      const hasHierarchy = headingLevels.every(
        (level, i) => i === 0 || level >= headingLevels[i - 1] - 1,
      );
      if (hasHierarchy) score += 20;
    }

    // Points for reasonable section lengths
    const avgLength = sections.reduce((sum, s) => sum + s.content.length, 0) / sections.length;
    if (avgLength > 100 && avgLength < 2000) score += 20;

    return Math.min(score, 100);
  }

  /**
   * Calculates basic readability score
   */
  private static calculateBasicReadabilityScore(content: string): number {
    const sentences = content.split(/[.!?]+/).filter((s) => s.trim().length > 0);
    const words = content.split(/\s+/).filter((w) => w.trim().length > 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / words.length;

    // Simple readability calculation (inverse of complexity)
    let score = 100;

    // Penalize long sentences
    if (avgWordsPerSentence > 20) score -= 20;
    else if (avgWordsPerSentence > 15) score -= 10;

    // Penalize long words
    if (avgCharsPerWord > 6) score -= 20;
    else if (avgCharsPerWord > 5) score -= 10;

    return Math.max(score, 0);
  }

  /**
   * Classifies the type of link based on text and URL
   */
  private static classifyLinkType(
    text: string,
    href: string,
  ): 'privacy_policy' | 'privacy_notice' | 'hipaa_notice' {
    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    if (lowerText.includes('hipaa') || lowerHref.includes('hipaa')) {
      return 'hipaa_notice';
    }

    if (lowerText.includes('notice') || lowerHref.includes('notice')) {
      return 'privacy_notice';
    }

    return 'privacy_policy';
  }

  /**
   * Detects the format of a link based on URL
   */
  private static detectLinkFormat(href: string): 'html' | 'pdf' | 'doc' | 'txt' {
    const lowerHref = href.toLowerCase();

    if (lowerHref.endsWith('.pdf')) return 'pdf';
    if (lowerHref.endsWith('.doc') || lowerHref.endsWith('.docx')) return 'doc';
    if (lowerHref.endsWith('.txt')) return 'txt';

    return 'html';
  }

  /**
   * Extract page title from HTML content
   * @param htmlContent - The HTML content to analyze
   * @returns string - The page title or empty string if not found
   */
  static extractPageTitle(htmlContent: string): string {
    try {
      const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
      if (titleMatch && titleMatch[1]) {
        return titleMatch[1].trim().replace(/\s+/g, ' ');
      }
      return '';
    } catch (error) {
      console.warn('Failed to extract page title:', error);
      return '';
    }
  }

  /**
   * Detect the language of the content
   * @param htmlContent - The HTML content to analyze
   * @returns string - The detected language code or 'en' as default
   */
  static detectLanguage(htmlContent: string): string {
    try {
      // Check for lang attribute in html tag
      const langMatch = htmlContent.match(/<html[^>]*lang\s*=\s*["']([^"']+)["']/i);
      if (langMatch && langMatch[1]) {
        return langMatch[1].toLowerCase();
      }

      // Check for meta language tags
      const metaLangMatch = htmlContent.match(
        /<meta[^>]*http-equiv\s*=\s*["']content-language["'][^>]*content\s*=\s*["']([^"']+)["']/i,
      );
      if (metaLangMatch && metaLangMatch[1]) {
        return metaLangMatch[1].toLowerCase();
      }

      // Default to English
      return 'en';
    } catch (error) {
      console.warn('Failed to detect language:', error);
      return 'en';
    }
  }
}
