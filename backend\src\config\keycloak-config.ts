import session from 'express-session';
import Keycloak from 'keycloak-connect';
import fs from 'fs';
import path from 'path';

let _keycloak: Keycloak.Keycloak | undefined;

// For development, using a simple memory store.
// For production, a more robust store like connect-redis or connect-mongo should be used.
/**
 * In-memory session store for Express sessions.
 * Suitable for development environments.
 * For production, a more persistent and scalable store like connect-redis or connect-mongo should be used.
 */
const memoryStore = new session.MemoryStore();

const getKeycloakInstance = (): Keycloak.Keycloak => {
  if (!_keycloak) {
    console.log('Initializing Keycloak from keycloak-config.ts...');
    // Path to keycloak.json, assuming it's in the backend root directory
    const keycloakJsonPath = path.resolve(process.cwd(), 'keycloak.json'); // Assumes CWD is /app, resolves to /app/keycloak.json

    if (!fs.existsSync(keycloakJsonPath)) {
      const configuredPathForErrorMessage =
        'D:\\Web projects\\Comply Checker\\backend\\keycloak.json'; // For error message clarity
      const errorMessage = `ERROR: keycloak.json not found at ${keycloakJsonPath} (expected at project root in container, originating from ${configuredPathForErrorMessage}). Please ensure keycloak.json is configured and present in the backend root directory.`;
      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    // Load configuration from keycloak.json
    const keycloakConfig = JSON.parse(fs.readFileSync(keycloakJsonPath, 'utf8'));

    _keycloak = new Keycloak({ store: memoryStore }, keycloakConfig as Keycloak.KeycloakConfig);
    console.log('Keycloak instance created using keycloak-config.ts.');
  }
  return _keycloak;
};

export { getKeycloakInstance, memoryStore };
