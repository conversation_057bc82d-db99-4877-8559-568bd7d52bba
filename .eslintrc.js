module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'prettier'],
  ignorePatterns: ['**/dist/**', '**/node_modules/**', '**/.next/**'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier', // Uses eslint-config-prettier to disable ESLint rules from @typescript-eslint/eslint-plugin that would conflict with prettier
    'plugin:prettier/recommended', // Enables eslint-plugin-prettier and displays prettier errors as ESLint errors. Make sure this is always the last configuration in the extends array.
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  env: {
    node: true,
    es6: true,
  },
  rules: {
    'prettier/prettier': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn', // Rule: avoid using 'any'
    '@typescript-eslint/naming-convention': [
      'warn',
      {
        selector: 'variable',
        format: ['camelCase', 'UPPER_CASE', 'PascalCase'], // PascalCase for some React components or constants
        leadingUnderscore: 'allow',
      },
      {
        selector: 'function',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      {
        selector: 'method',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      {
        selector: 'parameter',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      {
        selector: 'typeLike', // class, interface, typeAlias, enum, typeParameter
        format: ['PascalCase'],
      },
    ],
    'no-restricted-imports': 'off', // Handled by specific frontend/backend configs if needed
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // Rule: avoid magic numbers; define constants
    '@typescript-eslint/no-magic-numbers': [
      'off', // Often too restrictive, enable with care or per-project basis
      {
        ignore: [-1, 0, 1, 2, 100, 1000],
        ignoreArrayIndexes: true,
        enforceConst: true,
        detectObjects: false,
      },
    ],
    // Rule: use JSDoc for public classes/methods - basic check
    'require-jsdoc': [
      'warn',
      {
        require: {
          FunctionDeclaration: true,
          MethodDefinition: true,
          ClassDeclaration: true,
          ArrowFunctionExpression: false,
          FunctionExpression: false,
        },
      },
    ],
    'valid-jsdoc': [
      'warn',
      {
        prefer: { return: 'returns' },
        requireReturn: false, // Don't require @return if function doesn't return anything
        requireParamDescription: true,
        requireReturnDescription: true,
      },
    ],
    // Rule: one export per file (partially covered by module systems, more complex to enforce strictly with ESLint for all cases)
    // 'import/prefer-default-export': 'off', // if using default exports
    // 'import/no-default-export': 'warn', // if preferring named exports
  },
  overrides: [
    {
      files: ['*.js'],
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
      },
    },
    {
      files: ['.eslintrc.js', '*.config.js'],
      parserOptions: {
        sourceType: 'module',
      },
    },
    {
      files: ['migrations/*.ts', 'seeds/*.ts'],
      rules: {
        'require-jsdoc': 'off', // Disable JSDoc requirement for migrations and seeds
      },
    },
  ],
};
