# Comply Checker Theme System Documentation

## Overview

The Comply Checker theme system provides a comprehensive, WCAG AA compliant theming solution that ensures consistent design across the entire application. The system supports three distinct theme modes while maintaining strict adherence to the established design system and project rules.

## Available Theme Modes

### 1. Standard Theme (Default)
**Purpose**: Light mode with design system colors  
**Default**: ✅ Yes  
**WCAG AA Compliant**: ✅ Yes

#### Color Palette
```typescript
// Primary Colors
Primary Blue:     #0055A4  // 4.5:1 contrast ratio
Primary Light:    #1E6BB8  // Hover states
Primary Dark:     #003D7A  // Active states
Primary Foreground: #FFFFFF // Text on primary

// Accent Colors  
Accent Purple:    #663399  // Secondary actions
Accent Light:     #7A4DB8  // Hover states
Accent Dark:      #4D2673  // Active states
Accent Foreground: #FFFFFF // Text on accent

// Background Colors
Background:       #F5F5F5  // Page background
Background Secondary: #FFFFFF // Card backgrounds
Background Tertiary: #FAFAFA // Section backgrounds

// Text Colors
Text Primary:     #333333  // 12.6:1 contrast ratio
Text Secondary:   #666666  // 7:1 contrast ratio  
Text Tertiary:    #999999  // 4.5:1 contrast ratio

// Border Colors
Border:           #E5E5E5  // Standard borders
Border Light:     #F0F0F0  // Subtle borders
Border Dark:      #CCCCCC  // Emphasized borders

// Status Colors (Project Rules Compliant)
Success (Low Risk):    #059669  // Green
Warning (Medium Risk): #D97706  // Yellow
Error (Critical Risk): #DC2626  // Red
High Risk:            #EA580C  // Orange
Info:                 #3B82F6  // Blue
```

### 2. Dark Theme
**Purpose**: WCAG AA compliant dark mode  
**Default**: ❌ No  
**WCAG AA Compliant**: ✅ Yes

#### Color Palette
```typescript
// Primary Colors (Adjusted for dark backgrounds)
Primary Blue:     #4A9EFF  // Lighter for visibility
Primary Light:    #6BB3FF  // Hover states
Primary Dark:     #2E8BFF  // Active states
Primary Foreground: #1E2329 // Dark text on light blue

// Accent Colors (Adjusted for dark backgrounds)
Accent Purple:    #A855F7  // Lighter for visibility
Accent Light:     #C084FC  // Hover states
Accent Dark:      #9333EA  // Active states
Accent Foreground: #1E2329 // Dark text on light purple

// Background Colors
Background:       #1E2329  // Dark blue-gray
Background Secondary: #252B32 // Card backgrounds
Background Tertiary: #2F363E  // Section backgrounds

// Text Colors (High contrast for dark backgrounds)
Text Primary:     #EEEFF1  // High contrast light
Text Secondary:   #A8ACB0  // Medium contrast
Text Tertiary:    #6B7280  // Lower contrast

// Border Colors
Border:           #424952  // Dark borders
Border Light:     #353C45  // Subtle dark borders
Border Dark:      #2F363E  // Emphasized dark borders

// Status Colors (Adjusted for dark backgrounds)
Success:          #4ADE80  // Light green
Warning:          #FBBF24  // Light orange
Error:            #F87171  // Light red
Info:             #60A5FA  // Light blue
```

### 3. System Theme
**Purpose**: Automatically follows user's OS preference  
**Default**: ❌ No  
**WCAG AA Compliant**: ✅ Yes (inherits from Standard/Dark)

**Behavior**: 
- Detects `prefers-color-scheme: dark` media query
- Automatically applies Dark theme if OS is in dark mode
- Automatically applies Standard theme if OS is in light mode
- Updates dynamically when user changes OS theme preference

## Implementation Guidelines

### Basic Usage

#### 1. Using Theme Hooks
```typescript
import { useThemeColors, useButtonStyles, useCardStyles } from '@/hooks/useThemeColors';

function MyComponent() {
  const colors = useThemeColors();
  const buttonStyles = useButtonStyles();
  const cardStyles = useCardStyles();
  
  return (
    <div style={{ backgroundColor: colors.background }}>
      <button style={buttonStyles.primary.style}>
        Primary Action
      </button>
    </div>
  );
}
```

#### 2. Using Theme Wrapper Components
```typescript
import { 
  PageWrapper, 
  CardWrapper, 
  ButtonWrapper 
} from '@/components/layout/theme-enforcer';

function MyPage() {
  return (
    <PageWrapper>
      <CardWrapper variant="elevated">
        <h1>Card Content</h1>
        <ButtonWrapper variant="primary">
          Action Button
        </ButtonWrapper>
      </CardWrapper>
    </PageWrapper>
  );
}
```

#### 3. Using CSS Custom Properties
```css
.my-component {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.my-button {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
}
```

### Advanced Usage

#### 1. Theme Detection
```typescript
import { useTheme } from 'next-themes';
import { useThemeColors } from '@/hooks/useThemeColors';

function ThemeAwareComponent() {
  const { theme, resolvedTheme } = useTheme();
  const { isDark, isStandard, themeName } = useThemeColors();
  
  if (isDark) {
    // Dark theme specific logic
  }
  
  if (isStandard) {
    // Standard theme specific logic
  }
}
```

#### 2. Dynamic Theme Switching
```typescript
import { useTheme } from 'next-themes';

function ThemeSwitcher() {
  const { setTheme } = useTheme();
  
  return (
    <div>
      <button onClick={() => setTheme('standard')}>Standard</button>
      <button onClick={() => setTheme('dark')}>Dark</button>
      <button onClick={() => setTheme('system')}>System</button>
    </div>
  );
}
```

## WCAG AA Compliance Details

### Contrast Ratios
All color combinations meet or exceed WCAG AA requirements:

**Standard Theme**:
- Primary text (#333333) on white background: **12.6:1** (AAA)
- Secondary text (#666666) on white background: **7:1** (AAA)
- Primary blue (#0055A4) on white background: **4.5:1** (AA)

**Dark Theme**:
- Primary text (#EEEFF1) on dark background: **12.8:1** (AAA)
- Secondary text (#A8ACB0) on dark background: **7.2:1** (AAA)
- Primary blue (#4A9EFF) on dark background: **4.6:1** (AA)

### Accessibility Features
- **Focus Indicators**: 2px outline with 3:1 contrast ratio
- **Touch Targets**: Minimum 44px for mobile accessibility
- **Screen Reader Support**: Comprehensive ARIA labels
- **Keyboard Navigation**: Full keyboard accessibility
- **Reduced Motion**: Respects `prefers-reduced-motion`
- **High Contrast**: Supports `prefers-contrast: high`

## Migration Guide

### Updating Existing Components

#### Step 1: Identify Non-Compliant Elements
Look for:
- Hardcoded colors (`#000000`, `#FFFFFF`, `black`, `white`)
- Missing hover states
- Non-design-system colors

#### Step 2: Replace with Theme System
```typescript
// Before: Hardcoded styling
<Button className="bg-black text-white">Action</Button>

// After: Theme system compliant
import { useButtonStyles } from '@/hooks/useThemeColors';

function MyComponent() {
  const buttonStyles = useButtonStyles();
  
  return (
    <button style={buttonStyles.primary.style}>
      Action
    </button>
  );
}
```

#### Step 3: Add Interactive States
```typescript
// Before: No hover states
<div className="bg-white border">Content</div>

// After: With hover states
import { useCardStyles } from '@/hooks/useThemeColors';

function MyComponent() {
  const cardStyles = useCardStyles();
  
  return (
    <div style={cardStyles.default.style} className={cardStyles.default.className}>
      Content
    </div>
  );
}
```

### Common Migration Patterns

#### 1. Card Components
```typescript
// Before
<div className="bg-white border border-gray-200 rounded-lg p-6">
  Content
</div>

// After
<CardWrapper variant="default">
  Content
</CardWrapper>
```

#### 2. Button Components
```typescript
// Before
<button className="bg-blue-500 text-white px-4 py-2 rounded">
  Action
</button>

// After
<ButtonWrapper variant="primary">
  Action
</ButtonWrapper>
```

#### 3. Text Components
```typescript
// Before
<h1 className="text-black text-2xl font-bold">Title</h1>
<p className="text-gray-600">Description</p>

// After
import { useTextStyles } from '@/hooks/useThemeColors';

function MyComponent() {
  const textStyles = useTextStyles();
  
  return (
    <>
      <h1 style={textStyles.heading.style} className="text-2xl font-bold">
        Title
      </h1>
      <p style={textStyles.secondary.style}>
        Description
      </p>
    </>
  );
}
```

## Best Practices

### 1. Always Use Theme System
- **DO**: Use theme hooks and wrapper components
- **DON'T**: Use hardcoded colors or Tailwind color classes

### 2. Maintain Consistency
- **DO**: Use established color variables
- **DON'T**: Create custom colors outside the design system

### 3. Test Accessibility
- **DO**: Test all themes with accessibility tools
- **DON'T**: Assume colors work without testing contrast ratios

### 4. Handle Theme Changes
- **DO**: Use CSS transitions for smooth theme switching
- **DON'T**: Create jarring theme transitions

### 5. Document Custom Components
- **DO**: Document any theme-specific behavior
- **DON'T**: Create components without theme documentation

## File Structure

```
frontend/
├── lib/
│   └── theme-config.ts          # Theme configuration and utilities
├── hooks/
│   └── useThemeColors.ts        # Theme hooks and utilities
├── components/
│   └── layout/
│       ├── theme-provider.tsx   # Theme provider component
│       ├── theme-toggle.tsx     # Theme selection UI
│       └── theme-enforcer.tsx   # Theme wrapper components
└── styles/
    ├── globals.css              # Global theme variables
    └── design-system.css        # Design system implementation
```

## Troubleshooting

### Common Issues

#### 1. Theme Not Applying
**Problem**: Components not using theme colors  
**Solution**: Ensure ThemeProvider wraps your app and use theme hooks

#### 2. Contrast Issues
**Problem**: Text not readable in certain themes  
**Solution**: Use established text color variables, test with accessibility tools

#### 3. Theme Toggle Not Working
**Problem**: Theme selection not persisting  
**Solution**: Check localStorage permissions and ThemeProvider configuration

#### 4. CSS Variables Not Working
**Problem**: `var()` functions returning fallback values  
**Solution**: Ensure design-system.css is imported and CSS variables are defined

### Performance Considerations
- Theme switching is optimized with CSS transitions
- CSS custom properties provide efficient theme updates
- Theme detection uses efficient media queries
- Component re-renders are minimized with useMemo hooks

---

**Last Updated**: December 2024  
**Version**: 1.0  
**Maintained by**: Comply Checker Development Team

## API Reference

### Theme Configuration Types

```typescript
// Theme name union type
type ThemeName = 'standard' | 'dark' | 'system';

// Complete theme color interface
interface ThemeColors {
  // Primary Colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  primaryForeground: string;

  // Accent Colors
  accent: string;
  accentLight: string;
  accentDark: string;
  accentForeground: string;

  // Background Colors
  background: string;
  backgroundSecondary: string;
  backgroundTertiary: string;

  // Text Colors
  textPrimary: string;
  textSecondary: string;
  textTertiary: string;

  // Border Colors
  border: string;
  borderLight: string;
  borderDark: string;

  // Status Colors
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  error: string;
  errorForeground: string;
  info: string;
  infoForeground: string;

  // Component Colors
  card: string;
  cardForeground: string;
  input: string;
  inputForeground: string;
  muted: string;
  mutedForeground: string;

  // Interactive States
  hover: string;
  focus: string;
  active: string;
}
```

### Available Hooks

#### `useThemeColors()`
Returns current theme colors and theme state information.

```typescript
const {
  // All theme colors
  primary, accent, background, textPrimary, // ... etc

  // Theme state
  themeName,    // 'standard' | 'dark' | 'system'
  isDark,       // boolean
  isStandard,   // boolean
  isSystem      // boolean
} = useThemeColors();
```

#### `useStatusColors()`
Returns status and risk level colors for badges and indicators.

```typescript
const statusColors = useStatusColors();
// Returns: { success, warning, error, info, critical, high, medium, low }
```

#### `useButtonStyles()`
Returns pre-configured button styles for different variants.

```typescript
const buttonStyles = useButtonStyles();
// Returns: { primary, secondary, accent, ghost }
```

#### `useCardStyles()`
Returns pre-configured card styles for different variants.

```typescript
const cardStyles = useCardStyles();
// Returns: { default, elevated, muted }
```

### Available Components

#### `<ThemeProvider>`
Root theme provider component.

```typescript
<ThemeProvider>
  {children}
</ThemeProvider>
```

#### `<ThemeEnforcer>`
Ensures theme consistency across the application.

```typescript
<ThemeEnforcer>
  {children}
</ThemeEnforcer>
```

#### `<PageWrapper>`
Theme-aware page wrapper.

```typescript
<PageWrapper className="custom-class" style={customStyle}>
  {children}
</PageWrapper>
```

#### `<CardWrapper>`
Theme-aware card wrapper.

```typescript
<CardWrapper variant="default" | "elevated" | "muted">
  {children}
</CardWrapper>
```

#### `<ButtonWrapper>`
Theme-aware button wrapper.

```typescript
<ButtonWrapper
  variant="primary" | "secondary" | "accent" | "ghost"
  onClick={handleClick}
  disabled={false}
>
  {children}
</ButtonWrapper>
```

#### `<ThemeToggle>`
Theme selection dropdown component.

```typescript
<ThemeToggle />
```

## Testing Guidelines

### Unit Testing Themes

```typescript
import { render } from '@testing-library/react';
import { ThemeProvider } from '@/components/layout/theme-provider';

// Test component with different themes
const renderWithTheme = (component: React.ReactElement, theme: ThemeName) => {
  return render(
    <ThemeProvider defaultTheme={theme}>
      {component}
    </ThemeProvider>
  );
};

test('component renders correctly in standard theme', () => {
  const { container } = renderWithTheme(<MyComponent />, 'standard');
  // Test standard theme rendering
});

test('component renders correctly in dark theme', () => {
  const { container } = renderWithTheme(<MyComponent />, 'dark');
  // Test dark theme rendering
});
```

### Accessibility Testing

```typescript
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

test('component meets accessibility standards in all themes', async () => {
  const themes: ThemeName[] = ['standard', 'dark'];

  for (const theme of themes) {
    const { container } = renderWithTheme(<MyComponent />, theme);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  }
});
```

### Visual Regression Testing

```typescript
// Example with Storybook and Chromatic
export default {
  title: 'Components/MyComponent',
  component: MyComponent,
  parameters: {
    themes: {
      default: 'standard',
      list: [
        { name: 'standard', class: '', color: '#F5F5F5' },
        { name: 'dark', class: 'dark', color: '#1E2329' },
      ],
    },
  },
};

export const Standard = () => <MyComponent />;
export const Dark = () => <MyComponent />;
Dark.parameters = { theme: 'dark' };
```

## Deployment Considerations

### Environment Variables
No environment variables required for theme system.

### Build Optimization
- CSS custom properties are optimized during build
- Theme switching uses efficient CSS transitions
- No runtime theme compilation required

### Browser Support
- **Modern Browsers**: Full support (Chrome 49+, Firefox 31+, Safari 9.1+)
- **Legacy Browsers**: Graceful degradation with fallback colors
- **CSS Custom Properties**: Required for full functionality

### Performance Metrics
- **Theme Switch Time**: < 200ms
- **Initial Load Impact**: < 5KB additional CSS
- **Runtime Memory**: Minimal impact with CSS custom properties

*This documentation ensures consistent, accessible, and maintainable theming across the Comply Checker platform. All developers must follow these guidelines to maintain design system compliance.*
