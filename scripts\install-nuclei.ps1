# PowerShell script to install Nuclei vulnerability scanner
# This script downloads and installs Nuclei for Windows

Write-Host "🔍 Installing Nuclei Vulnerability Scanner..." -ForegroundColor Green

# Create tools directory if it doesn't exist
$toolsDir = ".\tools"
if (!(Test-Path $toolsDir)) {
    New-Item -ItemType Directory -Path $toolsDir -Force
    Write-Host "📁 Created tools directory: $toolsDir" -ForegroundColor Yellow
}

# Set Nuclei installation path
$nucleiDir = "$toolsDir\nuclei"
$nucleiExe = "$nucleiDir\nuclei.exe"

# Check if Nuclei is already installed
if (Test-Path $nucleiExe) {
    Write-Host "✅ Nuclei is already installed at: $nucleiExe" -ForegroundColor Green
    
    # Check version
    try {
        $version = & $nucleiExe -version 2>$null
        Write-Host "📋 Current version: $version" -ForegroundColor Cyan
    } catch {
        Write-Host "⚠️ Could not determine Nuclei version" -ForegroundColor Yellow
    }
    
    $response = Read-Host "Do you want to update to the latest version? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "🚀 Using existing Nuclei installation" -ForegroundColor Green
        exit 0
    }
}

# Create Nuclei directory
if (!(Test-Path $nucleiDir)) {
    New-Item -ItemType Directory -Path $nucleiDir -Force
}

try {
    Write-Host "📥 Downloading latest Nuclei release..." -ForegroundColor Yellow
    
    # Get latest release info from GitHub API
    $apiUrl = "https://api.github.com/repos/projectdiscovery/nuclei/releases/latest"
    $release = Invoke-RestMethod -Uri $apiUrl
    
    # Find Windows AMD64 asset
    $asset = $release.assets | Where-Object { $_.name -like "*windows_amd64.zip" }
    
    if (!$asset) {
        throw "Could not find Windows AMD64 release asset"
    }
    
    Write-Host "📋 Found release: $($release.tag_name)" -ForegroundColor Cyan
    Write-Host "📋 Asset: $($asset.name)" -ForegroundColor Cyan
    
    # Download the release
    $downloadUrl = $asset.browser_download_url
    $zipPath = "$toolsDir\nuclei.zip"
    
    Write-Host "⬇️ Downloading from: $downloadUrl" -ForegroundColor Yellow
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
    
    Write-Host "📦 Extracting Nuclei..." -ForegroundColor Yellow
    
    # Extract the zip file
    Expand-Archive -Path $zipPath -DestinationPath $nucleiDir -Force
    
    # Clean up zip file
    Remove-Item $zipPath -Force
    
    # Verify installation
    if (Test-Path $nucleiExe) {
        Write-Host "✅ Nuclei installed successfully!" -ForegroundColor Green
        
        # Get version
        try {
            $version = & $nucleiExe -version 2>$null
            Write-Host "📋 Installed version: $version" -ForegroundColor Cyan
        } catch {
            Write-Host "⚠️ Could not determine Nuclei version, but binary exists" -ForegroundColor Yellow
        }
        
        # Update templates
        Write-Host "📥 Updating Nuclei templates..." -ForegroundColor Yellow
        try {
            & $nucleiExe -update-templates 2>$null
            Write-Host "✅ Templates updated successfully!" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Could not update templates automatically" -ForegroundColor Yellow
            Write-Host "💡 You can update them later with: nuclei -update-templates" -ForegroundColor Cyan
        }
        
        Write-Host ""
        Write-Host "🎉 Nuclei installation completed!" -ForegroundColor Green
        Write-Host "📋 Binary location: $nucleiExe" -ForegroundColor Cyan
        Write-Host "💡 Add to PATH or set NUCLEI_PATH environment variable" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "🔧 To use with Comply Checker, set environment variable:" -ForegroundColor Yellow
        Write-Host "   NUCLEI_PATH=$nucleiExe" -ForegroundColor White
        Write-Host ""
        
    } else {
        throw "Nuclei binary not found after extraction"
    }
    
} catch {
    Write-Host "❌ Failed to install Nuclei: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Manual installation steps:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://github.com/projectdiscovery/nuclei/releases" -ForegroundColor White
    Write-Host "2. Download the Windows AMD64 release" -ForegroundColor White
    Write-Host "3. Extract to: $nucleiDir" -ForegroundColor White
    Write-Host "4. Set NUCLEI_PATH environment variable" -ForegroundColor White
    exit 1
}
