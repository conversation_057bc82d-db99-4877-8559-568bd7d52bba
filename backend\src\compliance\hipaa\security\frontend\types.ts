/**
 * HIPAA Security Frontend Types - Enhanced Version
 * Updated based on validation results for better reliability and UX
 */

// Enhanced scan progress tracking
export interface ScanProgress {
  phase: 'initializing' | 'discovery' | 'testing' | 'analysis' | 'complete' | 'error';
  progress: number; // 0-100
  currentTest: string;
  estimatedTimeRemaining: number;
  testsCompleted: number;
  totalTests: number;
  startTime: Date;
  timeouts: string[];
  retries: number;
}

// Enhanced error handling
export interface ScanError {
  type: 'network' | 'timeout' | 'validation' | 'server' | 'unknown';
  message: string;
  details?: string;
  recoverable: boolean;
  suggestedAction: string;
  timestamp: Date;
  context?: {
    phase?: string;
    test?: string;
    url?: string;
  };
}

// Enhanced configuration with reliability settings
export interface EnhancedScanConfig {
  targetUrl: string;
  protectedEndpoints: string[];
  publicEndpoints: string[];
  pagesToScan: string[];

  // Enhanced timeout configuration
  timeouts: {
    sslAnalysis: number;
    zapScanning: number;
    totalScan: number;
    individualTest: number;
  };

  // Reliability settings
  reliability: {
    maxRetries: number;
    retryDelay: number;
    circuitBreakerThreshold: number;
    enablePartialResults: boolean;
    enableProgressiveLoading: boolean;
  };

  // User experience settings
  ui: {
    showDetailedProgress: boolean;
    enableRealTimeUpdates: boolean;
    showPerformanceMetrics: boolean;
    enableNotifications: boolean;
  };
}

// Enhanced scan results with performance metrics
export interface EnhancedScanResult {
  scanId: string;
  targetUrl: string;
  status: 'running' | 'completed' | 'partial' | 'failed' | 'timeout';

  // Core results
  overallScore: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;

  // Enhanced metadata
  performance: {
    totalDuration: number;
    testExecutionTimes: Record<string, number>;
    timeouts: string[];
    retries: number;
    successRate: number;
    averageResponseTime: number;
  };

  // Reliability metrics
  reliability: {
    networkIssues: number;
    recoveredErrors: number;
    partialResults: boolean;
    dataCompleteness: number; // 0-100%
  };

  // Enhanced test results
  categories: {
    technical: CategoryResult;
    administrative: CategoryResult;
    organizational: CategoryResult;
    physical: CategoryResult;
  };

  // Detailed findings
  findings: {
    critical: Finding[];
    high: Finding[];
    medium: Finding[];
    low: Finding[];
  };

  timestamp: Date;
  executionLog: ExecutionLogEntry[];
}

export interface CategoryResult {
  category: string;
  score: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  executionTime: number;
  issues: Finding[];
}

export interface Finding {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  location: string;
  evidence: string;
  recommendation: string;
  remediationEffort: 'low' | 'medium' | 'high';
  hipaaSection: string;
  cweId?: number;
  owaspCategory?: string;
}

export interface ExecutionLogEntry {
  timestamp: Date;
  level: 'info' | 'warn' | 'error';
  phase: string;
  test?: string;
  message: string;
  duration?: number;
  details?: Record<string, string | number | boolean>;
}

// UI State Management
export interface ScanUIState {
  isScanning: boolean;
  progress: ScanProgress | null;
  results: EnhancedScanResult | null;
  error: ScanError | null;
  config: EnhancedScanConfig;

  // UI preferences
  preferences: {
    showAdvancedOptions: boolean;
    autoRefresh: boolean;
    notificationsEnabled: boolean;
    theme: 'light' | 'dark';
  };

  // Real-time updates
  realTime: {
    connected: boolean;
    lastUpdate: Date | null;
    updateInterval: number;
  };
}

// WebSocket message types for real-time updates
export interface WebSocketMessage {
  type: 'progress' | 'error' | 'complete' | 'partial' | 'heartbeat';
  scanId: string;
  timestamp: Date;
  data: ScanProgress | ScanError | EnhancedScanResult | Record<string, unknown>;
}

// Performance monitoring
export interface PerformanceMetrics {
  scanDuration: number;
  testExecutionTimes: Record<string, number>;
  timeouts: string[];
  retries: number;
  successRate: number;
  networkLatency: number;
  memoryUsage: number;
  cpuUsage: number;
}

// Circuit breaker state
export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failures: number;
  lastFailureTime: Date | null;
  successCount: number;
  nextAttemptTime: Date | null;
}

// Enhanced API responses
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: ScanError;
  metadata: {
    requestId: string;
    timestamp: Date;
    duration: number;
    retryCount: number;
  };
}

// Notification system
export interface Notification {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  persistent: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style: 'primary' | 'secondary' | 'danger';
}

// Export default configuration
export const DEFAULT_ENHANCED_CONFIG: EnhancedScanConfig = {
  targetUrl: '',
  protectedEndpoints: ['/admin', '/dashboard', '/account', '/api/users', '/api/patients'],
  publicEndpoints: ['/', '/about', '/contact', '/login'],
  pagesToScan: ['/', '/login', '/about', '/contact', '/dashboard'],

  timeouts: {
    sslAnalysis: 30000, // Increased from 10s
    zapScanning: 120000, // Increased from 60s
    totalScan: 300000, // Increased from 180s
    individualTest: 15000, // New: per-test timeout
  },

  reliability: {
    maxRetries: 3,
    retryDelay: 2000,
    circuitBreakerThreshold: 5,
    enablePartialResults: true,
    enableProgressiveLoading: true,
  },

  ui: {
    showDetailedProgress: true,
    enableRealTimeUpdates: true,
    showPerformanceMetrics: false,
    enableNotifications: true,
  },
};
