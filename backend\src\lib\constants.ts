/**
 * Collection of standardized error messages used across the backend.
 */
export const ERROR_MESSAGES = {
  KEYCLOAK_ID_NOT_FOUND: 'User Keycloak ID not found in token.',
  SCAN_URL_REQUIRED: 'Scan URL is required and must be a string.',
  SCAN_URL_INVALID_FORMAT: 'Scan URL must be a valid HTTP/HTTPS URL.',
  STANDARDS_REQUIRED: 'Standards must be a non-empty array of strings.',
  USER_NOT_FOUND: 'User not found.',
  SCAN_NOT_FOUND: 'Scan not found or user does not have access.',
  SCAN_CREATION_FAILED: 'Failed to create scan record.',
  SCAN_UPDATE_FAILED: 'Failed to update scan status.',
  SCAN_FAILED_INTERNAL: '<PERSON><PERSON> failed due to an internal server error.',
  FETCH_SCANS_FAILED: 'Failed to fetch scans.',
  FETCH_SCAN_FAILED: 'Failed to fetch scan details.',
  UNAUTHORIZED: 'Unauthorized.',
  INVALID_INPUT: 'Invalid input provided.',
  INVALID_SCAN_ID_FORMAT: 'Scan ID must be a valid UUID.',
};

/**
 * Defines the possible statuses for a compliance scan.
 * Used to track the lifecycle of a scan job.
 */
export const SCAN_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

/**
 * Represents the possible string literal values for a scan's status.
 * Derived from the SCAN_STATUS object to ensure type safety.
 */
export type ScanStatus = (typeof SCAN_STATUS)[keyof typeof SCAN_STATUS];

// Add other backend-wide constants here
