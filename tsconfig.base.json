{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true, // This will be overridden by individual projects if they emit
    "baseUrl": ".",
    "paths": {
      // You can define shared paths here if needed later
    }
  },
  "exclude": ["node_modules"]
}
