-- Create the keycloak_db database if it doesn't already exist.
-- The \gexec command executes the query generated by the SELECT statement.
SELECT 'CREATE DATABASE keycloak_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'keycloak_db')\gexec

-- Optional: Grant privileges. If Keycloak connects as POSTGRES_USER (complyuser),
-- which is typically a superuser, it already has rights to the new database.
-- If you were using a dedicated, non-superuser for Keycloak (e.g., keycloak_user),
-- you would uncomment and adapt the following lines:
-- GRANT ALL PRIVILEGES ON DATABASE keycloak_db TO your_keycloak_db_user;
