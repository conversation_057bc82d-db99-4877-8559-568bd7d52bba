# PowerShell script to configure Keycloak CORS settings
param(
    [string]$KeycloakUrl = "http://localhost:8080",
    [string]$AdminUser = "admin",
    [string]$AdminPassword = "admin",
    [string]$Realm = "complychecker",
    [string]$FrontendOrigin = "http://localhost:3000"
)

Write-Host "🔐 Configuring Keycloak CORS settings..." -ForegroundColor Blue

# Wait for Keycloak to be ready
Write-Host "⏳ Waiting for Keycloak to be ready..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0
do {
    $attempt++
    try {
        $response = Invoke-WebRequest -Uri "$KeycloakUrl/auth/health/ready" -Method GET -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Keycloak is ready!" -ForegroundColor Green
            break
        }
    }
    catch {
        Write-Host "⏳ Attempt $attempt/$maxAttempts - Keycloak not ready yet..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Host "❌ Keycloak failed to start within timeout" -ForegroundColor Red
    exit 1
}

# Get admin token
Write-Host "🔑 Getting admin token..." -ForegroundColor Blue
try {
    $tokenResponse = Invoke-RestMethod -Uri "$KeycloakUrl/auth/realms/master/protocol/openid-connect/token" -Method POST -ContentType "application/x-www-form-urlencoded" -Body @{
        grant_type = "password"
        client_id = "admin-cli"
        username = $AdminUser
        password = $AdminPassword
    }
    $adminToken = $tokenResponse.access_token
    Write-Host "✅ Admin token obtained" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to get admin token: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check if realm exists
Write-Host "🔍 Checking if realm '$Realm' exists..." -ForegroundColor Blue
try {
    $headers = @{
        "Authorization" = "Bearer $adminToken"
        "Content-Type" = "application/json"
    }
    
    $realmResponse = Invoke-RestMethod -Uri "$KeycloakUrl/auth/admin/realms/$Realm" -Method GET -Headers $headers
    Write-Host "✅ Realm '$Realm' found" -ForegroundColor Green
}
catch {
    Write-Host "❌ Realm '$Realm' not found: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Please create the realm manually or check the realm name" -ForegroundColor Yellow
    exit 1
}

# Update frontend client CORS settings
Write-Host "🌐 Updating frontend client CORS settings..." -ForegroundColor Blue
try {
    # Get the frontend client
    $clientsResponse = Invoke-RestMethod -Uri "$KeycloakUrl/auth/admin/realms/$Realm/clients?clientId=complychecker-frontend" -Method GET -Headers $headers
    
    if ($clientsResponse.Count -eq 0) {
        Write-Host "❌ Frontend client complychecker-frontend not found" -ForegroundColor Red
        exit 1
    }
    
    $frontendClient = $clientsResponse[0]
    $clientId = $frontendClient.id
    
    # Update client with CORS settings
    $frontendClient.webOrigins = @($FrontendOrigin)
    $frontendClient.redirectUris = @("$FrontendOrigin/*")
    
    $clientJson = $frontendClient | ConvertTo-Json -Depth 10
    
    Invoke-RestMethod -Uri "$KeycloakUrl/auth/admin/realms/$Realm/clients/$clientId" -Method PUT -Headers $headers -Body $clientJson
    Write-Host "✅ Frontend client CORS settings updated" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to update frontend client: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Keycloak CORS configuration completed successfully!" -ForegroundColor Green
Write-Host "📝 Configuration applied:" -ForegroundColor Cyan
Write-Host "   - Realm: $Realm" -ForegroundColor White
Write-Host "   - Frontend Origin: $FrontendOrigin" -ForegroundColor White
Write-Host "   - Client: complychecker-frontend" -ForegroundColor White
