// Compliance Standards
export interface ComplianceStandard {
  id: string;
  label: string;
}

export const COMPLIANCE_STANDARDS: ComplianceStandard[] = [
  { id: 'hipaa', label: 'HIPAA' },
  { id: 'gdpr', label: 'GDPR' },
  { id: 'ada', label: 'ADA' },
  { id: 'wcag', label: 'WCAG' },
  // Add more standards here as needed
];

// UI Messages
export const MESSAGES = {
  ERROR_NOT_LOGGED_IN: 'You must be logged in to submit a scan.',
  ERROR_INVALID_URL_EMPTY: 'Please enter a valid URL.',
  ERROR_INVALID_URL_FORMAT: 'Invalid URL format. Please include http:// or https://',
  ERROR_NO_STANDARD_SELECTED: 'Please select at least one compliance standard.',
  ERROR_SUBMISSION_FAILED: 'Failed to submit scan. Please try again.',
  SUCCESS_SCAN_SUBMITTED: '<PERSON>an submitted successfully! You will be redirected shortly.',
};

// UI Text
export const TEXT = {
  SUBMITTING_BUTTON: 'Submitting...',
  SUBMIT_SCAN_BUTTON: 'Submit Scan',
};

// Timeouts
export const TIMEOUTS = {
  REDIRECT_AFTER_SUBMIT: 3000, // milliseconds
};

// Default values or common strings
export const DEFAULT_STRINGS = {
  NOT_APPLICABLE: 'N/A',
};
