/**
 * Responsive Design System for Compliance Dashboard
 * Mobile-first approach with progressive enhancement
 */

/* ===== BREAKPOINT SYSTEM ===== */
:root {
  /* Breakpoint values */
  --breakpoint-sm: 640px; /* Small devices (landscape phones) */
  --breakpoint-md: 768px; /* Medium devices (tablets) */
  --breakpoint-lg: 1024px; /* Large devices (laptops) */
  --breakpoint-xl: 1280px; /* Extra large devices (desktops) */
  --breakpoint-2xl: 1536px; /* 2X large devices (large desktops) */

  /* Container max-widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ===== RESPONSIVE CONTAINERS ===== */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: var(--container-sm);
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: var(--container-md);
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: var(--container-2xl);
  }
}

/* ===== RESPONSIVE GRID SYSTEM ===== */
.grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

/* Small devices and up */
@media (min-width: 640px) {
  .grid-responsive {
    gap: 1.5rem;
  }

  .grid-responsive.grid-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-responsive.grid-sm-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Medium devices and up */
@media (min-width: 768px) {
  .grid-responsive {
    gap: 2rem;
  }

  .grid-responsive.grid-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-responsive.grid-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-responsive.grid-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Large devices and up */
@media (min-width: 1024px) {
  .grid-responsive.grid-lg-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-responsive.grid-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-responsive.grid-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-responsive.grid-lg-5 {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
.text-responsive-h1 {
  font-size: 1.875rem; /* 30px */
  line-height: 2.25rem; /* 36px */
}

@media (min-width: 640px) {
  .text-responsive-h1 {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
  }
}

@media (min-width: 768px) {
  .text-responsive-h1 {
    font-size: 3rem; /* 48px */
    line-height: 1;
  }
}

.text-responsive-h2 {
  font-size: 1.5rem; /* 24px */
  line-height: 2rem; /* 32px */
}

@media (min-width: 640px) {
  .text-responsive-h2 {
    font-size: 1.875rem; /* 30px */
    line-height: 2.25rem; /* 36px */
  }
}

@media (min-width: 768px) {
  .text-responsive-h2 {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
  }
}

.text-responsive-h3 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.75rem; /* 28px */
}

@media (min-width: 640px) {
  .text-responsive-h3 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
  }
}

/* ===== RESPONSIVE SPACING ===== */
.spacing-responsive {
  padding: 1rem;
}

@media (min-width: 640px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: 3rem;
  }
}

/* ===== RESPONSIVE CARDS ===== */
.card-responsive {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 1rem;
  transition: box-shadow 0.2s ease-in-out;
}

@media (min-width: 640px) {
  .card-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .card-responsive {
    padding: 2rem;
  }
}

.card-responsive:hover {
  box-shadow: var(--shadow-md);
}

/* ===== RESPONSIVE BUTTONS ===== */
.btn-responsive {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
  min-height: 2.5rem;
  width: 100%;
}

@media (min-width: 640px) {
  .btn-responsive {
    width: auto;
    padding: 0.75rem 1.5rem;
  }
}

@media (min-width: 768px) {
  .btn-responsive {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

/* ===== RESPONSIVE NAVIGATION ===== */
.nav-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .nav-responsive {
    flex-direction: row;
    gap: 1rem;
  }
}

/* ===== RESPONSIVE TABLES ===== */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 600px;
  width: 100%;
}

@media (min-width: 768px) {
  .table-responsive table {
    min-width: 100%;
  }
}

/* ===== RESPONSIVE MODALS ===== */
.modal-responsive {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content-responsive {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 28rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

@media (min-width: 640px) {
  .modal-content-responsive {
    max-width: 32rem;
  }
}

@media (min-width: 768px) {
  .modal-content-responsive {
    max-width: 42rem;
  }
}

@media (min-width: 1024px) {
  .modal-content-responsive {
    max-width: 56rem;
  }
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Hide on mobile */
.hidden-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hidden-mobile {
    display: block;
  }
}

/* Hide on desktop */
.hidden-desktop {
  display: block;
}

@media (min-width: 768px) {
  .hidden-desktop {
    display: none;
  }
}

/* Show only on mobile */
.mobile-only {
  display: block;
}

@media (min-width: 640px) {
  .mobile-only {
    display: none;
  }
}

/* Show only on tablet */
.tablet-only {
  display: none;
}

@media (min-width: 640px) and (max-width: 1023px) {
  .tablet-only {
    display: block;
  }
}

/* Show only on desktop */
.desktop-only {
  display: none;
}

@media (min-width: 1024px) {
  .desktop-only {
    display: block;
  }
}

/* ===== RESPONSIVE DASHBOARD SPECIFIC ===== */

/* Dashboard header */
.dashboard-header-responsive {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

@media (min-width: 768px) {
  .dashboard-header-responsive {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/* Dashboard cards grid */
.dashboard-grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .dashboard-grid-responsive {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid-responsive {
    gap: 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Module cards */
.module-card-responsive {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .module-card-responsive {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

/* Metrics display */
.metrics-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .metrics-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .metrics-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ===== TOUCH TARGETS ===== */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

@media (pointer: coarse) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}
