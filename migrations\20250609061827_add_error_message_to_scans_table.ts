import type { Knex } from 'knex';

const TABLE_NAME = 'scans';
const COLUMN_NAME = 'error_message';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, (table) => {
    table.text(COLUMN_NAME).nullable(); // Add the new column, allowing null values
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, (table) => {
    table.dropColumn(COLUMN_NAME); // Remove the column if rolling back
  });
}
