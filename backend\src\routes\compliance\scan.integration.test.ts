import request from 'supertest';
import app from '../../index'; // Express app instance
import { Response, NextFunction, Request as ExpressRequest } from 'express';
import db from '../../lib/db'; // Knex instance
import { Knex } from 'knex';

// Mock user ID for testing
const MOCK_USER_KEYCLOAK_ID = 'test-user-keycloak-id';

// Mock keycloak-connect
// This needs to be done before any modules that import keycloak-connect (like our app) are imported at the top level if not already.
// However, since app is imported above, this mock will apply to its Keycloak instance upon initialization.

// Simplified request type for mocking Keycloak authentication
interface MockAuthRequest extends ExpressRequest {
  kauth?: {
    grant?: {
      access_token?: {
        content: {
          sub: string;
          email?: string;
          email_verified?: boolean;
          name?: string;
          preferred_username?: string;
          given_name?: string;
          family_name?: string;
        };
      };
    };
  };
}
jest.mock('keycloak-connect', () => {
  const mKeycloak = {
    protect: jest.fn(() => (req: MockAuthRequest, _res: Partial<Response>, next: NextFunction) => {
      req.kauth = {
        grant: {
          access_token: {
            content: {
              sub: MOCK_USER_KEYCLOAK_ID,
              email_verified: true,
              name: 'Test User',
              preferred_username: 'testuser',
              given_name: 'Test',
              family_name: 'User',
              email: '<EMAIL>',
            },
          },
        },
      };
      return next();
    }),
    middleware: jest.fn(
      () => (_req: Partial<ExpressRequest>, _res: Partial<Response>, next: NextFunction) => next(),
    ),
    // Mock other Keycloak methods if needed by the application during startup or routing
  };
  return jest.fn(() => mKeycloak); // Mock the constructor
});

describe('Scan API Endpoints', () => {
  let knexInstance: Knex;

  beforeAll(async () => {
    knexInstance = db; // Use the imported db instance
    // Ensure migrations are current before tests run
    await knexInstance.migrate.latest();
  });

  afterEach(async () => {
    // Clean up tables after each test to ensure isolation
    // Order matters due to foreign key constraints if any are added later for findings
    await knexInstance('compliance_findings').del();
    await knexInstance('scans').del();
  });

  afterAll(async () => {
    // Destroy the database connection after all tests
    await knexInstance.destroy();
  });

  describe('POST /api/v1/compliance/scans', () => {
    it('should create a new scan with valid data and return 201', async () => {
      const scanData = {
        url: 'https://example.com',
        standards: ['gdpr', 'hipaa'],
      };

      const response = await request(app).post('/api/v1/compliance/scans').send(scanData);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('url', scanData.url);
      expect(response.body).toHaveProperty('status', 'PENDING'); // Or 'QUEUED' depending on initial status
      expect(response.body).toHaveProperty('user_keycloak_id', MOCK_USER_KEYCLOAK_ID);
      expect(response.body).toHaveProperty('standards');
      expect(response.body.standards).toEqual(expect.arrayContaining(scanData.standards));
      expect(response.body).toHaveProperty('findings'); // Initially empty
      expect(response.body.findings).toEqual([]);

      // Verify the scan was saved to the database
      const dbScan = await knexInstance('scans').where({ id: response.body.id }).first();
      expect(dbScan).toBeDefined();
      expect(dbScan.url).toBe(scanData.url);
      expect(dbScan.user_keycloak_id).toBe(MOCK_USER_KEYCLOAK_ID);
    });

    it('should return 400 for invalid URL', async () => {
      const scanData = {
        url: 'not-a-valid-url',
        standards: ['gdpr'],
      };
      const response = await request(app).post('/api/v1/compliance/scans').send(scanData);
      expect(response.status).toBe(400);
    });

    it('should return 400 for empty standards array', async () => {
      const scanData = {
        url: 'https://example.com',
        standards: [],
      };
      const response = await request(app).post('/api/v1/compliance/scans').send(scanData);
      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/v1/compliance/scans', () => {
    it.todo('should return a list of scans for the authenticated user');
  });

  describe('GET /api/v1/compliance/scans/:id', () => {
    it.todo('should return a specific scan if found and belongs to the user');
    it.todo('should return 404 if scan not found');
    it.todo(
      'should return 404 if scan belongs to another user (effectively not found for this user)',
    );
  });
});
