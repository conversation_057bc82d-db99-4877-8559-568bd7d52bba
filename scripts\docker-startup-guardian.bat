@echo off
REM Docker Startup Guardian - Prevents WSL2 network issues
REM This script should be run before starting Docker Desktop

echo 🛡️ Docker Startup Guardian
echo =========================
echo Ensuring WSL2 network stability before Docker startup...

REM Check if WSL is responsive
wsl --list --quiet >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ WSL not responsive, restarting...
    net stop LxssManager >nul 2>&1
    timeout /t 3 /nobreak >nul
    net start LxssManager >nul 2>&1
    timeout /t 5 /nobreak >nul
)

REM Check network share accessibility
dir "\\wsl$" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ WSL network share not accessible, fixing...
    net stop lanmanserver >nul 2>&1
    net start lanmanserver >nul 2>&1
    timeout /t 3 /nobreak >nul
)

REM Ensure Docker WSL distributions are clean
wsl --list --quiet | findstr "docker-desktop" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker WSL distributions present
) else (
    echo ℹ️ Docker WSL distributions will be created on startup
)

echo ✅ WSL2 environment ready for Docker Desktop
echo You can now start Docker Desktop safely.

REM Optional: Auto-start Docker Desktop
set /p start_docker="Start Docker Desktop now? (y/N): "
if /i "%start_docker%"=="y" (
    echo Starting Docker Desktop...
    if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
        start "" "%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
    ) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
        start "" "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
    ) else (
        echo Docker Desktop not found in standard locations
    )
)

pause
